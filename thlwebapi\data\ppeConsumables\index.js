"use strict";

const utils = require("../utils");
const config = require("../../config");
const sql = require("mssql");
const logger = require("../../utils/logger");
const { forEach } = require("lodash");
const { sendEmail } = require("../../utils/email");

const getOrderRequests = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");
    const { recordset } = await pool.request().query(sqlQueries.getAllRequest);
    return recordset;
  } catch (error) {
    console.log(error);
    return error.message;
  }
};

const getProducts = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");
    // console.log("company", company, type_id);
    const { recordset } = await pool
      .request()
      // .input("company", sql.VarChar, company)
      // .input("type_id", sql.Int, type_id)
      // .input("fetch_all", sql.Bit, company)
      .query(sqlQueries.getAllProducts);
    return recordset;
  } catch (error) {
    console.log(error);
    return error.message;
  }
};

const getRequestDataById = async (id) => {
  try {
    console.log(id);
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");
    const { recordset } = await pool
      .request()
      .input("ProductId", sql.Int, id)
      .query(sqlQueries.getSingleRequestByid);
    console.log("recordset----------------\n", recordset);
    return recordset;
  } catch (error) {
    console.log(error);
    return error.message;
  }
};

const getProductRequestDataById = async (id) => {
  try {
    console.log(id);
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");
    const { recordset } = await pool
      .request()
      .input("ProductId", sql.Int, id)
      .query(sqlQueries.getProductRequestByid);
    console.log("recordset----------------\n", recordset);
    return recordset;
  } catch (error) {
    console.log(error);
    return error.message;
  }
};
const getProductType = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");
    const { recordset } = await pool.request().query(sqlQueries.getProductType);
    return recordset;
  } catch (error) {
    console.log(error);
    return error.message;
  }
};

const addProduct = async (productData) => {
  try {
      if (productData.sizesWithOrder && Array.isArray(productData.sizesWithOrder)){
        const uniqueSize = [];
        const seenSize = new Set();

        productData.sizesWithOrder.forEach(size => {
          const cleanName = size.name.trim().toLowerCase();
          if(cleanName && !seenSize.has(cleanName)){
            uniqueSize.push({
              name: size.name.trim(),
              displayOrder: size.displayOrder
            });
          }
        });
        
        productData.sizesWithOrder = uniqueSize;
        sizesWithOrderJSON = JSON.stringify(uniqueSize);
      }

      // if (productData.NewSizeNames) {
      //   const sizeArray = productData.NewSizeNames.split(",")
      //     .map((s) => s.trim())
      //     .filter((s) => s)
      //     .filter(
      //       (size, index, arr) =>
      //         arr.findIndex((s) => s.toLowerCase() === size.toLowerCase()) ===
      //         index
      //     );
      // }

    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");
    console.log("productData", productData);

    let sizesWithOrderJSON = null;
    if (
      productData.SizesWithOrder &&
      Array.isArray(productData.SizesWithOrder)
    ) {
      sizesWithOrderJSON = JSON.stringify(productData.SizesWithOrder);
      // console.log("SizesWithOrder JSON:", sizesWithOrderJSON);
    }

    const result = await pool
      .request()
      // .input("RequestItemID", sql.Int, productData.RequestItemID)
      .input("ProductName", sql.VarChar, productData.ProductName)
      .input("ProductCode", sql.VarChar, productData.ProductCode)
      .input("TypeID", sql.Int, productData.TypeID)
      .input("IsSizeRequired", sql.Bit, productData.IsSizeRequired)
      .input("SizeLabel", sql.VarChar, productData.SizeLabel)
      .input("PackageQuantity", sql.VarChar, productData.PackageQuantity)
      .input("Comments", sql.VarChar, productData.Comments)
      .input("SizeIDs", sql.VarChar, productData.SizeIDs) // Comma-separated string of Size IDs
      .input("namePrintable", sql.Bit, productData.NamePrintable) // Comma-separated string of Size IDs
      .input("NewSizeNames", sql.VarChar, productData.NewSizeNames) // Comma-separated string of Size IDs
      .input("SizesWithOrderJSON", sql.NVarChar(sql.MAX), sizesWithOrderJSON) // JSON string with ordered sizes
      .query(sqlQueries.addProduct);

    return result.recordset[0]; // Assuming the stored procedure returns the new product ID
  } catch (error) {
    console.error("Error adding product:", error);
    return { error: error.message };
  }
};
//#region new
const createOrderRequest = async (orderData) => {
  try {
    // Input validation
    if (!orderData || !orderData.items || !Array.isArray(orderData.items)) {
      throw new Error("Order data with items array is required");
    }

    if (orderData.items.length === 0) {
      throw new Error("At least one item is required");
    }

    const requiredFields = ["SubmitterUserID", "TargetSiteID", "RequiredDate"];
    for (const field of requiredFields) {
      if (!orderData[field]) {
        // && !orderData[field.toLowerCase()]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    orderData.items.forEach((item, index) => {
      if (!item.ProductID || !item.Quantity) {
        throw new Error(
          `Item ${index + 1}: ProductID and Quantity are required`
        );
      }
    });

    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");
    console.log("orderData", orderData);

    const result = await pool
      .request()
      .input(
        "SubmitterUserID",
        sql.Int,
        orderData.SubmitterUserID || orderData.user_id
      )
      .input(
        "TargetSiteID",
        sql.Int,
        orderData.TargetSiteID || orderData.site_id
      )
      .input("InitialStatusActionID", sql.Int, orderData.Status_id)
      .input(
        "RequiredDate",
        sql.Date,
        orderData.RequiredDate || orderData.required_date
      )
      .input("ItemsJSON", sql.VarChar, JSON.stringify(orderData.items))
      .input("submitterUserEmail", sql.VarChar, orderData.username || "system")
      .query(sqlQueries.createOrderRequest);

    logger.info({
      username: orderData.username || "system",
      type: "success",
      description: `Multi - Order request created successfully `, //for product ID ${orderData.ProductID || orderData.product_id}`,
      item_id: result.recordset[0]?.NewRequestID || null,
      module_id: 3,
    });

    if (orderData.Status_id === 1) {
      // Email notification logic
      let tableRows = orderData.items
        .map(
          (item) => `
          <tr style="font-size:14px;">
            <td style="border: 1px solid #000; padding: 8px;">${
              item.ProductName || "-"
            }</td>
            <td style="border: 1px solid #000; padding: 8px;">${
              item.Size || "-"
            }</td>
            <td style="border: 1px solid #000; padding: 8px;">${
              item.Quantity
            }</td>
            <td style="border: 1px solid #000; padding: 8px;">${
              item.NameForPrinting || "-"
            }</td>
            <td style="border: 1px solid #000; padding: 8px;">${
              item.Comments || "-"
            }</td>
          </tr>
        `
        )
        .join("");

      let table = `
     <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width:100%; font-size:14px;">
      <thead>
        <tr>
          <th style="border: 1px solid #000; padding: 8px; text-align: left;">Product</th>
          <th style="border: 1px solid #000; padding: 8px; text-align: left;">Size</th>
          <th style="border: 1px solid #000; padding: 8px; text-align: left;">Quantity</th>
          <th style="border: 1px solid #000; padding: 8px; text-align: left;">Name For Printing</th>
          <th style="border: 1px solid #000; padding: 8px; text-align: left;">Comments</th>
        </tr>
      </thead>
      <tbody>
        ${tableRows}
      </tbody>
    </table>
  `;

      let recipientEmails;
      let ccEmail = null;
      let paragraph;
      let emailType;
      let comment;
      const emailSqlQueries = await utils.loadSqlQueries("suppliers");

      recipientEmails = orderData.username || orderData.orignatorEmail;
      ccEmail = "";
      // ccEmail = "<EMAIL>;<EMAIL>";
      emailType = "submittedPPERequestForISS";
      paragraph = `
        <p style="font-size:14px;">
          There has been a new PPE/Consumable Request, ID: ${result.recordset[0]?.NewRequestID} that has been set up for Approval.
        </p>
        <p style="font-size:14px;">
          Please log on to the portal to review the details:
        </p>
        ${table}`;
      // comment = productsData.comment //TODO paste the summary of table

      console.log("recipientEmails", recipientEmails);
      console.log("ccEmail", ccEmail);
      console.log("emailType", emailType);
      console.log("paragraph", paragraph);

      const placeholders = {
        paragraph: paragraph || "PPE/Consumable Request",
        comment: "",
      };
      console.log("placeholders", placeholders);

      let companyKey = "iss";

      // Send email notification
      if (recipientEmails && emailType) {
        if (ccEmail === recipientEmails) {
          ccEmail = null;
        }
        await sendEmail({
          placeholders,
          emailType,
          recipientEmails,
          ccEmail,
          companyKey,
          request_no: result.recordset[0]?.NewRequestID,
        });
      }
    }

    return result.recordset[0]; // Returns { NewRequestID, NewRequestItemID }
  } catch (error) {
    logger.error({
      username: orderData.username || "system",
      type: "error",
      description: `Failed to create order request: ${error.message}`,
      item_id: null,
      module_id: 3,
    });
    console.error("Error creating order request:", error);
    return { error: error.message };
  }
};
//#endregion new
//#region Update
async function updateOrderRequest(req) {
  try {
    console.log("reached into the update function 10", req);

    // Convert items array into JSON string for SQL
    const itemsJson = JSON.stringify(req.items);
    console.log("reached into the update function 11", itemsJson);

    // Connect to DB (assumes pool is already configured globally)
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");

    // Execute SQL (stored procedure or inline query)
    const result = await pool
      .request()
      .input("RequestID", sql.Int, req.requestId)
      .input("SubmitterUserID", sql.Int, req.submitterUserId)
      .input("TargetSiteID", sql.Int, req.targetSiteId)
      .input("action_id", sql.Int, req.action_id)
      .input("RequiredDate", sql.Date, req.requiredDate)
      .input(
        "commentOnUpdatingRequest",
        sql.VarChar,
        req.comment || "Request updated by user."
      )
      .input("ItemsJSON", sql.NVarChar(sql.MAX), itemsJson)
      .query(sqlQueries.updateByRequestID);

      
// console.log("Full SQL result:", result);
// console.log("All recordsets:", result.recordsets);
    if (req.action_id !== 5) {
      // Email notification logic
      let tableRows = req.items
        .map(
          (item) => `
    <tr style="font-size:14px;">
      <td>${item.ProductName}</td>
      <td>${item.Size || "-"}</td>
      <td>${item.Quantity}</td>
      <td>${item.NameForPrinting || "-"}</td>
      <td>${item.Comments || "-"}</td>
    </tr>
  `
        )
        .join("");

      let table = `
  <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width:100%; font-size:14px;">
    <thead>
      <tr>
        <th>Product</th>
        <th>Size</th>
        <th>Quantity</th>
        <th>Name For Printing</th>
        <th>Comments</th>
      </tr>
    </thead>
    <tbody>
      ${tableRows}
    </tbody>
  </table>
`;

      let recipientEmails;
      let ccEmail = null;
      let paragraph;
      let emailType;
      let comment;
      const emailSqlQueries = await utils.loadSqlQueries("suppliers");

      // let actualActionId = orderData.actionId;
      // console.log("actual action id", actualActionId);
      let emailBody;
      switch (req.action_id) {
        case 1:
          emailType = "submittedPPERequestForISS";
          emailBody = "set up for Approval";
          break;
        case 2:
          emailType = "ApprovedPPERequestForISS";
          emailBody = "Approved";
          break;
        case 3:
          emailType = "RejectedPPERequestForISS";
          emailBody = "Rejected";
          break;
        case 4:
          emailType = "OrderedPPERequestForISS";
          emailBody = "Marked Ordered";
          break;
      }
      let recipientEmailsData;


      recipientEmails = req.orignatorEmail// "<EMAIL>";
      ccEmail = "";
      // ccEmail = "<EMAIL>;<EMAIL>";
      paragraph = `<p>Request ID: ${req.requestId} has been ${emailBody}.</p><p>Please log on to the portal, review the details</p>${table}`;

      console.log("recipientEmails", recipientEmails);
      console.log("ccEmail", ccEmail);
      console.log("emailType", emailType);
      console.log("paragraph", paragraph);

      const placeholders = {
        paragraph: paragraph || "PPE/Consumable Request",
        comment: "",
      };
      console.log("placeholders", placeholders);

      let companyKey = "iss";

      // Send email notification
      if (recipientEmails && emailType) {
        if (ccEmail === recipientEmails) {
          ccEmail = null;
        }
        await sendEmail({
          placeholders,
          emailType,
          recipientEmails,
          ccEmail,
          companyKey,
          request_no: req.requestId,
        });
      }
    }
    return result;
  } catch (err) {
    console.error("Error updating order request:", err);
    return err;
  }
}
//#endregion Update

//#region Update item
const updateRequestItem = async (rowdata) => {
  // rowdata.cancelledReasonapi
  try {
    console.log("reached into the update function", rowdata);

    // Connect to DB (assumes pool is already configured globally)
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");

    // Execute SQL (stored procedure or inline query)
    const result = await pool
      .request()
      .input("RequestItemID", sql.Int, rowdata.RequestItemID)
      .input(
        "commentOnUpdatingRequest",
        sql.VarChar,
        rowdata.commentOnUpdatingRequest
      )
      // .input("TargetSiteID", sql.Int, rowdata.targetSiteId)
      .input("action_id", sql.Int, rowdata.action_id)
      .input("SubmitterUserID", sql.Int, rowdata.SubmitterUserID)
      .input("SubmitterEmail", sql.VarChar, rowdata.SubmitterEmail)
      // .input("orignatorEmail", sql.VarChar, rowdata.orignatorEmail)
      // .input("ItemsJSON", sql.NVarChar(sql.MAX), itemsJson)
      .query(sqlQueries.updateRequestItem);

    if (rowdata.action_id !== 5) {
      // Email notification logic
      let tableRows = `
    <tr style="font-size:14px;">
      <td>${rowdata.ProductName}</td>
      <td>${rowdata.Size || "-"}</td>
      <td>${rowdata.Quantity}</td>
      <td>${rowdata.NameForPrinting || "-"}</td>
      <td>${rowdata.Comments || "-"}</td>
    </tr>
  `;

      let table = `
        <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width:100%; font-size:14px;">
          <thead>
            <tr>
              <th>Product</th>
              <th>Size</th>
              <th>Quantity</th>
              <th>Name For Printing</th>
              <th>Comments</th>
            </tr>
          </thead>
          <tbody>
            ${tableRows}
          </tbody>
        </table>
      `;

      let recipientEmails;
      let ccEmail = null;
      let paragraph;
      let emailType;
      let comment;
      const emailSqlQueries = await utils.loadSqlQueries("suppliers");

      // let actualActionId = orderData.actionId;
      // console.log("actual action id", actualActionId);
      let emailBody;
      switch (rowdata.action_id) {
        case 1:
          emailType = "submittedPPERequestForISS";
          emailBody = "set up for Approval";
          break;
        case 2:
          emailType = "ApprovedPPERequestForISS";
          emailBody = "Approved";
          break;
        case 3:
          emailType = "RejectedPPERequestForISS";
          emailBody = "Rejected";
          break;
        case 4:
          emailType = "OrderedPPERequestForISS";
          emailBody = "Marked Ordered";
          break;
      }
      let recipientEmailsData;


      recipientEmails = rowdata.orignatorEmail; // "<EMAIL>";
      ccEmail = "";
      // ccEmail = "<EMAIL>;<EMAIL>";
      paragraph = `
      <p>Request ID: ${rowdata.request_no} - ${rowdata.item_number} has been ${emailBody}.</p>
        <p>
    ${
      rowdata.commentOnUpdatingRequest
        ? `Comments: ${rowdata.commentOnUpdatingRequest}`
        : ""
    }
    ${
      rowdata.action_id === 3 && rowdata.cancelledReasonapi
        ? `Comments: ${rowdata.cancelledReasonapi}`
        : ""
    }
  </p>
      <p>Please log on to the portal, review the details</p>${table}`;

      console.log("recipientEmails", recipientEmails);
      console.log("ccEmail", ccEmail);
      console.log("emailType", emailType);
      console.log("paragraph", paragraph);

      const placeholders = {
        paragraph: paragraph || "PPE/Consumable Request",
        comment: "",
      };
      console.log("placeholders", placeholders);

      let companyKey = "iss";

      // Send email notification
      if (recipientEmails && emailType) {
        if (ccEmail === recipientEmails) {
          ccEmail = null;
        }
        await sendEmail({
          placeholders,
          emailType,
          recipientEmails,
          ccEmail,
          companyKey,
          request_no: `${rowdata.request_no} - ${rowdata.item_number}`,
        });
      }
    }
    return result;
  } catch (err) {
    console.error("Error updating order request:", err);
    return err;
  }
};

const getSites = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");
    const { recordset } = await pool.request().query(sqlQueries.getSites);
    return recordset;
  } catch (error) {
    console.error("Error getting sites:", error);
    return { error: error.message };
  }
};

async function updateProductRequest({
  id,
  ProductName,
  TypeID,
  IsSizeRequired,
  NamePrintable,
  NewSizeNames,
  orderedSizesData,
}) {
  try {
    console.log("reached into the update function for product", id);

  // Clean and deduplicate orderedSizesData
    if (orderedSizesData && Array.isArray(orderedSizesData)) {
      const uniqueSizes = [];
      const seenSizes = new Set();
      
      orderedSizesData.forEach(size => {
        const cleanName = size.name.trim().toLowerCase();
        if (cleanName && !seenSizes.has(cleanName)) {
          seenSizes.add(cleanName);
          uniqueSizes.push({
            name: size.name.trim(),
            displayOrder: size.displayOrder
          });
        }
      });
      
      orderedSizesData = uniqueSizes;
    }

    // Clean and deduplicate NewSizeNames
    if (NewSizeNames) {
      const sizeArray = NewSizeNames.split(",")
        .map((s) => s.trim())
        .filter((s) => s)
        .filter(
          (size, index, arr) =>
            arr.findIndex((s) => s.toLowerCase() === size.toLowerCase()) ===
            index
        );
      NewSizeNames = sizeArray.join(",");
    }

    // Convert items array into JSON string for SQL
    const orderedSizesDataJson = JSON.stringify(orderedSizesData);
    
    console.log("Cleaned NewSizeNames:", NewSizeNames);
    console.log("Cleaned orderedSizesData:", orderedSizesDataJson);
    // Convert items array into JSON string for SQL
    // const orderedSizesDataJson = JSON.stringify(orderedSizesData);
    // Connect to DB (assumes pool is already configured globally)
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");
    console.log(
      "id,productname,typeid, issize required, name printable, new sizes names,ordered size data json",
      parseInt(id),
      TypeID,
      IsSizeRequired,
      NamePrintable,
      NewSizeNames,
      orderedSizesDataJson
    );
    const result = await pool
      .request()
      .input("ProductID", sql.Int, parseInt(id))
      .input("ProductName", sql.VarChar, ProductName)
      .input("TypeID", sql.Int, TypeID)
      .input("IsSizeRequired", sql.Bit, IsSizeRequired)
      .input("NamePrintable", sql.Bit, NamePrintable)
      .input("NewSizeNames", sql.VarChar, NewSizeNames)
      .input("SizesWithOrderJSON", sql.NVarChar(sql.MAX), orderedSizesDataJson)
      .query(sqlQueries.updateProductRequest);
    console.log("result", result.recordset[0]);
    return result.recordset;
  } catch (err) {
    console.error("Error updating product request:", err);
    return { error: err.message };
  }
}

async function deleteProduct(id) {
  try {
    if (isNaN(id)) return { success: false, reason: "invalid_id" };

    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");
    console.log("id of product to delete", id);

    const { recordset } = await pool
      .request()
      .input("ProductID", sql.Int, parseInt(id))
      .query(sqlQueries.deleteProductCheck);
    // console.log("recordset", recordset);
    console.log("recordset", recordset[0].CanDelete);

    if (!recordset[0].CanDelete) {
      return { success: false, reason: "cannot delete" };
    }

    const result = await pool
      .request()
      .input("ProductID", sql.Int, parseInt(id))
      .query(sqlQueries.deleteProduct);

    return { success: result.rowsAffected[0] > 0 };
  } catch (err) {
    console.error("Error deleting product:", err);
    return { success: false, reason: "error" };
  }
}
async function updateHiddenState(data) {
  try {
    // if (isNaN(data)) return { success: false, reason: "invalid_id" };
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");
    console.log("data of product to be hidden", data);

    
    const { recordset } = await pool
      .request()
      .input("ProductID", sql.Int, parseInt(data.productId))
      .query(sqlQueries.deleteProductCheck);
    // console.log("recordset", recordset);
    console.log("recordset", recordset[0].CanDelete);

    if (!recordset[0].CanDelete) {
      return { success: false, reason: "cannot delete" };
    }


    const result = await pool
      .request()
      .input("ProductID", sql.Int, parseInt(data.productId))
      .input("MarkHidden", sql.Int, parseInt(data.markHidden))
      .query(sqlQueries.updateHiddenState);
    // console.log("result", result);

    return { success: result.rowsAffected[0] > 0 };
  } catch (err) {
    console.error("Error in hiding product:", err);
    return { success: false, reason: "error" };
  }
}

module.exports = {
  getOrderRequests,
  getProducts,
  addProduct,
  getSites,
  createOrderRequest,
  updateOrderRequest,
  updateRequestItem,
  getRequestDataById,
  getProductRequestDataById,
  updateProductRequest,
  getProductType,
  deleteProduct,
  updateHiddenState,
};

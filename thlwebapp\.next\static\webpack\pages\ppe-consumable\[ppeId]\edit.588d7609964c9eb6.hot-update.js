"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable/[ppeId]/edit",{

/***/ "./components/PpeConsumable.js":
/*!*************************************!*\
  !*** ./components/PpeConsumable.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fortawesome/free-regular-svg-icons */ \"./node_modules/@fortawesome/free-regular-svg-icons/index.mjs\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lodash/forEach */ \"./node_modules/lodash/forEach.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(lodash_forEach__WEBPACK_IMPORTED_MODULE_12__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    valueContainer: (provided)=>({\n            ...provided,\n            height: \"28px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: ()=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst emptyItem = {\n    product: null,\n    size: null,\n    quantity: \"\",\n    nameForPrinting: \"\",\n    comments: \"\"\n};\nconst PpeConsumable = (param)=>{\n    let { userData, pageType, sites, OrderRequestData, ppeId = null } = param;\n    var _currentItem_product, _currentItem_product_sizes, _currentItem_product1, _currentItem_product2, _currentItem_product3, _currentItem_product_sizes1, _currentItem_product4, _currentItem_product5;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [nameOfOriginator, setNameOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [emailOfOriginator, setEmailOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [siteData, setSiteData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            ...emptyItem\n        }\n    ]);\n    const [requiredDate, setRequiredDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [createdDate, setCreatedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [site, setSite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((sites === null || sites === void 0 ? void 0 : sites[0]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [productsData, setProductsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [currentItem, setCurrentItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        ...emptyItem\n    });\n    const [savedItems, setSavedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // console.log(\"dsksldibiasdbilsdbv\", siteData);\n    // Add validation states for current item\n    // const [productValid, setProductValid] = useState(\"\");\n    // const [quantityValid, setQuantityValid] = useState(\"\");\n    const [dateWarning, setDateWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // const [isEditMode, setIsEditMode] = useState();\n    const [requestStatus, setRequestStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const [requestStatusList, setRequestStatusList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [submitted, setSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popupType, setPopupType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [editingRowId, setEditingRowId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gridApi, setGridApi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const closeModal = (e)=>{\n        if (e) {\n            e.preventDefault();\n        }\n        setIsValidCancelReason(true);\n        setCancelledReasonapi(\"\");\n        setIsOpen(false);\n    };\n    //#region Load data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n        if (OrderRequestData || (OrderRequestData === null || OrderRequestData === void 0 ? void 0 : OrderRequestData.length) > 0) {\n            var _OrderRequestData_, _OrderRequestData_1, _OrderRequestData_2, _OrderRequestData_3;\n            // console.log(\"OrderRequestData\", OrderRequestData);\n            if ((_OrderRequestData_ = OrderRequestData[0]) === null || _OrderRequestData_ === void 0 ? void 0 : _OrderRequestData_.action_id) {\n                var _OrderRequestData_4, _OrderRequestData_5;\n                setRequestStatus((_OrderRequestData_4 = OrderRequestData[0]) === null || _OrderRequestData_4 === void 0 ? void 0 : _OrderRequestData_4.action_id);\n                if (((_OrderRequestData_5 = OrderRequestData[0]) === null || _OrderRequestData_5 === void 0 ? void 0 : _OrderRequestData_5.action_id) !== 5) {\n                    setSubmitted(true);\n                }\n            }\n            if ((_OrderRequestData_1 = OrderRequestData[0]) === null || _OrderRequestData_1 === void 0 ? void 0 : _OrderRequestData_1.site_id) {\n                var _OrderRequestData_6, _OrderRequestData_7;\n                setSite({\n                    label: (_OrderRequestData_6 = OrderRequestData[0]) === null || _OrderRequestData_6 === void 0 ? void 0 : _OrderRequestData_6.site_name,\n                    value: (_OrderRequestData_7 = OrderRequestData[0]) === null || _OrderRequestData_7 === void 0 ? void 0 : _OrderRequestData_7.site_id\n                });\n            }\n            if ((_OrderRequestData_2 = OrderRequestData[0]) === null || _OrderRequestData_2 === void 0 ? void 0 : _OrderRequestData_2.required_date) {\n                var _OrderRequestData_8, _OrderRequestData_9;\n                setRequiredDate(((_OrderRequestData_8 = OrderRequestData[0]) === null || _OrderRequestData_8 === void 0 ? void 0 : _OrderRequestData_8.required_date) ? new Date((_OrderRequestData_9 = OrderRequestData[0]) === null || _OrderRequestData_9 === void 0 ? void 0 : _OrderRequestData_9.required_date).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if ((_OrderRequestData_3 = OrderRequestData[0]) === null || _OrderRequestData_3 === void 0 ? void 0 : _OrderRequestData_3.created_at) {\n                var _OrderRequestData_10, _OrderRequestData_11;\n                setCreatedDate(((_OrderRequestData_10 = OrderRequestData[0]) === null || _OrderRequestData_10 === void 0 ? void 0 : _OrderRequestData_10.created_at) ? new Date((_OrderRequestData_11 = OrderRequestData[0]) === null || _OrderRequestData_11 === void 0 ? void 0 : _OrderRequestData_11.created_at).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if (OrderRequestData[0].user_name) {\n                // setNameOfOriginator();\n                setNameOfOriginator(OrderRequestData[0].user_name);\n            }\n            if (OrderRequestData[0].actioned_by_email) {\n                // setNameOfOriginator();\n                setEmailOfOriginator(OrderRequestData[0].actioned_by_email);\n            }\n            // setrowdata\n            if (OrderRequestData && Array.isArray(OrderRequestData)) {\n                console.log(\"load data\", OrderRequestData, requestStatus);\n                const STATUS_MAP = {\n                    1: \"Pending Review\",\n                    2: \"Approved\",\n                    3: \"Rejected\",\n                    4: \"Ordered\",\n                    5: \"Draft\"\n                };\n                const mappedItems = OrderRequestData.map((item)=>({\n                        id: item.order_request_items,\n                        product: {\n                            value: item.product_id,\n                            label: item.product_name\n                        },\n                        size: {\n                            value: item.size,\n                            label: item.size || \"N/A\"\n                        },\n                        quantity: item.quantity,\n                        nameForPrinting: item.name_for_printing,\n                        comments: (item.action_id === 3 ? \"Rejected Reason: \".concat(item.latest_comment) : item.comments) || \"\",\n                        updateFlag: 0,\n                        status: STATUS_MAP[item.action_id]\n                    }));\n                const uniqueValues = [\n                    ...new Set(OrderRequestData.map((item)=>item.action_id))\n                ];\n                setRequestStatusList(uniqueValues);\n                setSavedItems(mappedItems);\n                console.log(\"uniqueValues\", uniqueValues);\n            }\n        }\n    }, [\n        OrderRequestData\n    ]);\n    //#endregion Load data\n    //#region ag-grid\n    const onRowSelected = (event)=>{\n        if (event.node.isSelected()) {\n            setEditingRowId(event.data.id);\n            setCurrentItem(event.data);\n        }\n    };\n    const onGridReady = (params1)=>{\n        setGridApi(params1.api);\n    };\n    // inside your component\n    const getRowClass = (params1)=>{\n        return params1.data.id === editingRowId ? \"bg-blue-100\" : \"\";\n    };\n    const IconsRenderer = (props)=>{\n        const handleDelete = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n        };\n        const handleEdit = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            setHasUnsavedChanges(true);\n            setEditingRowId(selectedRow.id);\n            // setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n            const handleRowUpdate = (actionId)=>()=>{\n                    setSelectedRowData(params.data);\n                    setStatusAction(actionId);\n                    setShowStatusDialog(true);\n                };\n            // event.preventDefault();\n            // const selectedRow = props.data;\n            // setEditingRowId(selectedRow.id || props.node.id);\n            // // const updatedData = savedItems.filter((item) => item !== selectedRow);\n            // // setSavedItems(updatedData);\n            //   setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            // Populate the form with the selected row data\n            setCurrentItem({\n                id: selectedRow.id,\n                product: selectedRow.product,\n                size: selectedRow.size,\n                quantity: selectedRow.quantity,\n                nameForPrinting: selectedRow.nameForPrinting,\n                nameForPrintingFlag: selectedRow.nameForPrinting,\n                comments: selectedRow.comments,\n                updateFlag: 1\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-row gap-4 justify-center text-skin-primary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleEdit,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faPenToSquare\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDelete,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\", \" text-red-500\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faTrash\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        // onClick={handleRowUpdate(4)}\n                        icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faCircleCheck,\n                        className: \"\".concat(requestStatus !== 1 || !requestStatus ? \"hidden\" : \"\", \" text-green-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        // onClick={handleRowUpdate(4)}\n                        icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmarkCircle,\n                        className: \"\".concat(requestStatus !== 1 || !requestStatus ? \"hidden\" : \"\", \" text-red-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        // onClick={handleRowUpdate(4)}\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faSquareCheck,\n                        className: \"\".concat(requestStatus !== 2 || !requestStatus ? \"hidden\" : \"\", \" text-green-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Column definitions for the grid\n    const columnDefs = [\n        {\n            headerName: \"Product\",\n            field: \"product.label\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Size\",\n            field: \"size.label\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Quantity\",\n            field: \"quantity\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Name for Printing\",\n            field: \"nameForPrinting\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Comments\",\n            field: \"comments\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            hide: requestStatus === 5 || !requestStatus,\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: 1.25,\n            filterParams: {\n                values: [\n                    \"Pending Review\",\n                    \"Approved\",\n                    \"Rejected\",\n                    \"Ordered\",\n                    \"Draft\"\n                ]\n            }\n        },\n        {\n            headerName: \"Actions\",\n            cellRenderer: IconsRenderer,\n            headerClass: \"header-with-border\",\n            flex: 1\n        }\n    ];\n    //#endregion ag-grid\n    //#region api's\n    function getAllProducts() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-products\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to fetch products: \".concat(error.message));\n            throw error;\n        });\n    }\n    function getAllSites() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/sites\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        });\n    }\n    //#endregion api's\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const siteData = await getAllSites();\n                const formattedSites = siteData === null || siteData === void 0 ? void 0 : siteData.map((site)=>({\n                        value: site.id,\n                        label: site.name\n                    }));\n                setSiteData(formattedSites);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error fetching Sites\", error);\n            }\n        };\n        const fetchProducts = async ()=>{\n            try {\n                const productsJson = await getAllProducts();\n                const filterOutHiddenProducts = productsJson.filter((product)=>!product.IsProductHidden);\n                const formattedProducts = filterOutHiddenProducts === null || filterOutHiddenProducts === void 0 ? void 0 : filterOutHiddenProducts.map((product)=>({\n                        value: product.ProductId,\n                        label: product.ProductName,\n                        size_required: product.IsSizeRequired || product.size_required,\n                        sizes: product.AvailableSizes ? product.AvailableSizes.split(\", \").map((size)=>({\n                                value: size.toLowerCase().replace(/\\s+/g, \"\"),\n                                label: size\n                            })) : [],\n                        productCode: product.ProductCode,\n                        packageQuantity: product.PackageQuantity,\n                        productType: product.ProductType,\n                        name_printable: product.name_printable\n                    }));\n                setProductsData(formattedProducts);\n            } catch (error) {\n                console.error(\"Error fetching products:\", error);\n            }\n        };\n        fetchProducts();\n        fetchSites();\n        setLoading(false);\n    }, []);\n    const updateSelectedRowStatusForSingleItem = (updatedStatusID, params1)=>{\n        if (updatedStatusID == 3) {\n            if (cancelledReasonapi) {\n                setIsValidCancelReason(true);\n            } else {\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        console.log(\"sssssssssssssssssssssssssssssssssssss\", params1);\n        const apiPayload = {\n            request_no: params1.id,\n            item_number: params1.item_number,\n            ProductName: params1.product_name,\n            Size: params1.size,\n            Quantity: params1.quantity,\n            NameForPrinting: params1.NameForPrinting,\n            Comments: updatedStatusID == 3 ? cancelledReasonapi : params1.comment,\n            RequestItemID: params1.request_item_id,\n            commentOnUpdatingRequest: cancelledReasonapi,\n            action_id: updatedStatusID,\n            SubmitterUserID: userData.user_id,\n            SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n            orignatorEmail: params1 === null || params1 === void 0 ? void 0 : params1.orignatorEmail,\n            cancelledReasonapi: cancelledReasonapi\n        };\n        try {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-product-request-items/\").concat(params1.id), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n                getData().then((data)=>{\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            item_number: row.item_number,\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: (row.action_id === 3 ? \"Rejected Reason: \".concat(row.latest_comment) : row.comments) || \"\",\n                            size: row.size,\n                            quantity: row.quantity,\n                            NameForPrinting: row.name_for_printing,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    setRequestRowData(formattedData);\n                    setCancelledReasonapi(\"\");\n                // closeModal();\n                // setShowStatusDialog(false);\n                });\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    //#region Validate\n    // Update the validate function to use savedItems instead of items\n    const validate = ()=>{\n        const newErrors = {};\n        if (!site) newErrors.site = \"Site is required.\";\n        if (!requiredDate) newErrors.requiredDate = \"Required date is missing.\";\n        if (!savedItems || savedItems.length === 0) {\n            newErrors.savedItems = \"At least one item must be added.\";\n        }\n        // Update state for field-level messages\n        setErrors(newErrors);\n        const hasErrors = Object.keys(newErrors).length > 0;\n        if (hasErrors) {\n            const errorMessages = Object.values(newErrors);\n            // setPopupConfig({\n            //   title: \"Validation Error\",\n            //   message: errorMessages.join(\"\\n\"),\n            //   type: \"error\",\n            // });\n            // setShowPopup(true); // trigger popup\n            const errorsLength = Object.keys(newErrors).length;\n            if (errorsLength > 1) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill all required fields.\");\n            } else if (errorsLength == 1 && newErrors.savedItems) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please add at least one item.\");\n            }\n            return false;\n        }\n        return true;\n    };\n    const handleSiteAdd = (value)=>{\n        setSite(value);\n        // Clear the error for site if value is valid\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (value) {\n                delete updated.site;\n            }\n            return updated;\n        });\n    };\n    // Add date validation function\n    const validateRequiredDate = (selectedDate)=>{\n        // setRequiredDate(selectedDate);\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (selectedDate) {\n                delete updated.requiredDate;\n            }\n            return updated;\n        });\n        if (!selectedDate) {\n            setDateWarning(\"\");\n            return;\n        }\n        const today = new Date();\n        const selected = new Date(selectedDate);\n        const twoWeeksFromNow = new Date();\n        twoWeeksFromNow.setDate(today.getDate() + 14);\n        if (selected < twoWeeksFromNow) {\n            setDateWarning(\"Notice: Less than 2 weeks lead time may affect availability.\");\n        } else {\n            setDateWarning(\"\");\n        }\n    };\n    // #region handlers\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    const handleCancelReason = (data)=>{\n        if (data) {\n            setIsValidCancelReason(true);\n        } else {\n            setIsValidCancelReason(false);\n        }\n    };\n    const handleSaveClick = (e)=>{\n        // e.preventDefault();\n        if (hasUnsavedChanges) {\n            setPopupType(\"unsavedWarning\");\n            setIsOpen(true); // your existing modal state\n        } else {\n            saveCurrentItem(e);\n        }\n    };\n    const saveCurrentItem = (e)=>{\n        var _currentItem_product, _currentItem_product_sizes, _currentItem_product1;\n        console.log(\"save order request\");\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (e) {\n                delete updated.savedItems;\n            }\n            return updated;\n        });\n        if (!e) {\n            console.log(\"returning here\");\n            setDateWarning(\"\");\n            return;\n        }\n        e.preventDefault();\n        let count = 0;\n        if (!currentItem.product) {\n            console.log(\"++ product\");\n            count++;\n        // setProductValid(\"Please select a product\");\n        } else {\n        // setProductValid(\"\");\n        }\n        if (!currentItem.quantity || currentItem.quantity < 1) {\n            console.log(\"++ quantity\");\n            count++;\n        // setQuantityValid(\"Please enter a valid quantity\");\n        } else {\n            if (currentItem.quantity > 1 && currentItem.nameForPrinting) {\n                // currentItem.nameForPrinting=\"\"\n                setCurrentItem((prev)=>({\n                        ...prev,\n                        nameForPrinting: \"\"\n                    }));\n            }\n        // setQuantityValid(\"\");\n        }\n        if (((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) && ((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) && !currentItem.size) {\n            console.log(\"++ product size\");\n            count++;\n        // Add size validation if needed\n        }\n        if (count > 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill in all mandatory fields to add items with valid quantity\");\n            return;\n        }\n        //     if (editingRowId) {\n        //         // Update existing item\n        //   const updatedItems = savedItems.map(item =>\n        //     item.id === editingRowId ? { ...currentItem, id: editingRowId } : item\n        //   );\n        //   setSavedItems(updatedItems);\n        //   setEditingRowId(null);\n        // } else {\n        //   // Add new item\n        //   setSavedItems([...savedItems, { ...currentItem, id: Date.now() }]);\n        // }\n        if (editingRowId) {\n            // Add the edited item back to savedItems\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: editingRowId,\n                    product: currentItem.product ? {\n                        ...currentItem.product\n                    } : null,\n                    size: currentItem.size ? {\n                        ...currentItem.size\n                    } : null\n                }\n            ]);\n            setEditingRowId(null);\n        } else {\n            // Add new item\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: \"row-\".concat(Date.now())\n                }\n            ]);\n        }\n        // Reset current item\n        setCurrentItem({\n            ...emptyItem\n        });\n        setEditingRowId(null);\n        setHasUnsavedChanges(false);\n    };\n    // Handle current item changes\n    const handleCurrentItemChange = (field, value)=>{\n        if (field === \"product\") {\n            // console.log(\"field-------------\",value)\n            // Reset other fields when product changes\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    size: null,\n                    quantity: \"\",\n                    nameForPrinting: \"\",\n                    nameForPrintingFlag: \"\",\n                    comments: \"\"\n                }));\n        } else if (field === \"quantity\" && value > 1) {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    nameForPrinting: \"\"\n                }));\n        } else {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value\n                }));\n        }\n    };\n    //#region Save\n    const handleSubmit = (status_ID)=>{\n        if (status_ID != 1 && !validate()) {\n            return;\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssssssssssssssssssss\",savedItems);\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        // Transform savedItems to API format\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size;\n            return {\n                requestItemId: item.requestItemId,\n                // RequestItemID: typeof item.id === 'string' && item.id.startsWith('row-') ? null : item.id,\n                ProductID: (_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value,\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            Status_id: status_ID,\n            SubmitterUserID: userData === null || userData === void 0 ? void 0 : userData.user_id,\n            TargetSiteID: (site === null || site === void 0 ? void 0 : site.value) || 3,\n            RequiredDate: requiredDate,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            items: items,\n            orignatorEmail: emailOfOriginator\n        };\n        // console.log(\"items--------------------------------------------------------\",items)\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/create-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    //#region updated conditional\n    const updateSelectedRowStatus = (updatedStatusID, existingStatusId)=>{\n        const STATUS_MAP = {\n            1: \"Pending Review\",\n            2: \"Approved\",\n            3: \"Rejected\",\n            4: \"Ordered\",\n            5: \"Draft\"\n        };\n        const toBeUpdated = savedItems.filter((item)=>item.status == STATUS_MAP[existingStatusId]);\n        if (toBeUpdated.length === 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"No items found with the specified status\");\n            return;\n        }\n        setLoading(true);\n        if (updatedStatusID == 3) {\n            if (cancelledReasonapi) {\n                setIsValidCancelReason(true);\n                console.log(\"triggerrerererered\", cancelledReasonapi);\n            } else {\n                console.log(\"triggerrerererered\");\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssss\", toBeUpdated);\n        const updatePromises = toBeUpdated.map(async (item)=>{\n            const apiPayload = {\n                request_no: item.id,\n                item_number: item.item_number,\n                ProductName: item.product.label,\n                Size: item.size.label,\n                Quantity: item.quantity,\n                NameForPrinting: item.nameForPrinting,\n                Comments: updatedStatusID == 3 ? cancelledReasonapi : item.comments,\n                RequestItemID: item.id,\n                commentOnUpdatingRequest: cancelledReasonapi,\n                action_id: updatedStatusID,\n                SubmitterUserID: userData.user_id,\n                SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n                orignatorEmail: emailOfOriginator,\n                cancelledReasonapi: cancelledReasonapi\n            };\n            console.log(\"apiPayload\", apiPayload); // Log for debugging purposes.\n            const res = await fetch(\"\".concat(_services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress, \"ppe-consumables/update-product-request-items/\").concat(item.id), {\n                method: \"POST\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            });\n            if (res.status === 200) {\n                return res.json();\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    router.push(redirectUrl);\n                }, 3000);\n                throw new Error(\"Unauthorized\");\n            } else {\n                const errorText = await res.text();\n                console.error(\"Update failed:\", errorText);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to update item status.\");\n                throw new Error(\"Update failed\");\n            }\n        });\n        Promise.all(updatePromises).then((results)=>{\n            const successCount = results.filter((result)=>result === null || result === void 0 ? void 0 : result.msg).length;\n            if (successCount > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Successfully updated \".concat(successCount, \" item(s)\"), {\n                    position: \"top-right\"\n                });\n                setTimeout(()=>{\n                    router.replace(\"/ppe-consumable\");\n                }, 2000);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"No items were successfully updated\");\n                router.push(\"/ppe-consumable\");\n            }\n        }).catch((error)=>{\n            console.error(\"Error updating items:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Some items failed to update\");\n        }).finally(()=>{\n            setLoading(false);\n        });\n    };\n    //#endregion updated conditional\n    // #region update\n    const handleUpdate = (action_id)=>{\n        if (!validate()) {\n            return;\n        }\n        // handleSaveClick();\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        console.log(\"savedItems\", savedItems);\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size, _item_size1;\n            return {\n                // RequestItemID: item.id,\n                RequestItemID: typeof item.id === \"string\" && item.id.startsWith(\"row-\") ? null : item.id,\n                ProductID: item.product_id || ((_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value),\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                // SizeID: item.size?.value || null, //TODO no size handel\n                SizeID: ((_item_size1 = item.size) === null || _item_size1 === void 0 ? void 0 : _item_size1.value) && !isNaN(Number(item.size.value)) ? Number(item.size.value) : null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            requestId: ppeId,\n            action_id: action_id,\n            submitterUserId: userData.user_id,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            targetSiteId: site.value,\n            requiredDate: requiredDate,\n            items: items,\n            comment: cancelledReasonapi,\n            orignatorEmail: emailOfOriginator\n        };\n        console.log(\"apiPayload--------------------------------------------------------\", apiPayload);\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n        setLoading(false);\n        setTimeout(()=>{\n            setLoading(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Request submitted!\");\n            router.push(\"/ppe-consumable\");\n        }, 1000);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"update\") {\n            setIsEdit(true);\n        }\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n    }, [\n        0\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"flex flex-col justify-start max-w-full  mx-auto mr-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row gap-8 bg-white p-6 rounded-lg shadow-[0_0_1px_rgba(0,0,0,0.1)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1130,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: nameOfOriginator || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1131,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1129,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1139,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: emailOfOriginator || (userData === null || userData === void 0 ? void 0 : userData.email) || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1140,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1138,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Created Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1148,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: new Date().toLocaleDateString(\"en-CA\"),\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1149,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1147,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Site\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500 ml-[2px]\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1158,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1157,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            value: site,\n                                            onChange: (e)=>{\n                                                setSite();\n                                                handleSiteAdd(e);\n                                            },\n                                            options: siteData,\n                                            isDisabled: (sites === null || sites === void 0 ? void 0 : sites.length) === 1 || submitted,\n                                            styles: customSelectStyles,\n                                            isClearable: true,\n                                            className: \" w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1160,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        errors.site && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.site\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1172,\n                                            columnNumber: 31\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1156,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Required Date \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1176,\n                                                    columnNumber: 31\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1175,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            min: new Date().toISOString().split(\"T\")[0],\n                                            value: requiredDate,\n                                            onChange: (e)=>{\n                                                setRequiredDate(e.target.value);\n                                                validateRequiredDate(e.target.value);\n                                            },\n                                            className: \"block w-full px-4 border rounded-lg form-input\",\n                                            disabled: submitted\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1178,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        dateWarning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-orange-500 text-sm\",\n                                            children: dateWarning\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1190,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.requiredDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.requiredDate\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1193,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1174,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1128,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row justify-between items-end my-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"flex mb-1 font-semibold tracking-wider text-base pl-2\",\n                                    children: \"Items\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1213,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1212,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col mb-28px bg-white p-6 rounded-lg py-8 shadow-[0_0_2px_rgba(0,0,0,0.2)]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Product \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1222,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1221,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: currentItem.product,\n                                                        onChange: (val)=>handleCurrentItemChange(\"product\", val),\n                                                        options: productsData,\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1224,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1220,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Size\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 \".concat(!((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) || !((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) || submitted ? \"hidden\" : \"\"),\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1238,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1236,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: currentItem.size,\n                                                        onChange: (val)=>handleCurrentItemChange(\"size\", val),\n                                                        options: ((_currentItem_product2 = currentItem.product) === null || _currentItem_product2 === void 0 ? void 0 : _currentItem_product2.sizes) || [],\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: !((_currentItem_product3 = currentItem.product) === null || _currentItem_product3 === void 0 ? void 0 : _currentItem_product3.size_required) || !((_currentItem_product4 = currentItem.product) === null || _currentItem_product4 === void 0 ? void 0 : (_currentItem_product_sizes1 = _currentItem_product4.sizes) === null || _currentItem_product_sizes1 === void 0 ? void 0 : _currentItem_product_sizes1.length) || submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1250,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1235,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Quantity \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1267,\n                                                                columnNumber: 28\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1266,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        // min={0}\n                                                        max: 999,\n                                                        value: currentItem.quantity,\n                                                        onChange: (e)=>handleCurrentItemChange(\"quantity\", e.target.value > 999 ? 999 : parseInt(e.target.value)),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1269,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1265,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Name for Printing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1285,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        maxLength: \"50\" //TODO change in db also\n                                                        ,\n                                                        value: currentItem.nameForPrinting,\n                                                        onChange: (e)=>handleCurrentItemChange(\"nameForPrinting\", e.target.value),\n                                                        disabled: currentItem.quantity !== 1 || submitted || !((_currentItem_product5 = currentItem.product) === null || _currentItem_product5 === void 0 ? void 0 : _currentItem_product5.name_printable),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        placeholder: \"Only if quantity = 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1286,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1284,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Comments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1303,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        value: currentItem.comments,\n                                                        maxLength: \"500\",\n                                                        onChange: (e)=>handleCurrentItemChange(\"comments\", e.target.value),\n                                                        className: \"disabled:text-gray-400 disabled:bg-[#F6F3F3] block w-full h-8 px-4 border rounded-lg form-input resize-none\",\n                                                        rows: 1,\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1304,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1302,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    onClick: saveCurrentItem,\n                                                    // onClick={handleSaveClick}\n                                                    className: \"px-2 py-1 2xl:px-3.5 border border-skin-primary rounded-md text-skin-primary cursor-pointer \".concat(submitted ? \"hidden\" : \"\"),\n                                                    disabled: submitted,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faFloppyDisk\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1325,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1317,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1315,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1219,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ag-theme-alpine my-[24px]\",\n                                        style: {\n                                            height: 250,\n                                            width: \"100%\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__.AgGridReact, {\n                                            ref: gridRef,\n                                            columnDefs: columnDefs,\n                                            rowData: savedItems,\n                                            rowHeight: 35,\n                                            getRowClass: getRowClass,\n                                            onGridReady: onGridReady\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1336,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1332,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    popupType === \"unsavedWarning\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"One item is being edited. If you continue, changes will be lost.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1354,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: closeModal,\n                                                        className: \"border px-4 py-2 rounded\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1359,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            saveCurrentItem(); // discard editing row\n                                                            closeModal();\n                                                        },\n                                                        className: \"bg-blue-600 text-white px-4 py-2 rounded\",\n                                                        children: \"Save Anyway\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1365,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1358,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1353,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1218,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1211,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end gap-4 rounded-lg  p-4 mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    router.push(\"/ppe-consumable\");\n                                },\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6\",\n                                disabled: loading,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1381,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (pageType === \"edit\") {\n                                        console.log(\"hasUnsavedChanges\", hasUnsavedChanges);\n                                        if (hasUnsavedChanges) {\n                                            setPopupType(\"Save\");\n                                            setIsOpen(true);\n                                        } else {\n                                            handleUpdate(5);\n                                        }\n                                    } else {\n                                        handleSubmit(5);\n                                    }\n                                },\n                                disabled: loading || requestStatus === 1,\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6 \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1391,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (hasUnsavedChanges) {\n                                        setPopupType(\"submitWhileEditing\");\n                                        setIsOpen(true);\n                                    } else {\n                                        if (!validate()) return;\n                                        setPopupType(\"submit\");\n                                        setIsOpen(true);\n                                    }\n                                },\n                                className: \"border border-skin-primary bg-skin-primary text-white rounded-md px-6 font-medium \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                disabled: loading || requestStatus === 1,\n                                children: \"Submit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1413,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"reject\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-red-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Reject All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1432,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"approve\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Approve All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1451,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                // onClick={() => {\n                                //   updateSelectedRowStatus(4, 2);\n                                // }}\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"markOrdered\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(requestStatusList.includes(2) ? \"\" : \"hidden\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Mark All Approved Requests to Ordered\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1470,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1380,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1118,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition, {\n                appear: true,\n                show: isOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: (value)=>{\n                        if (popupType !== \"reject\" || isValidCancelReason) {\n                            closeModal();\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1518,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1509,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                            lineNumber: 1539,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1538,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Confirm \".concat(popupType == \"submit\" ? \"Submission\" : popupType == \"reject\" ? \"Rejection\" : popupType == \"approve\" ? \"Approval\" : popupType == \"markOrdered\" ? \"markOrdered\" : popupType == \"Save\" ? \"Save\" : popupType == \"submitWhileEditing\" ? \"Submission\" : \" \")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1537,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeModal,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1563,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1557,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1536,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                popupType == \"submit\" ? \"submit\" : popupType == \"rejectSingleItem\" ? \"reject\" : popupType == \"approveSingleItem\" ? \"approve\" : popupType == \"markSingleItemOrdered\" ? \"mark approved items as ordered\" : popupType == \"reject\" ? \"reject\" : popupType == \"approve\" ? \"approve\" : popupType == \"markOrdered\" ? \"mark approved items as ordered\" : popupType == \"Save\" ? \"save? there is a item being edited,\\n it will be lost if you Save \" : popupType == \"submitWhileEditing\" ? \"Submit? there is a item being edited,\\n it will be lost if you Submit \" : \" \",\n                                                                \" \",\n                                                                \"this request?\",\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1571,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        popupType == \"reject\" || popupType == \"rejectSingleItem\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                    className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                                    rows: \"8\",\n                                                                    value: cancelledReasonapi,\n                                                                    onChange: (e)=>{\n                                                                        setCancelledReasonapi(e.target.value);\n                                                                        if (e.target.value) {\n                                                                            setIsValidCancelReason(true);\n                                                                        }\n                                                                    },\n                                                                    onBlur: (e)=>{\n                                                                        const trimmedValue = trimInputText(e.target.value);\n                                                                        setCancelledReasonapi(trimmedValue);\n                                                                    },\n                                                                    placeholder: \"Provide reason for rejection...\",\n                                                                    maxLength: \"500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1597,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                (popupType == \"reject\" || popupType == \"rejectSingleItem\") && !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"Please Provide reason for cancellation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1617,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1595,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1570,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                closeModal(), setCancelledReasonapi(\"\");\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1626,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                if (popupType === \"reject\" && !(cancelledReasonapi === null || cancelledReasonapi === void 0 ? void 0 : cancelledReasonapi.trim())) {\n                                                                    setIsValidCancelReason(false);\n                                                                    return;\n                                                                }\n                                                                if (pageType == \"add\") {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleSubmit(1);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleSubmit(1);\n                                                                    }\n                                                                } else {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleUpdate(1);\n                                                                    } else if (popupType === \"reject\") {\n                                                                        updateSelectedRowStatus(3, 1);\n                                                                    } else if (popupType === \"approve\") {\n                                                                        updateSelectedRowStatus(2, 1);\n                                                                    } else if (popupType === \"rejectSingleItem\") {\n                                                                        updateSelectedRowStatusForSingleItem(3);\n                                                                    } else if (popupType === \"approveSingleItem\") {\n                                                                        updateSelectedRowStatusForSingleItem(2);\n                                                                    } else if (popupType === \"Save\") {\n                                                                        handleUpdate(5);\n                                                                    } else if (popupType === \"markOrdered\") {\n                                                                        updateSelectedRowStatus(4, 2);\n                                                                    } else if (popupType === \"markSingleItemOrdered\") {\n                                                                        updateSelectedRowStatusForSingleItem(4, 2);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleUpdate(1);\n                                                                    }\n                                                                }\n                                                                setCancelledReasonapi(\"\");\n                                                                closeModal();\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center h-full border border-skin-primary\",\n                                                            children: \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1636,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1625,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1534,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1532,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1523,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1522,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1521,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 1500,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1499,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(PpeConsumable, \"O68D4V6/DUUfJI1W3US3XF3lslU=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = PpeConsumable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PpeConsumable);\nvar _c;\n$RefreshReg$(_c, \"PpeConsumable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/PpeConsumable.js\n"));

/***/ })

});
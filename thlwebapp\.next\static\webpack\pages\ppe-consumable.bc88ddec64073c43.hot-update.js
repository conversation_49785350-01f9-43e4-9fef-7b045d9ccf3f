"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable",{

/***/ "./pages/ppe-consumable.js":
/*!*********************************!*\
  !*** ./pages/ppe-consumable.js ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _components_AddProductDialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/AddProductDialog */ \"./components/AddProductDialog.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var _utils_renderer_ppeActionRenderer__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/renderer/ppeActionRenderer */ \"./utils/renderer/ppeActionRenderer.js\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _utils_renderer_ppeActiveRenderer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/renderer/ppeActiveRenderer */ \"./utils/renderer/ppeActiveRenderer.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst PPEConsumables = (param)=>{\n    let { userData } = param;\n    _s();\n    // console.log(\"PARETN component rendered\", userData);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    // const [rowData, setRowData] = useState([]);\n    const [requestRowData, setRequestRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__.useLoading)();\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [orderView, setOrderView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [productTypes, setProductTypes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    //#region pop up states\n    const [showAddProduct, setShowAddProduct] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isEditingProduct, setIsEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editProductData, setEditProductData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Controlled filter states\n    const [siteFilter, setSiteFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [dateFilter, setDateFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showStatusDialog, setShowStatusDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedRowData, setSelectedRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [statusAction, setStatusAction] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Load saved view from localStorage on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Only run on client side\n        if (true) {\n            const savedView = localStorage.getItem(\"ppeSelectedView\");\n            if (savedView && (savedView === \"Orders\" || savedView === \"Products\")) {\n                setSelectedView(savedView);\n                setOrderView(savedView === \"Orders\");\n            } else {\n                setSelectedView(\"Orders\");\n                setOrderView(true);\n            }\n        }\n    }, []); // Empty dependency array - runs only on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!selectedView) return;\n        const fetchProducts = async ()=>{\n            if (selectedView === \"Orders\") {\n                getData().then((data)=>{\n                    // console.log(\"data\", data);\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: row.comments,\n                            NameForPrinting: row.name_for_printing,\n                            size: row.size,\n                            quantity: row.quantity,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    // console.log(\"Formatted Data:\", formattedData);\n                    // setRowData(formattedData);\n                    setRequestRowData(formattedData);\n                });\n            } else {\n                const [productsJson, productTypesJson] = await Promise.all([\n                    getAllProducts(),\n                    getProductTypes()\n                ]);\n                // console.log(\"Fetched Products JSON:\", productsJson);\n                // console.log(\"Fetched Product Types JSON:\", productTypesJson);\n                // Save product types for later use (e.g., filters or dialogs)\n                if (Array.isArray(productTypesJson)) {\n                    setProductTypes(productTypesJson);\n                } else {\n                    setProductTypes([]);\n                }\n                // console.log(\"Fetched Products JSON:\", productsJson);\n                if (productsJson && productsJson.length > 0) {\n                    // console.log(\n                    //   \"Fetched Products JSON inside code execution\",\n                    //   productsJson\n                    // );\n                    const formattedProductData = productsJson.map((product)=>({\n                            product_id: product.ProductId,\n                            product_name: product.ProductName || \"Unnamed Product\",\n                            category: product.ProductType || \"Uncategorized\",\n                            type_id: product.TypeId,\n                            stock: 0,\n                            sizes: product.AvailableSizes || \"One Size\",\n                            unit: deriveUnitFromPackageQuantity(product.PackageQuantity),\n                            name_printable: product.name_printable,\n                            IsProductHidden: product.IsProductHidden\n                        }));\n                    // setRowData(formattedProductData);\n                    setRequestRowData(formattedProductData);\n                } else {\n                    const fallbackProductData = [\n                        {\n                            product_name: \"Nitrile Gloves\",\n                            category: \"Gloves\",\n                            stock: 1200,\n                            unit: \"Box\",\n                            status: \"Available\"\n                        }\n                    ];\n                // setRowData(fallbackProductData);\n                }\n            }\n        };\n        fetchProducts();\n    }, [\n        selectedView\n    ]);\n    // #region get data\n    const getData = async ()=>{\n        // setRowData([]);\n        setRequestRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        console.log(\"\");\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-ppe-requests\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            console.error(error);\n        });\n    };\n    async function getAllProducts() {\n        // setRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-products\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to fetch products: \".concat(error.message));\n            throw error;\n        });\n    }\n    async function getProductTypes() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/get-product_types\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch product types\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to fetch product types: \".concat(error.message));\n            throw error;\n        });\n    }\n    //#region column def\n    // Example column definitions for Products\n    // Replace with proper filter handling\n    const handleSiteFilterChange = (value)=>{\n        setSiteFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"site_name\");\n            if (filterInstance) {\n                if (value) {\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        filter: value\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"status\");\n            if (filterInstance) {\n                if (value) {\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        filter: value\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    const handleSelectedView = (selectedType)=>{\n        setSelectedView(selectedType);\n        setOrderView(selectedType === \"Orders\");\n        // Only save to localStorage on client side\n        if (true) {\n            localStorage.setItem(\"ppeSelectedView\", selectedType);\n        }\n    };\n    const handleDateFilterChange = (value)=>{\n        setDateFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"required_date\");\n            if (filterInstance) {\n                if (value) {\n                    const selectedDate = new Date(value);\n                    const yyyy = selectedDate.getFullYear();\n                    const mm = String(selectedDate.getMonth() + 1).padStart(2, \"0\");\n                    const dd = String(selectedDate.getDate()).padStart(2, \"0\");\n                    const formattedDate = \"\".concat(yyyy, \"-\").concat(mm, \"-\").concat(dd);\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        dateFrom: formattedDate\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    // Example column definitions\n    const requestColumnDefs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>[\n            {\n                headerName: \"Request ID\",\n                field: \"request_id\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Required Date\",\n                field: \"required_date\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agDateColumnFilter\",\n                filterParams: {\n                    comparator: (filterLocalDateAtMidnight, cellValue)=>{\n                        const dateAsString = cellValue;\n                        if (!dateAsString) return 0;\n                        const cellDate = new Date(dateAsString);\n                        const cellDateAtMidnight = new Date(cellDate);\n                        cellDateAtMidnight.setHours(0, 0, 0, 0);\n                        if (cellDateAtMidnight < filterLocalDateAtMidnight) {\n                            return -1;\n                        } else if (cellDateAtMidnight > filterLocalDateAtMidnight) {\n                            return 1;\n                        } else {\n                            return 0;\n                        }\n                    }\n                },\n                valueFormatter: (params)=>{\n                    return params.value ? new Date(params.value).toLocaleDateString() : \"\";\n                }\n            },\n            {\n                headerName: \"Site\",\n                field: \"site_name\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\",\n                filterParams: {\n                    values: [\n                        \"ISS1-Teynham\",\n                        \"ISS2-Linton\",\n                        \"ISS3-Sittingbourne\"\n                    ]\n                }\n            },\n            {\n                headerName: \"Product\",\n                field: \"product_name\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Size\",\n                field: \"size\",\n                flex: 0.75,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Qty\",\n                field: \"quantity\",\n                flex: 0.75,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Requestor\",\n                field: \"requestor\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Comment\",\n                field: \"comment\",\n                flex: 1,\n                hide: !orderView\n            },\n            {\n                headerName: \"Status\",\n                field: \"status\",\n                hide: !orderView,\n                cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                cellStyle: ()=>({\n                        justifyContent: \"center\"\n                    }),\n                flex: 1.25,\n                filterParams: {\n                    values: [\n                        \"Pending Review\",\n                        \"Approved\",\n                        \"Rejected\",\n                        \"Ordered\",\n                        \"Draft\"\n                    ]\n                }\n            },\n            //product view\n            {\n                headerName: \"Product Name\",\n                field: \"product_name\",\n                flex: 1,\n                hide: orderView\n            },\n            {\n                headerName: \"Category\",\n                field: \"category\",\n                flex: 0.5,\n                hide: orderView\n            },\n            {\n                headerName: \"Sizes\",\n                field: \"sizes\",\n                flex: 0.5,\n                hide: orderView\n            },\n            {\n                headerName: \"Name Printing\",\n                field: \"name_printable\",\n                flex: 0.5,\n                hide: orderView,\n                cellRenderer: \"agCheckboxCellRenderer\",\n                cellRendererParams: {\n                    disabled: true\n                },\n                cellStyle: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\"\n                },\n                headerClass: \"ag-header-cell-centered\"\n            },\n            {\n                headerName: \"Status\",\n                // field: \"site_id\",\n                flex: 0.5,\n                cellRenderer: (params)=>(0,_utils_renderer_ppeActiveRenderer__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(params, setShowAddProduct, orderView, setIsEditingProduct, setEditProductData, //banana\n                    setShowStatusDialog, setSelectedRowData, setStatusAction),\n                sortable: false,\n                hide: orderView\n            },\n            {\n                headerName: \"Actions\",\n                field: \"site_id\",\n                flex: 1,\n                cellRenderer: (params)=>(0,_utils_renderer_ppeActionRenderer__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(params, setShowAddProduct, orderView, setIsEditingProduct, setEditProductData, //banana\n                    setShowStatusDialog, setSelectedRowData, setStatusAction, userData),\n                sortable: false\n            }\n        ], [\n        orderView\n    ]);\n    // console.log(\"order vbiew\", orderView);\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: true,\n            filterParams: {\n                debounceMs: 300\n            },\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }), []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsLoading(false);\n    // console.log(\n    //   \"Selected View:\",\n    //   selectedView,\n    //   \"Order View:\",\n    //   orderView,\n    //   selectedView === \"Orders\"\n    // );\n    }, [\n        selectedView\n    ]);\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(requestColumnDefs);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (gridRef.current && gridRef.current.api) {\n            gridRef.current.api.setColumnDefs(requestColumnDefs);\n            // Refresh the grid to apply new column definitions\n            gridRef.current.api.refreshHeader();\n            gridRef.current.api.refreshCells();\n        }\n    }, [\n        requestColumnDefs,\n        orderView\n    ]); // Add orderView dependency\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n        setSearchInput(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const handleClearFilters = ()=>{\n        // Clear AG Grid filters\n        if (gridRef.current && gridRef.current.api) {\n            gridRef.current.api.setFilterModel(null);\n            gridRef.current.api.onFilterChanged();\n        }\n        // Reset UI control states\n        setSiteFilter(\"\");\n        setStatusFilter(\"\");\n        setDateFilter(\"\");\n    };\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    //#region api\n    const updateSelectedRowStatus = (updatedStatusID, comment, params)=>{\n        if (updatedStatusID == 3) {\n            if (comment) {\n                setIsValidCancelReason(true);\n            } else {\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        console.log(\"sssssssssssssssssssssssssssssssssssss\", params);\n        const apiPayload = {\n            request_no: params.id,\n            // item_number: params.item_number,\n            ProductName: params.product_name,\n            Size: params.size,\n            Quantity: params.quantity,\n            NameForPrinting: params.NameForPrinting,\n            Comments: params.comment,\n            RequestItemID: params.request_item_id,\n            commentOnUpdatingRequest: comment,\n            action_id: updatedStatusID,\n            SubmitterUserID: userData.user_id,\n            SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n            orignatorEmail: params === null || params === void 0 ? void 0 : params.orignatorEmail\n        };\n        try {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-product-request-items/\").concat(params.id), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n                getData().then((data)=>{\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: row.comments,\n                            size: row.size,\n                            quantity: row.quantity,\n                            NameForPrinting: row.name_for_printing,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    setRequestRowData(formattedData);\n                    setCancelledReasonapi(\"\");\n                    setShowStatusDialog(false);\n                });\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_3__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                lineNumber: 724,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-20 md:mr-12 lg:mr-14\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                                children: [\n                                    (userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex rounded-full bg-gray-200 p-1 \\n                 \".concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\", \"\\n                \"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-1 rounded-full text-sm font-medium transition \".concat(selectedView === \"Orders\" ? \"bg-white shadow text-black\" : \"text-gray-500 hover:text-black\"),\n                                                    onClick: ()=>handleSelectedView(\"Orders\"),\n                                                    children: \"Requests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 743,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-1 rounded-full text-sm font-medium transition \".concat(selectedView === \"Products\" ? \"bg-white shadow text-black\" : \"text-gray-500 hover:text-black\"),\n                                                    onClick: ()=>{\n                                                        handleSelectedView(\"Products\"), handleClearFilters();\n                                                    },\n                                                    children: \"Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 732,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 731,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    orderView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        // style={{ marginBottom: \"10px\" }}\n                                        className: \"flex gap-4 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Site:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: siteFilter,\n                                                        onChange: (e)=>handleSiteFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"All Sites\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 780,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS1-Teynham\",\n                                                                children: \"ISS1-Teynham\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS2-Linton\",\n                                                                children: \"ISS2-Linton\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS3-Sittingbourne\",\n                                                                children: \"ISS3-Sittingbourne\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 773,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Status:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: statusFilter,\n                                                        onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"All Statuses\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Pending Review\",\n                                                                children: \"Pending\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 800,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Approved\",\n                                                                children: \"Approved\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 801,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Rejected\",\n                                                                children: \"Rejected\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Ordered\",\n                                                                children: \"Ordered\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Draft\",\n                                                                children: \"Draft\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 793,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 791,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Date:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 810,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: dateFilter,\n                                                        onChange: (e)=>handleDateFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 811,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 809,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleClearFilters,\n                                                className: \"ml-4 px-3 py-1 border rounded bg-gray-200 hover:bg-gray-300\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 819,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 769,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative block w-[47vh] text-gray-400  mt-0 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__.FontAwesomeIcon, {\n                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_17__.faSearch,\n                                                            className: \"fw-bold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 830,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 829,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"filter-text-box\",\n                                                        placeholder: \"Search...\",\n                                                        onInput: onFilterTextBoxChanged,\n                                                        value: searchInput,\n                                                        className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none shadow-[0_0_5px_rgba(0,0,0,0.1)]\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 832,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 828,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"ml-2 my-2 px-3 py-1 p-[6px] border rounded-md bg-skin-primary text-white text-sm cursor-pointer\",\n                                                onClick: ()=>{\n                                                    if (selectedView === \"Orders\") {\n                                                        router.push(\"/ppe-consumable/add\");\n                                                    } else {\n                                                        setShowAddProduct(true);\n                                                        setIsEditingProduct(false);\n                                                    }\n                                                },\n                                                children: orderView ? \"New Request\" : \"Add Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 841,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 827,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 727,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddProductDialog__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                open: showAddProduct,\n                                onOpenChange: (_, data)=>{\n                                    setShowAddProduct(data.open);\n                                    setIsEditingProduct(false);\n                                },\n                                onSubmit: (param)=>{\n                                    let { productName, printing, sizes } = param;\n                                    // handle your submit logic here\n                                    console.log({\n                                        productName,\n                                        printing,\n                                        sizes\n                                    });\n                                    setShowAddProduct(false);\n                                    router.reload();\n                                },\n                                isEditingProduct: isEditingProduct,\n                                editProductData: editProductData,\n                                productTypes: productTypes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 856,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative ag-theme-alpine !rounded-md\",\n                                    style: {\n                                        height: \"calc(100vh - 180px)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_5__.AgGridReact, {\n                                            rowData: requestRowData,\n                                            ref: gridRef,\n                                            requestColumnDefs: requestColumnDefs,\n                                            defaultColDef: defaultColDef,\n                                            suppressRowClickSelection: true,\n                                            pagination: true,\n                                            paginationPageSize: pageSize,\n                                            onPageSizeChanged: handlePageSizeChange,\n                                            tooltipShowDelay: 0,\n                                            tooltipHideDelay: 1000,\n                                            onGridReady: handleGridReady\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 878,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-start mt-2 pagination-style\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"page-size-select pagination\",\n                                                className: \"inputs\",\n                                                children: [\n                                                    \"Show\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"page-size-select\",\n                                                        onChange: handlePageSizeChange,\n                                                        value: pageSize,\n                                                        className: \"focus:outline-none\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 10,\n                                                                children: \"10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 900,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 15,\n                                                                children: \"15\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 901,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 25,\n                                                                children: \"25\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 902,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 50,\n                                                                children: \"50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 903,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 100,\n                                                                children: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 904,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 894,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" \",\n                                                    \"Entries\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 892,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 891,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                    lineNumber: 874,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 873,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                        lineNumber: 726,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.FluentProvider, {\n                        theme: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.webLightTheme,\n                        className: \"!bg-transparent\",\n                        style: {\n                            fontFamily: \"poppinsregular\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.Dialog, {\n                                open: showStatusDialog,\n                                onOpenChange: (_, data)=>{\n                                    if (!data.open) {\n                                        setIsValidCancelReason(true);\n                                        setCancelledReasonapi(\"\"); // Also clear the text\n                                    }\n                                    setShowStatusDialog(data.open);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogTrigger, {\n                                        disableButtonEnhancement: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"none\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 928,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 927,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogSurface, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogBody, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogTitle, {\n                                                    children: \"Update Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mb-6\",\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                statusAction === 2 ? \"approve\" : statusAction === 3 ? \"reject\" : statusAction === 4 ? \"mark as ordered\" : \"update\",\n                                                                \" \",\n                                                                \"this request ?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 934,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        statusAction === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                            rows: \"8\",\n                                                            value: cancelledReasonapi,\n                                                            onChange: (e)=>{\n                                                                setCancelledReasonapi(e.target.value);\n                                                                if (e.target.value) {\n                                                                    setIsValidCancelReason(true);\n                                                                }\n                                                            },\n                                                            onBlur: (e)=>{\n                                                                const trimmedValue = trimInputText(e.target.value);\n                                                                setCancelledReasonapi(trimmedValue);\n                                                            },\n                                                            placeholder: \"Provide reason for rejection...\",\n                                                            maxLength: \"500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 946,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        statusAction === 3 && !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"Please Provide reason for cancellation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 965,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"8px\",\n                                                                marginTop: \"16px\"\n                                                            },\n                                                            className: \"flex justify-end\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        setShowStatusDialog(false);\n                                                                        setCancelledReasonapi(\"\");\n                                                                    },\n                                                                    \"data-modal-hide\": \"default-modal\",\n                                                                    type: \"button\",\n                                                                    className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                    lineNumber: 973,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        // Handle status update logic here\n                                                                        console.log(\"Updating status:\", statusAction, selectedRowData);\n                                                                        updateSelectedRowStatus(statusAction, cancelledReasonapi, selectedRowData);\n                                                                    },\n                                                                    \"data-modal-hide\": \"default-modal\",\n                                                                    type: \"button\",\n                                                                    className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                                    children: \"Continue\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                    lineNumber: 981,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 969,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 933,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 931,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 930,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 917,\n                                columnNumber: 11\n                            }, undefined),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                        lineNumber: 912,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                lineNumber: 725,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(PPEConsumables, \"j80CPzQ/fhm9JozSpRdK+Ar7Bt0=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__.useLoading\n    ];\n});\n_c = PPEConsumables;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PPEConsumables);\n// Helper function to derive unit from package quantity\nfunction deriveUnitFromPackageQuantity(packageQuantity) {\n    if (!packageQuantity) return \"Unit\";\n    const lower = packageQuantity.toLowerCase();\n    if (lower.includes(\"box\")) return \"Box\";\n    if (lower.includes(\"pack\")) return \"Pack\";\n    if (lower.includes(\"pair\")) return \"Pair\";\n    return \"Unit\";\n}\nvar _c;\n$RefreshReg$(_c, \"PPEConsumables\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/ppe-consumable.js\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable",{

/***/ "./pages/ppe-consumable.js":
/*!*********************************!*\
  !*** ./pages/ppe-consumable.js ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _components_AddProductDialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/AddProductDialog */ \"./components/AddProductDialog.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var _utils_renderer_ppeActionRenderer__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/renderer/ppeActionRenderer */ \"./utils/renderer/ppeActionRenderer.js\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _utils_renderer_ppeActiveRenderer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/renderer/ppeActiveRenderer */ \"./utils/renderer/ppeActiveRenderer.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst PPEConsumables = (param)=>{\n    let { userData } = param;\n    _s();\n    // console.log(\"PARETN component rendered\", userData);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    // const [rowData, setRowData] = useState([]);\n    const [requestRowData, setRequestRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__.useLoading)();\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [orderView, setOrderView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [productTypes, setProductTypes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    //#region pop up states\n    const [showAddProduct, setShowAddProduct] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isEditingProduct, setIsEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editProductData, setEditProductData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Controlled filter states\n    const [siteFilter, setSiteFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [dateFilter, setDateFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showStatusDialog, setShowStatusDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedRowData, setSelectedRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [statusAction, setStatusAction] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Load saved view from localStorage on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Only run on client side\n        if (true) {\n            const savedView = localStorage.getItem(\"ppeSelectedView\");\n            if (savedView && (savedView === \"Orders\" || savedView === \"Products\")) {\n                setSelectedView(savedView);\n                setOrderView(savedView === \"Orders\");\n            } else {\n                setSelectedView(\"Orders\");\n                setOrderView(true);\n            }\n        }\n    }, []); // Empty dependency array - runs only on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!selectedView) return;\n        const fetchProducts = async ()=>{\n            if (selectedView === \"Orders\") {\n                getData().then((data)=>{\n                    // console.log(\"data\", data);\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: row.comments,\n                            NameForPrinting: row.name_for_printing,\n                            size: row.size,\n                            quantity: row.quantity,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    // console.log(\"Formatted Data:\", formattedData);\n                    // setRowData(formattedData);\n                    setRequestRowData(formattedData);\n                });\n            } else {\n                const [productsJson, productTypesJson] = await Promise.all([\n                    getAllProducts(),\n                    getProductTypes()\n                ]);\n                // console.log(\"Fetched Products JSON:\", productsJson);\n                // console.log(\"Fetched Product Types JSON:\", productTypesJson);\n                // Save product types for later use (e.g., filters or dialogs)\n                if (Array.isArray(productTypesJson)) {\n                    setProductTypes(productTypesJson);\n                } else {\n                    setProductTypes([]);\n                }\n                // console.log(\"Fetched Products JSON:\", productsJson);\n                if (productsJson && productsJson.length > 0) {\n                    // console.log(\n                    //   \"Fetched Products JSON inside code execution\",\n                    //   productsJson\n                    // );\n                    const formattedProductData = productsJson.map((product)=>({\n                            product_id: product.ProductId,\n                            product_name: product.ProductName || \"Unnamed Product\",\n                            category: product.ProductType || \"Uncategorized\",\n                            type_id: product.TypeId,\n                            stock: 0,\n                            sizes: product.AvailableSizes || \"One Size\",\n                            unit: deriveUnitFromPackageQuantity(product.PackageQuantity),\n                            name_printable: product.name_printable,\n                            IsProductHidden: product.IsProductHidden\n                        }));\n                    // setRowData(formattedProductData);\n                    setRequestRowData(formattedProductData);\n                } else {\n                    const fallbackProductData = [\n                        {\n                            product_name: \"Nitrile Gloves\",\n                            category: \"Gloves\",\n                            stock: 1200,\n                            unit: \"Box\",\n                            status: \"Available\"\n                        }\n                    ];\n                // setRowData(fallbackProductData);\n                }\n            }\n        };\n        fetchProducts();\n    }, [\n        selectedView\n    ]);\n    // #region get data\n    const getData = async ()=>{\n        // setRowData([]);\n        setRequestRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        console.log(\"\");\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-ppe-requests\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await logout();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            console.error(error);\n        });\n    };\n    async function getAllProducts() {\n        // setRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-products\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await logout();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to fetch products: \".concat(error.message));\n            throw error;\n        });\n    }\n    async function getProductTypes() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/get-product_types\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await logout();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch product types\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to fetch product types: \".concat(error.message));\n            throw error;\n        });\n    }\n    //#region column def\n    // Example column definitions for Products\n    // Replace with proper filter handling\n    const handleSiteFilterChange = (value)=>{\n        setSiteFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"site_name\");\n            if (filterInstance) {\n                if (value) {\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        filter: value\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"status\");\n            if (filterInstance) {\n                if (value) {\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        filter: value\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    const handleSelectedView = (selectedType)=>{\n        setSelectedView(selectedType);\n        setOrderView(selectedType === \"Orders\");\n        // Only save to localStorage on client side\n        if (true) {\n            localStorage.setItem(\"ppeSelectedView\", selectedType);\n        }\n    };\n    const handleDateFilterChange = (value)=>{\n        setDateFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"required_date\");\n            if (filterInstance) {\n                if (value) {\n                    const selectedDate = new Date(value);\n                    const yyyy = selectedDate.getFullYear();\n                    const mm = String(selectedDate.getMonth() + 1).padStart(2, \"0\");\n                    const dd = String(selectedDate.getDate()).padStart(2, \"0\");\n                    const formattedDate = \"\".concat(yyyy, \"-\").concat(mm, \"-\").concat(dd);\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        dateFrom: formattedDate\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    // Example column definitions\n    const requestColumnDefs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>[\n            {\n                headerName: \"Request ID\",\n                field: \"request_id\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Required Date\",\n                field: \"required_date\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agDateColumnFilter\",\n                filterParams: {\n                    comparator: (filterLocalDateAtMidnight, cellValue)=>{\n                        const dateAsString = cellValue;\n                        if (!dateAsString) return 0;\n                        const cellDate = new Date(dateAsString);\n                        const cellDateAtMidnight = new Date(cellDate);\n                        cellDateAtMidnight.setHours(0, 0, 0, 0);\n                        if (cellDateAtMidnight < filterLocalDateAtMidnight) {\n                            return -1;\n                        } else if (cellDateAtMidnight > filterLocalDateAtMidnight) {\n                            return 1;\n                        } else {\n                            return 0;\n                        }\n                    }\n                },\n                valueFormatter: (params)=>{\n                    return params.value ? new Date(params.value).toLocaleDateString() : \"\";\n                }\n            },\n            {\n                headerName: \"Site\",\n                field: \"site_name\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\",\n                filterParams: {\n                    values: [\n                        \"ISS1-Teynham\",\n                        \"ISS2-Linton\",\n                        \"ISS3-Sittingbourne\"\n                    ]\n                }\n            },\n            {\n                headerName: \"Product\",\n                field: \"product_name\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Size\",\n                field: \"size\",\n                flex: 0.75,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Qty\",\n                field: \"quantity\",\n                flex: 0.75,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Requestor\",\n                field: \"requestor\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Comment\",\n                field: \"comment\",\n                flex: 1,\n                hide: !orderView\n            },\n            {\n                headerName: \"Status\",\n                field: \"status\",\n                hide: !orderView,\n                cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                cellStyle: ()=>({\n                        justifyContent: \"center\"\n                    }),\n                flex: 1.25,\n                filterParams: {\n                    values: [\n                        \"Pending Review\",\n                        \"Approved\",\n                        \"Rejected\",\n                        \"Ordered\",\n                        \"Draft\"\n                    ]\n                }\n            },\n            //product view\n            {\n                headerName: \"Product Name\",\n                field: \"product_name\",\n                flex: 1,\n                hide: orderView\n            },\n            {\n                headerName: \"Category\",\n                field: \"category\",\n                flex: 0.5,\n                hide: orderView\n            },\n            {\n                headerName: \"Sizes\",\n                field: \"sizes\",\n                flex: 0.5,\n                hide: orderView\n            },\n            {\n                headerName: \"Name Printing\",\n                field: \"name_printable\",\n                flex: 0.5,\n                hide: orderView,\n                cellRenderer: \"agCheckboxCellRenderer\",\n                cellRendererParams: {\n                    disabled: true\n                },\n                cellStyle: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\"\n                },\n                headerClass: \"ag-header-cell-centered\"\n            },\n            {\n                headerName: \"Status\",\n                // field: \"site_id\",\n                flex: 0.5,\n                cellRenderer: (params)=>(0,_utils_renderer_ppeActiveRenderer__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(params, setShowAddProduct, orderView, setIsEditingProduct, setEditProductData, //banana\n                    setShowStatusDialog, setSelectedRowData, setStatusAction),\n                sortable: false,\n                hide: orderView\n            },\n            {\n                headerName: \"Actions\",\n                field: \"site_id\",\n                flex: 1,\n                cellRenderer: (params)=>(0,_utils_renderer_ppeActionRenderer__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(params, setShowAddProduct, orderView, setIsEditingProduct, setEditProductData, //banana\n                    setShowStatusDialog, setSelectedRowData, setStatusAction, userData),\n                sortable: false\n            }\n        ], [\n        orderView\n    ]);\n    // console.log(\"order vbiew\", orderView);\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: true,\n            filterParams: {\n                debounceMs: 300\n            },\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }), []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsLoading(false);\n    // console.log(\n    //   \"Selected View:\",\n    //   selectedView,\n    //   \"Order View:\",\n    //   orderView,\n    //   selectedView === \"Orders\"\n    // );\n    }, [\n        selectedView\n    ]);\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(requestColumnDefs);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (gridRef.current && gridRef.current.api) {\n            gridRef.current.api.setColumnDefs(requestColumnDefs);\n            // Refresh the grid to apply new column definitions\n            gridRef.current.api.refreshHeader();\n            gridRef.current.api.refreshCells();\n        }\n    }, [\n        requestColumnDefs,\n        orderView\n    ]); // Add orderView dependency\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n        setSearchInput(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const handleClearFilters = ()=>{\n        // Clear AG Grid filters\n        if (gridRef.current && gridRef.current.api) {\n            gridRef.current.api.setFilterModel(null);\n            gridRef.current.api.onFilterChanged();\n        }\n        // Reset UI control states\n        setSiteFilter(\"\");\n        setStatusFilter(\"\");\n        setDateFilter(\"\");\n    };\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    //#region api\n    const updateSelectedRowStatus = (updatedStatusID, comment, params)=>{\n        if (updatedStatusID == 3) {\n            if (comment) {\n                setIsValidCancelReason(true);\n            } else {\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        console.log(\"sssssssssssssssssssssssssssssssssssss\", params);\n        const apiPayload = {\n            request_no: params.id,\n            item_number: params.item_number,\n            ProductName: params.product_name,\n            Size: params.size,\n            Quantity: params.quantity,\n            NameForPrinting: params.NameForPrinting,\n            Comments: params.comment,\n            RequestItemID: params.request_item_id,\n            commentOnUpdatingRequest: comment,\n            action_id: updatedStatusID,\n            SubmitterUserID: userData.user_id,\n            SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n            orignatorEmail: params === null || params === void 0 ? void 0 : params.orignatorEmail\n        };\n        try {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-product-request-items/\").concat(params.id), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await logout();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n                getData().then((data)=>{\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: row.comments,\n                            size: row.size,\n                            quantity: row.quantity,\n                            NameForPrinting: row.name_for_printing,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    setRequestRowData(formattedData);\n                    setCancelledReasonapi(\"\");\n                    setShowStatusDialog(false);\n                });\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_3__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                lineNumber: 723,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-20 md:mr-12 lg:mr-14\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                                children: [\n                                    (userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex rounded-full bg-gray-200 p-1 \\n                 \".concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\", \"\\n                \"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-1 rounded-full text-sm font-medium transition \".concat(selectedView === \"Orders\" ? \"bg-white shadow text-black\" : \"text-gray-500 hover:text-black\"),\n                                                    onClick: ()=>handleSelectedView(\"Orders\"),\n                                                    children: \"Requests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 742,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-1 rounded-full text-sm font-medium transition \".concat(selectedView === \"Products\" ? \"bg-white shadow text-black\" : \"text-gray-500 hover:text-black\"),\n                                                    onClick: ()=>{\n                                                        handleSelectedView(\"Products\"), handleClearFilters();\n                                                    },\n                                                    children: \"Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 731,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 730,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    orderView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        // style={{ marginBottom: \"10px\" }}\n                                        className: \"flex gap-4 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Site:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: siteFilter,\n                                                        onChange: (e)=>handleSiteFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"All Sites\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS1-Teynham\",\n                                                                children: \"ISS1-Teynham\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS2-Linton\",\n                                                                children: \"ISS2-Linton\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS3-Sittingbourne\",\n                                                                children: \"ISS3-Sittingbourne\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 772,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Status:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 791,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: statusFilter,\n                                                        onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"All Statuses\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Pending Review\",\n                                                                children: \"Pending\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 799,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Approved\",\n                                                                children: \"Approved\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 800,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Rejected\",\n                                                                children: \"Rejected\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 801,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Ordered\",\n                                                                children: \"Ordered\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Draft\",\n                                                                children: \"Draft\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 790,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Date:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 809,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: dateFilter,\n                                                        onChange: (e)=>handleDateFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 810,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 808,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleClearFilters,\n                                                className: \"ml-4 px-3 py-1 border rounded bg-gray-200 hover:bg-gray-300\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 818,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 768,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative block w-[47vh] text-gray-400  mt-0 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__.FontAwesomeIcon, {\n                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_16__.faSearch,\n                                                            className: \"fw-bold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 829,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"filter-text-box\",\n                                                        placeholder: \"Search...\",\n                                                        onInput: onFilterTextBoxChanged,\n                                                        value: searchInput,\n                                                        className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none shadow-[0_0_5px_rgba(0,0,0,0.1)]\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 827,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"ml-2 my-2 px-3 py-1 p-[6px] border rounded-md bg-skin-primary text-white text-sm cursor-pointer\",\n                                                onClick: ()=>{\n                                                    if (selectedView === \"Orders\") {\n                                                        router.push(\"/ppe-consumable/add\");\n                                                    } else {\n                                                        setShowAddProduct(true);\n                                                        setIsEditingProduct(false);\n                                                    }\n                                                },\n                                                children: orderView ? \"New Request\" : \"Add Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 840,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 826,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 726,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddProductDialog__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                open: showAddProduct,\n                                onOpenChange: (_, data)=>{\n                                    setShowAddProduct(data.open);\n                                    setIsEditingProduct(false);\n                                },\n                                onSubmit: (param)=>{\n                                    let { productName, printing, sizes } = param;\n                                    // handle your submit logic here\n                                    console.log({\n                                        productName,\n                                        printing,\n                                        sizes\n                                    });\n                                    setShowAddProduct(false);\n                                    router.reload();\n                                },\n                                isEditingProduct: isEditingProduct,\n                                editProductData: editProductData,\n                                productTypes: productTypes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 855,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative ag-theme-alpine !rounded-md\",\n                                    style: {\n                                        height: \"calc(100vh - 180px)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_5__.AgGridReact, {\n                                            rowData: requestRowData,\n                                            ref: gridRef,\n                                            requestColumnDefs: requestColumnDefs,\n                                            defaultColDef: defaultColDef,\n                                            suppressRowClickSelection: true,\n                                            pagination: true,\n                                            paginationPageSize: pageSize,\n                                            onPageSizeChanged: handlePageSizeChange,\n                                            tooltipShowDelay: 0,\n                                            tooltipHideDelay: 1000,\n                                            onGridReady: handleGridReady\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 877,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-start mt-2 pagination-style\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"page-size-select pagination\",\n                                                className: \"inputs\",\n                                                children: [\n                                                    \"Show\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"page-size-select\",\n                                                        onChange: handlePageSizeChange,\n                                                        value: pageSize,\n                                                        className: \"focus:outline-none\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 10,\n                                                                children: \"10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 899,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 15,\n                                                                children: \"15\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 900,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 25,\n                                                                children: \"25\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 901,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 50,\n                                                                children: \"50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 902,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 100,\n                                                                children: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 903,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" \",\n                                                    \"Entries\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 891,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 890,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                    lineNumber: 873,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 872,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                        lineNumber: 725,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.FluentProvider, {\n                        theme: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.webLightTheme,\n                        className: \"!bg-transparent\",\n                        style: {\n                            fontFamily: \"poppinsregular\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.Dialog, {\n                                open: showStatusDialog,\n                                onOpenChange: (_, data)=>{\n                                    if (!data.open) {\n                                        setIsValidCancelReason(true);\n                                        setCancelledReasonapi(\"\"); // Also clear the text\n                                    }\n                                    setShowStatusDialog(data.open);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogTrigger, {\n                                        disableButtonEnhancement: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"none\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 927,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 926,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogSurface, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogBody, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogTitle, {\n                                                    children: \"Update Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 931,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mb-6\",\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                statusAction === 2 ? \"approve\" : statusAction === 3 ? \"reject\" : statusAction === 4 ? \"mark as ordered\" : \"update\",\n                                                                \" \",\n                                                                \"this request ?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        statusAction === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                            rows: \"8\",\n                                                            value: cancelledReasonapi,\n                                                            onChange: (e)=>{\n                                                                setCancelledReasonapi(e.target.value);\n                                                                if (e.target.value) {\n                                                                    setIsValidCancelReason(true);\n                                                                }\n                                                            },\n                                                            onBlur: (e)=>{\n                                                                const trimmedValue = trimInputText(e.target.value);\n                                                                setCancelledReasonapi(trimmedValue);\n                                                            },\n                                                            placeholder: \"Provide reason for rejection...\",\n                                                            maxLength: \"500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 945,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        statusAction === 3 && !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"Please Provide reason for cancellation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"8px\",\n                                                                marginTop: \"16px\"\n                                                            },\n                                                            className: \"flex justify-end\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        setShowStatusDialog(false);\n                                                                        setCancelledReasonapi(\"\");\n                                                                    },\n                                                                    \"data-modal-hide\": \"default-modal\",\n                                                                    type: \"button\",\n                                                                    className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                    lineNumber: 972,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        // Handle status update logic here\n                                                                        console.log(\"Updating status:\", statusAction, selectedRowData);\n                                                                        updateSelectedRowStatus(statusAction, cancelledReasonapi, selectedRowData);\n                                                                    },\n                                                                    \"data-modal-hide\": \"default-modal\",\n                                                                    type: \"button\",\n                                                                    className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                                    children: \"Continue\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                    lineNumber: 980,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 968,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 930,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 929,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 916,\n                                columnNumber: 11\n                            }, undefined),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                        lineNumber: 911,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                lineNumber: 724,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(PPEConsumables, \"j80CPzQ/fhm9JozSpRdK+Ar7Bt0=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__.useLoading\n    ];\n});\n_c = PPEConsumables;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PPEConsumables);\n// Helper function to derive unit from package quantity\nfunction deriveUnitFromPackageQuantity(packageQuantity) {\n    if (!packageQuantity) return \"Unit\";\n    const lower = packageQuantity.toLowerCase();\n    if (lower.includes(\"box\")) return \"Box\";\n    if (lower.includes(\"pack\")) return \"Pack\";\n    if (lower.includes(\"pair\")) return \"Pair\";\n    return \"Unit\";\n}\nvar _c;\n$RefreshReg$(_c, \"PPEConsumables\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9wcGUtY29uc3VtYWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXlDO0FBTzFCO0FBQ3dDO0FBQ1I7QUFDSDtBQUNHO0FBQ1E7QUFDVTtBQUNKO0FBQ0Q7QUFDcEI7QUFDUztBQUNZO0FBQ2M7QUFDUjtBQVcvQjtBQUNtQztBQUV2RSxNQUFNNEIsaUJBQWlCO1FBQUMsRUFBRUMsUUFBUSxFQUFFOztJQUNsQyxzREFBc0Q7SUFDdEQsTUFBTUMsU0FBU2pCLHVEQUFTQTtJQUN4Qiw4Q0FBOEM7SUFDOUMsTUFBTSxDQUFDa0IsZ0JBQWdCQyxrQkFBa0IsR0FBRzlCLCtDQUFRQSxDQUFDLEVBQUU7SUFDdkQsTUFBTSxDQUFDK0IsVUFBVUMsWUFBWSxHQUFHaEMsK0NBQVFBLENBQUM7SUFDekMsTUFBTWlDLFVBQVVoQyw2Q0FBTUE7SUFDdEIsTUFBTSxFQUFFaUMsWUFBWSxFQUFFLEdBQUd4Qix5RUFBVUE7SUFDbkMsTUFBTSxDQUFDeUIsYUFBYUMsZUFBZSxHQUFHcEMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDcUMsYUFBYUMsZUFBZSxHQUFHdEMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDdUMsY0FBY0MsZ0JBQWdCLEdBQUd4QywrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUN5QyxXQUFXQyxhQUFhLEdBQUcxQywrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUMyQyxjQUFjQyxnQkFBZ0IsR0FBRzVDLCtDQUFRQSxDQUFDLEVBQUU7SUFFbkQsdUJBQXVCO0lBQ3ZCLE1BQU0sQ0FBQzZDLGdCQUFnQkMsa0JBQWtCLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUMrQyxrQkFBa0JDLG9CQUFvQixHQUFHaEQsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDaUQsaUJBQWlCQyxtQkFBbUIsR0FBR2xELCtDQUFRQTtJQUN0RCxNQUFNLENBQUNtRCxxQkFBcUJDLHVCQUF1QixHQUFHcEQsK0NBQVFBLENBQUM7SUFFL0QsMkJBQTJCO0lBQzNCLE1BQU0sQ0FBQ3FELFlBQVlDLGNBQWMsR0FBR3RELCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3VELGNBQWNDLGdCQUFnQixHQUFHeEQsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDeUQsWUFBWUMsY0FBYyxHQUFHMUQsK0NBQVFBLENBQUM7SUFFN0MsTUFBTSxDQUFDMkQsa0JBQWtCQyxvQkFBb0IsR0FBRzVELCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQzZELGlCQUFpQkMsbUJBQW1CLEdBQUc5RCwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUMrRCxjQUFjQyxnQkFBZ0IsR0FBR2hFLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ2lFLG9CQUFvQkMsc0JBQXNCLEdBQUdsRSwrQ0FBUUEsQ0FBQztJQUU3RCx1REFBdUQ7SUFDdkRFLGdEQUFTQSxDQUFDO1FBQ1IsMEJBQTBCO1FBQzFCLElBQUksSUFBNkIsRUFBRTtZQUNqQyxNQUFNaUUsWUFBWUMsYUFBYUMsT0FBTyxDQUFDO1lBQ3ZDLElBQUlGLGFBQWNBLENBQUFBLGNBQWMsWUFBWUEsY0FBYyxVQUFTLEdBQUk7Z0JBQ3JFM0IsZ0JBQWdCMkI7Z0JBQ2hCekIsYUFBYXlCLGNBQWM7WUFDN0IsT0FBTztnQkFDTDNCLGdCQUFnQjtnQkFDaEJFLGFBQWE7WUFDZjtRQUNGO0lBQ0YsR0FBRyxFQUFFLEdBQUcsOENBQThDO0lBRXREeEMsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUNxQyxjQUFjO1FBQ25CLE1BQU0rQixnQkFBZ0I7WUFDcEIsSUFBSS9CLGlCQUFpQixVQUFVO2dCQUM3QmdDLFVBQVVDLElBQUksQ0FBQyxDQUFDQztvQkFDZCw2QkFBNkI7b0JBQzdCLE1BQU1DLFVBQVVELGlCQUFBQSwyQkFBQUEsS0FBTUUsTUFBTSxDQUFDLENBQUNDLEtBQUtDO3dCQUMvQixJQUFJLENBQUNELEdBQUcsQ0FBQ0MsSUFBSUMsVUFBVSxDQUFDLEVBQUU7NEJBQ3hCRixHQUFHLENBQUNDLElBQUlDLFVBQVUsQ0FBQyxHQUFHLEVBQUU7d0JBQzFCO3dCQUNBRixHQUFHLENBQUNDLElBQUlDLFVBQVUsQ0FBQyxDQUFDQyxJQUFJLENBQUNGO3dCQUN6QixPQUFPRDtvQkFDVCxHQUFHLENBQUM7b0JBQ04sTUFBTUksYUFBYTt3QkFDakIsR0FBRzt3QkFDSCxHQUFHO3dCQUNILEdBQUc7d0JBQ0gsR0FBRzt3QkFDSCxHQUFHO29CQUNMO29CQUNBLE1BQU1DLFdBQVcsQ0FBQztvQkFDbEIsTUFBTUMsZ0JBQWdCVCxpQkFBQUEsMkJBQUFBLEtBQU1VLEdBQUcsQ0FBQyxDQUFDTjt3QkFDL0JJLFFBQVEsQ0FBQ0osSUFBSUMsVUFBVSxDQUFDLEdBQUcsQ0FBQ0csUUFBUSxDQUFDSixJQUFJQyxVQUFVLENBQUMsSUFBSSxLQUFLO3dCQUM3RCxNQUFNTSxXQUFXVixPQUFPLENBQUNHLElBQUlDLFVBQVUsQ0FBQzt3QkFDdEMsTUFBTU8sUUFBUUQsU0FBU0UsU0FBUyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxlQUFlLEtBQUtYLElBQUlXLGVBQWUsSUFBSTt3QkFFckYsT0FBTzs0QkFDTEMsSUFBSVosSUFBSVksRUFBRTs0QkFDVlgsWUFBWSxHQUF1QkQsT0FBcEJBLElBQUlDLFVBQVUsRUFBQyxPQUFxQixPQUFoQkQsSUFBSWEsV0FBVzs0QkFDbERDLGVBQWUsSUFBSUMsS0FBS2YsSUFBSWMsYUFBYSxFQUN0Q0UsV0FBVyxHQUNYQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7NEJBQ2hCQyxXQUFXbEIsSUFBSWtCLFNBQVM7NEJBQ3hCQyxjQUFjbkIsSUFBSW1CLFlBQVksSUFBSTs0QkFDbENDLFdBQVdwQixJQUFJcUIsU0FBUzs0QkFDeEJDLGdCQUFnQnRCLElBQUl1QixpQkFBaUI7NEJBQ3JDQyxTQUFTeEIsSUFBSXlCLFFBQVE7NEJBQ3JCQyxpQkFBZ0IxQixJQUFJMkIsaUJBQWlCOzRCQUNyQ0MsTUFBTTVCLElBQUk0QixJQUFJOzRCQUNkQyxVQUFVN0IsSUFBSTZCLFFBQVE7NEJBQ3RCQyxRQUFRM0IsVUFBVSxDQUFDSCxJQUFJK0IsU0FBUyxDQUFDOzRCQUNqQ3BCLGlCQUFpQlgsSUFBSVcsZUFBZTt3QkFDdEM7b0JBQ0Y7b0JBQ0EsaURBQWlEO29CQUNqRCw2QkFBNkI7b0JBQzdCMUQsa0JBQWtCb0Q7Z0JBQ3BCO1lBQ0YsT0FBTztnQkFDTCxNQUFNLENBQUMyQixjQUFjQyxpQkFBaUIsR0FBRyxNQUFNQyxRQUFRQyxHQUFHLENBQUM7b0JBQ3pEQztvQkFDQUM7aUJBQ0Q7Z0JBRUQsdURBQXVEO2dCQUN2RCxnRUFBZ0U7Z0JBRWhFLDhEQUE4RDtnQkFDOUQsSUFBSUMsTUFBTUMsT0FBTyxDQUFDTixtQkFBbUI7b0JBQ25DbEUsZ0JBQWdCa0U7Z0JBQ2xCLE9BQU87b0JBQ0xsRSxnQkFBZ0IsRUFBRTtnQkFDcEI7Z0JBQ0EsdURBQXVEO2dCQUN2RCxJQUFJaUUsZ0JBQWdCQSxhQUFhUSxNQUFNLEdBQUcsR0FBRztvQkFDM0MsZUFBZTtvQkFDZixtREFBbUQ7b0JBQ25ELGlCQUFpQjtvQkFDakIsS0FBSztvQkFDTCxNQUFNQyx1QkFBdUJULGFBQWExQixHQUFHLENBQUMsQ0FBQ29DLFVBQWE7NEJBQzFEQyxZQUFZRCxRQUFRRSxTQUFTOzRCQUM3QnpCLGNBQWN1QixRQUFRRyxXQUFXLElBQUk7NEJBQ3JDQyxVQUFVSixRQUFRSyxXQUFXLElBQUk7NEJBQ2pDQyxTQUFTTixRQUFRTyxNQUFNOzRCQUN2QkMsT0FBTzs0QkFDUEMsT0FBT1QsUUFBUVUsY0FBYyxJQUFJOzRCQUNqQ0MsTUFBTUMsOEJBQThCWixRQUFRYSxlQUFlOzRCQUMzREMsZ0JBQWdCZCxRQUFRYyxjQUFjOzRCQUN0Q0MsaUJBQWlCZixRQUFRZSxlQUFlO3dCQUMxQztvQkFDQSxvQ0FBb0M7b0JBQ3BDeEcsa0JBQWtCd0Y7Z0JBQ3BCLE9BQU87b0JBQ0wsTUFBTWlCLHNCQUFzQjt3QkFDMUI7NEJBQ0V2QyxjQUFjOzRCQUNkMkIsVUFBVTs0QkFDVkksT0FBTzs0QkFDUEcsTUFBTTs0QkFDTnZCLFFBQVE7d0JBQ1Y7cUJBQ0Q7Z0JBQ0QsbUNBQW1DO2dCQUNyQztZQUNGO1FBQ0Y7UUFDQXJDO0lBQ0YsR0FBRztRQUFDL0I7S0FBYTtJQUVqQixtQkFBbUI7SUFDbkIsTUFBTWdDLFVBQVU7UUFDZCxrQkFBa0I7UUFDbEJ6QyxrQkFBa0IsRUFBRTtRQUNwQixJQUFJMEcsZ0JBQWdCNUgsMkRBQVNBLENBQUM0SCxhQUFhO1FBQzNDQyxRQUFRQyxHQUFHLENBQUM7UUFDWixPQUFPQyxNQUFNLEdBQWlCLE9BQWRILGVBQWMscUNBQW1DO1lBQy9ESSxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1lBQ0FDLGFBQWE7UUFDZixHQUNHdEUsSUFBSSxDQUFDLE9BQU91RTtZQUNYLElBQUlBLElBQUlwQyxNQUFNLElBQUksS0FBSztnQkFDckJyRSxlQUFlO2dCQUNmO1lBQ0Y7WUFDQUEsZUFBZTtZQUNmLElBQUl5RyxJQUFJcEMsTUFBTSxLQUFLLEtBQUs7Z0JBQ3RCLE9BQU9vQyxJQUFJQyxJQUFJO1lBQ2pCO1lBQ0EsSUFBSUQsSUFBSXBDLE1BQU0sS0FBSyxLQUFLO2dCQUN0QnJHLGlEQUFLQSxDQUFDMkksS0FBSyxDQUNUO1lBRUosT0FBTyxJQUFJRixJQUFJcEMsTUFBTSxLQUFLLEtBQUs7Z0JBQzdCckcsaURBQUtBLENBQUMySSxLQUFLLENBQUM7Z0JBQ1pDLFdBQVc7b0JBQ1QsTUFBTUM7b0JBQ052SCxPQUFPbUQsSUFBSSxDQUFDO2dCQUNkLEdBQUc7WUFDTDtZQUNBLE1BQU0sSUFBSXFFLE1BQU07UUFDbEIsR0FDQ0MsS0FBSyxDQUFDLENBQUNKO1lBQ05SLFFBQVFRLEtBQUssQ0FBQ0E7UUFDaEI7SUFDSjtJQUNBLGVBQWVoQztRQUNiLGtCQUFrQjtRQUNsQixJQUFJdUIsZ0JBQWdCNUgsMkRBQVNBLENBQUM0SCxhQUFhO1FBQzNDLE9BQU9HLE1BQU0sR0FBaUIsT0FBZEgsZUFBYyxpQ0FBK0I7WUFDM0RJLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7WUFDQUMsYUFBYTtRQUNmLEdBQ0d0RSxJQUFJLENBQUMsT0FBT3VFO1lBQ1gsSUFBSUEsSUFBSXBDLE1BQU0sSUFBSSxLQUFLO2dCQUNyQnJFLGVBQWU7Z0JBQ2Y7WUFDRjtZQUNBQSxlQUFlO1lBQ2YsSUFBSXlHLElBQUlwQyxNQUFNLEtBQUssS0FBSztnQkFDdEIsT0FBT29DLElBQUlDLElBQUk7WUFDakI7WUFDQSxJQUFJRCxJQUFJcEMsTUFBTSxLQUFLLEtBQUs7Z0JBQ3RCckcsaURBQUtBLENBQUMySSxLQUFLLENBQ1Q7WUFFSixPQUFPLElBQUlGLElBQUlwQyxNQUFNLEtBQUssS0FBSztnQkFDN0JyRyxpREFBS0EsQ0FBQzJJLEtBQUssQ0FBQztnQkFDWkMsV0FBVztvQkFDVCxNQUFNQztvQkFDTnZILE9BQU9tRCxJQUFJLENBQUM7Z0JBQ2QsR0FBRztZQUNMO1lBQ0EsTUFBTSxJQUFJcUUsTUFBTTtRQUNsQixHQUNDQyxLQUFLLENBQUMsQ0FBQ0o7WUFDTlIsUUFBUVEsS0FBSyxDQUFDQTtZQUNkM0ksaURBQUtBLENBQUMySSxLQUFLLENBQUMsNkJBQTJDLE9BQWRBLE1BQU1LLE9BQU87WUFDdEQsTUFBTUw7UUFDUjtJQUNKO0lBRUEsZUFBZS9CO1FBQ2IsSUFBSXNCLGdCQUFnQjVILDJEQUFTQSxDQUFDNEgsYUFBYTtRQUMzQyxPQUFPRyxNQUFNLEdBQWlCLE9BQWRILGVBQWMsc0NBQW9DO1lBQ2hFSSxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1lBQ0FDLGFBQWE7UUFDZixHQUNHdEUsSUFBSSxDQUFDLE9BQU91RTtZQUNYLElBQUlBLElBQUlwQyxNQUFNLElBQUksS0FBSztnQkFDckJyRSxlQUFlO2dCQUNmO1lBQ0Y7WUFDQUEsZUFBZTtZQUNmLElBQUl5RyxJQUFJcEMsTUFBTSxLQUFLLEtBQUs7Z0JBQ3RCLE9BQU9vQyxJQUFJQyxJQUFJO1lBQ2pCO1lBQ0EsSUFBSUQsSUFBSXBDLE1BQU0sS0FBSyxLQUFLO2dCQUN0QnJHLGlEQUFLQSxDQUFDMkksS0FBSyxDQUNUO1lBRUosT0FBTyxJQUFJRixJQUFJcEMsTUFBTSxLQUFLLEtBQUs7Z0JBQzdCckcsaURBQUtBLENBQUMySSxLQUFLLENBQUM7Z0JBQ1pDLFdBQVc7b0JBQ1QsTUFBTUM7b0JBQ052SCxPQUFPbUQsSUFBSSxDQUFDO2dCQUNkLEdBQUc7WUFDTDtZQUNBLE1BQU0sSUFBSXFFLE1BQU07UUFDbEIsR0FDQ0MsS0FBSyxDQUFDLENBQUNKO1lBQ05SLFFBQVFRLEtBQUssQ0FBQ0E7WUFDZDNJLGlEQUFLQSxDQUFDMkksS0FBSyxDQUFDLGtDQUFnRCxPQUFkQSxNQUFNSyxPQUFPO1lBQzNELE1BQU1MO1FBQ1I7SUFDSjtJQUVBLG9CQUFvQjtJQUNwQiwwQ0FBMEM7SUFDMUMsc0NBQXNDO0lBQ3RDLE1BQU1NLHlCQUF5QixDQUFDQztRQUM5QmxHLGNBQWNrRztRQUNkLElBQUl2SCxRQUFRd0gsT0FBTyxJQUFJeEgsUUFBUXdILE9BQU8sQ0FBQ0MsR0FBRyxFQUFFO1lBQzFDLE1BQU1DLGlCQUFpQjFILFFBQVF3SCxPQUFPLENBQUNDLEdBQUcsQ0FBQ0UsaUJBQWlCLENBQUM7WUFDN0QsSUFBSUQsZ0JBQWdCO2dCQUNsQixJQUFJSCxPQUFPO29CQUNURyxlQUFlRSxRQUFRLENBQUM7d0JBQ3RCQyxNQUFNO3dCQUNOQyxRQUFRUDtvQkFDVjtnQkFDRixPQUFPO29CQUNMRyxlQUFlRSxRQUFRLENBQUM7Z0JBQzFCO2dCQUNBNUgsUUFBUXdILE9BQU8sQ0FBQ0MsR0FBRyxDQUFDTSxlQUFlO1lBQ3JDO1FBQ0Y7SUFDRjtJQUVBLE1BQU1DLDJCQUEyQixDQUFDVDtRQUNoQ2hHLGdCQUFnQmdHO1FBQ2hCLElBQUl2SCxRQUFRd0gsT0FBTyxJQUFJeEgsUUFBUXdILE9BQU8sQ0FBQ0MsR0FBRyxFQUFFO1lBQzFDLE1BQU1DLGlCQUFpQjFILFFBQVF3SCxPQUFPLENBQUNDLEdBQUcsQ0FBQ0UsaUJBQWlCLENBQUM7WUFDN0QsSUFBSUQsZ0JBQWdCO2dCQUNsQixJQUFJSCxPQUFPO29CQUNURyxlQUFlRSxRQUFRLENBQUM7d0JBQ3RCQyxNQUFNO3dCQUNOQyxRQUFRUDtvQkFDVjtnQkFDRixPQUFPO29CQUNMRyxlQUFlRSxRQUFRLENBQUM7Z0JBQzFCO2dCQUNBNUgsUUFBUXdILE9BQU8sQ0FBQ0MsR0FBRyxDQUFDTSxlQUFlO1lBQ3JDO1FBQ0Y7SUFDRjtJQUVBLE1BQU1FLHFCQUFxQixDQUFDQztRQUMxQjNILGdCQUFnQjJIO1FBQ2hCekgsYUFBYXlILGlCQUFpQjtRQUM5QiwyQ0FBMkM7UUFDM0MsSUFBSSxJQUE2QixFQUFFO1lBQ2pDL0YsYUFBYWdHLE9BQU8sQ0FBQyxtQkFBbUJEO1FBQzFDO0lBQ0Y7SUFFQSxNQUFNRSx5QkFBeUIsQ0FBQ2I7UUFDOUI5RixjQUFjOEY7UUFDZCxJQUFJdkgsUUFBUXdILE9BQU8sSUFBSXhILFFBQVF3SCxPQUFPLENBQUNDLEdBQUcsRUFBRTtZQUMxQyxNQUFNQyxpQkFDSjFILFFBQVF3SCxPQUFPLENBQUNDLEdBQUcsQ0FBQ0UsaUJBQWlCLENBQUM7WUFDeEMsSUFBSUQsZ0JBQWdCO2dCQUNsQixJQUFJSCxPQUFPO29CQUNULE1BQU1jLGVBQWUsSUFBSTFFLEtBQUs0RDtvQkFDOUIsTUFBTWUsT0FBT0QsYUFBYUUsV0FBVztvQkFDckMsTUFBTUMsS0FBS0MsT0FBT0osYUFBYUssUUFBUSxLQUFLLEdBQUdDLFFBQVEsQ0FBQyxHQUFHO29CQUMzRCxNQUFNQyxLQUFLSCxPQUFPSixhQUFhUSxPQUFPLElBQUlGLFFBQVEsQ0FBQyxHQUFHO29CQUV0RCxNQUFNRyxnQkFBZ0IsR0FBV04sT0FBUkYsTUFBSyxLQUFTTSxPQUFOSixJQUFHLEtBQU0sT0FBSEk7b0JBRXZDbEIsZUFBZUUsUUFBUSxDQUFDO3dCQUN0QkMsTUFBTTt3QkFDTmtCLFVBQVVEO29CQUNaO2dCQUNGLE9BQU87b0JBQ0xwQixlQUFlRSxRQUFRLENBQUM7Z0JBQzFCO2dCQUNBNUgsUUFBUXdILE9BQU8sQ0FBQ0MsR0FBRyxDQUFDTSxlQUFlO1lBQ3JDO1FBQ0Y7SUFDRjtJQUVBLDZCQUE2QjtJQUM3QixNQUFNaUIsb0JBQW9COUssOENBQU9BLENBQy9CLElBQU07WUFDSjtnQkFDRStLLFlBQVk7Z0JBQ1pDLE9BQU87Z0JBQ1BDLE1BQU07Z0JBQ05DLE1BQU0sQ0FBQzVJO2dCQUNQc0gsUUFBUTtZQUNWO1lBQ0E7Z0JBQ0VtQixZQUFZO2dCQUNaQyxPQUFPO2dCQUNQQyxNQUFNO2dCQUNOQyxNQUFNLENBQUM1STtnQkFDUHNILFFBQVE7Z0JBQ1J1QixjQUFjO29CQUNaQyxZQUFZLENBQUNDLDJCQUEyQkM7d0JBQ3RDLE1BQU1DLGVBQWVEO3dCQUNyQixJQUFJLENBQUNDLGNBQWMsT0FBTzt3QkFFMUIsTUFBTUMsV0FBVyxJQUFJL0YsS0FBSzhGO3dCQUMxQixNQUFNRSxxQkFBcUIsSUFBSWhHLEtBQUsrRjt3QkFDcENDLG1CQUFtQkMsUUFBUSxDQUFDLEdBQUcsR0FBRyxHQUFHO3dCQUVyQyxJQUFJRCxxQkFBcUJKLDJCQUEyQjs0QkFDbEQsT0FBTyxDQUFDO3dCQUNWLE9BQU8sSUFBSUkscUJBQXFCSiwyQkFBMkI7NEJBQ3pELE9BQU87d0JBQ1QsT0FBTzs0QkFDTCxPQUFPO3dCQUNUO29CQUNGO2dCQUNGO2dCQUNBTSxnQkFBZ0IsQ0FBQ0M7b0JBQ2YsT0FBT0EsT0FBT3ZDLEtBQUssR0FDZixJQUFJNUQsS0FBS21HLE9BQU92QyxLQUFLLEVBQUV3QyxrQkFBa0IsS0FDekM7Z0JBQ047WUFDRjtZQUNBO2dCQUNFZCxZQUFZO2dCQUNaQyxPQUFPO2dCQUNQQyxNQUFNO2dCQUNOQyxNQUFNLENBQUM1STtnQkFDUHNILFFBQVE7Z0JBQ1J1QixjQUFjO29CQUNaVyxRQUFRO3dCQUFDO3dCQUFnQjt3QkFBZTtxQkFBcUI7Z0JBQy9EO1lBQ0Y7WUFDQTtnQkFDRWYsWUFBWTtnQkFDWkMsT0FBTztnQkFDUEMsTUFBTTtnQkFDTkMsTUFBTSxDQUFDNUk7Z0JBQ1BzSCxRQUFRO1lBQ1Y7WUFDQTtnQkFDRW1CLFlBQVk7Z0JBQ1pDLE9BQU87Z0JBQ1BDLE1BQU07Z0JBQ05DLE1BQU0sQ0FBQzVJO2dCQUNQc0gsUUFBUTtZQUNWO1lBQ0E7Z0JBQ0VtQixZQUFZO2dCQUNaQyxPQUFPO2dCQUNQQyxNQUFNO2dCQUNOQyxNQUFNLENBQUM1STtnQkFDUHNILFFBQVE7WUFDVjtZQUNBO2dCQUNFbUIsWUFBWTtnQkFDWkMsT0FBTztnQkFDUEMsTUFBTTtnQkFDTkMsTUFBTSxDQUFDNUk7Z0JBQ1BzSCxRQUFRO1lBQ1Y7WUFDQTtnQkFBRW1CLFlBQVk7Z0JBQVdDLE9BQU87Z0JBQVdDLE1BQU07Z0JBQUdDLE1BQU0sQ0FBQzVJO1lBQVU7WUFDckU7Z0JBQ0V5SSxZQUFZO2dCQUNaQyxPQUFPO2dCQUNQRSxNQUFNLENBQUM1STtnQkFDUHlKLGNBQWNwTCw4RUFBcUJBO2dCQUNuQ3FMLFdBQVcsSUFBTzt3QkFBRUMsZ0JBQWdCO29CQUFTO2dCQUM3Q2hCLE1BQU07Z0JBQ05FLGNBQWM7b0JBQ1pXLFFBQVE7d0JBQ047d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7cUJBQ0Q7Z0JBQ0g7WUFDRjtZQUVBLGNBQWM7WUFDZDtnQkFDRWYsWUFBWTtnQkFDWkMsT0FBTztnQkFDUEMsTUFBTTtnQkFDTkMsTUFBTTVJO1lBQ1I7WUFDQTtnQkFBRXlJLFlBQVk7Z0JBQVlDLE9BQU87Z0JBQVlDLE1BQU07Z0JBQUtDLE1BQU01STtZQUFVO1lBQ3hFO2dCQUFFeUksWUFBWTtnQkFBU0MsT0FBTztnQkFBU0MsTUFBTTtnQkFBS0MsTUFBTTVJO1lBQVU7WUFDbEU7Z0JBQ0V5SSxZQUFZO2dCQUNaQyxPQUFPO2dCQUNQQyxNQUFNO2dCQUNOQyxNQUFNNUk7Z0JBQ055SixjQUFjO2dCQUNkRyxvQkFBb0I7b0JBQ2xCQyxVQUFVO2dCQUNaO2dCQUNBSCxXQUFXO29CQUNUSSxTQUFTO29CQUNUQyxZQUFZO29CQUNaSixnQkFBZ0I7Z0JBQ2xCO2dCQUNBSyxhQUFhO1lBQ2Y7WUFDQTtnQkFDRXZCLFlBQVk7Z0JBQ1osb0JBQW9CO2dCQUNwQkUsTUFBTTtnQkFDTmMsY0FBYyxDQUFDSCxTQUNidEssOEVBQXFCQSxDQUNuQnNLLFFBQ0FqSixtQkFDQUwsV0FDQU8scUJBQ0FFLG9CQUNBLFFBQVE7b0JBQ1JVLHFCQUNBRSxvQkFDQUU7Z0JBRUowSSxVQUFVO2dCQUNWckIsTUFBTTVJO1lBRVI7WUFDQTtnQkFDRXlJLFlBQVk7Z0JBQ1pDLE9BQU87Z0JBQ1BDLE1BQU07Z0JBQ05jLGNBQWMsQ0FBQ0gsU0FDYmhMLDhFQUFpQkEsQ0FDZmdMLFFBQ0FqSixtQkFDQUwsV0FDQU8scUJBQ0FFLG9CQUNBLFFBQVE7b0JBQ1JVLHFCQUNBRSxvQkFDQUUsaUJBQ0FyQztnQkFFSitLLFVBQVU7WUFDWjtTQUNELEVBQ0Q7UUFBQ2pLO0tBQVU7SUFHYix5Q0FBeUM7SUFDekMsTUFBTWtLLGdCQUFnQnhNLDhDQUFPQSxDQUMzQixJQUFPO1lBQ0x1TSxVQUFVO1lBQ1YzQyxRQUFRO1lBQ1J1QixjQUFjO2dCQUNac0IsWUFBWTtZQUNkO1lBQ0FDLFdBQVc7WUFDWHpCLE1BQU07WUFDTjBCLGNBQWM7UUFDaEIsSUFDQSxFQUFFO0lBR0o1TSxnREFBU0EsQ0FBQztRQUNSZ0MsYUFBYTtJQUNiLGVBQWU7SUFDZixzQkFBc0I7SUFDdEIsa0JBQWtCO0lBQ2xCLG1CQUFtQjtJQUNuQixlQUFlO0lBQ2YsOEJBQThCO0lBQzlCLEtBQUs7SUFDUCxHQUFHO1FBQUNLO0tBQWE7SUFFakIsTUFBTXdLLGtCQUFrQixDQUFDaEI7UUFDdkJBLE9BQU9yQyxHQUFHLENBQUNzRCxhQUFhLENBQUMvQjtJQUMzQjtJQUVBL0ssZ0RBQVNBLENBQUM7UUFDUixJQUFJK0IsUUFBUXdILE9BQU8sSUFBSXhILFFBQVF3SCxPQUFPLENBQUNDLEdBQUcsRUFBRTtZQUMxQ3pILFFBQVF3SCxPQUFPLENBQUNDLEdBQUcsQ0FBQ3NELGFBQWEsQ0FBQy9CO1lBQ2xDLG1EQUFtRDtZQUNuRGhKLFFBQVF3SCxPQUFPLENBQUNDLEdBQUcsQ0FBQ3VELGFBQWE7WUFDakNoTCxRQUFRd0gsT0FBTyxDQUFDQyxHQUFHLENBQUN3RCxZQUFZO1FBQ2xDO0lBQ0YsR0FBRztRQUFDakM7UUFBbUJ4STtLQUFVLEdBQUcsMkJBQTJCO0lBRS9ELE1BQU0wSyx5QkFBeUIvTSxrREFBV0EsQ0FBQztRQUN6QzZCLFFBQVF3SCxPQUFPLENBQUNDLEdBQUcsQ0FBQzBELGNBQWMsQ0FDaENDLFNBQVNDLGNBQWMsQ0FBQyxtQkFBbUI5RCxLQUFLO1FBRWxEcEgsZUFBZWlMLFNBQVNDLGNBQWMsQ0FBQyxtQkFBbUI5RCxLQUFLO0lBQ2pFLEdBQUcsRUFBRTtJQUVMLE1BQU0rRCx1QkFBdUIsQ0FBQ0M7UUFDNUIsTUFBTUMsY0FBY0MsU0FBU0YsTUFBTUcsTUFBTSxDQUFDbkUsS0FBSyxFQUFFO1FBQ2pEeEgsWUFBWXlMO1FBQ1p4TCxRQUFRd0gsT0FBTyxDQUFDQyxHQUFHLENBQUNrRSxxQkFBcUIsQ0FBQ0g7SUFDNUM7SUFFQSxNQUFNSSxxQkFBcUI7UUFDekIsd0JBQXdCO1FBQ3hCLElBQUk1TCxRQUFRd0gsT0FBTyxJQUFJeEgsUUFBUXdILE9BQU8sQ0FBQ0MsR0FBRyxFQUFFO1lBQzFDekgsUUFBUXdILE9BQU8sQ0FBQ0MsR0FBRyxDQUFDb0UsY0FBYyxDQUFDO1lBQ25DN0wsUUFBUXdILE9BQU8sQ0FBQ0MsR0FBRyxDQUFDTSxlQUFlO1FBQ3JDO1FBRUEsMEJBQTBCO1FBQzFCMUcsY0FBYztRQUNkRSxnQkFBZ0I7UUFDaEJFLGNBQWM7SUFDaEI7SUFDQSxNQUFNcUssZ0JBQWdCLENBQUNDO1FBQ3JCLE9BQU9BLE1BQU1DLElBQUk7SUFDbkI7SUFFQSxhQUFhO0lBQ2IsTUFBTUMsMEJBQTBCLENBQUNDLGlCQUFpQjlILFNBQVMwRjtRQUN6RCxJQUFJb0MsbUJBQW1CLEdBQUU7WUFDdkIsSUFBRzlILFNBQVM7Z0JBQ1ZqRCx1QkFBdUI7WUFDekIsT0FBTztnQkFDTEEsdUJBQXVCO2dCQUN2QjtZQUNGO1FBQ0Y7UUFFQXFGLFFBQVFDLEdBQUcsQ0FBQyx5Q0FBeUNxRDtRQUNyRCxNQUFNcUMsYUFBYTtZQUNqQkMsWUFBWXRDLE9BQU90RyxFQUFFO1lBQ3JCQyxhQUFhcUcsT0FBT3JHLFdBQVc7WUFDL0JnQyxhQUFhcUUsT0FBTy9GLFlBQVk7WUFDaENzSSxNQUFNdkMsT0FBT3RGLElBQUk7WUFDakI4SCxVQUFVeEMsT0FBT3JGLFFBQVE7WUFDekJILGlCQUFpQndGLE9BQU94RixlQUFlO1lBQ3ZDaUksVUFBVXpDLE9BQU8xRixPQUFPO1lBQ3hCb0ksZUFBZTFDLE9BQU92RyxlQUFlO1lBQ3JDa0osMEJBQTBCckk7WUFDMUJPLFdBQVd1SDtZQUNYUSxpQkFBaUJoTixTQUFTaU4sT0FBTztZQUNqQ0MsY0FBYyxFQUFFbE4scUJBQUFBLCtCQUFBQSxTQUFVbU4sS0FBSztZQUMvQjNJLGNBQWMsRUFBRTRGLG1CQUFBQSw2QkFBQUEsT0FBUTVGLGNBQWM7UUFDeEM7UUFHQSxJQUFJO1lBQ0YsSUFBSXFDLGdCQUFnQjVILDJEQUFTQSxDQUFDNEgsYUFBYTtZQUMzQ0csTUFDRSxHQUFnRW9ELE9BQTdEdkQsZUFBYyxpREFBeUQsT0FBVnVELE9BQU90RyxFQUFFLEdBQ3pFO2dCQUNFbUQsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxhQUFhO2dCQUNiaUcsTUFBTUMsS0FBS0MsU0FBUyxDQUFDYjtZQUN2QixHQUVDNUosSUFBSSxDQUFDLENBQUN1RTtnQkFDTCxJQUFJQSxJQUFJcEMsTUFBTSxLQUFLLEtBQUs7b0JBQ3RCLE9BQU9vQyxJQUFJQyxJQUFJO2dCQUNqQixPQUFPLElBQUlELElBQUlwQyxNQUFNLEtBQUssS0FBSztvQkFDN0JyRyxpREFBS0EsQ0FBQzJJLEtBQUssQ0FBQztvQkFDWkMsV0FBVzt3QkFDVCxNQUFNQzt3QkFDTixNQUFNK0YsY0FBYyxtQkFFbEIsT0FGcUNDLG1CQUNyQ0MsT0FBT0MsUUFBUSxDQUFDQyxRQUFRO3dCQUUxQjFOLE9BQU9tRCxJQUFJLENBQUNtSztvQkFDZCxHQUFHO29CQUNILE9BQU87Z0JBQ1QsT0FBTztvQkFDTDVPLGlEQUFLQSxDQUFDMkksS0FBSyxDQUFDO29CQUNac0csV0FBVztnQkFDYjtZQUNGLEdBQ0MvSyxJQUFJLENBQUMsQ0FBQ3dFO2dCQUNMLElBQUlBLFFBQVFBLEtBQUt3RyxHQUFHLEVBQUU7b0JBQ3BCbFAsaURBQUtBLENBQUNtUCxPQUFPLENBQUN6RyxLQUFLd0csR0FBRyxFQUFFO3dCQUN0QkUsVUFBVTtvQkFDWjtvQkFDQXhHLFdBQVc7d0JBQ1R0SCxPQUFPK04sT0FBTyxDQUFDO29CQUNqQixHQUFHO2dCQUNMO2dCQUNBcEwsVUFBVUMsSUFBSSxDQUFDLENBQUNDO29CQUNkLE1BQU1DLFVBQVVELGlCQUFBQSwyQkFBQUEsS0FBTUUsTUFBTSxDQUFDLENBQUNDLEtBQUtDO3dCQUNqQyxJQUFJLENBQUNELEdBQUcsQ0FBQ0MsSUFBSUMsVUFBVSxDQUFDLEVBQUU7NEJBQ3hCRixHQUFHLENBQUNDLElBQUlDLFVBQVUsQ0FBQyxHQUFHLEVBQUU7d0JBQzFCO3dCQUNBRixHQUFHLENBQUNDLElBQUlDLFVBQVUsQ0FBQyxDQUFDQyxJQUFJLENBQUNGO3dCQUN6QixPQUFPRDtvQkFDVCxHQUFHLENBQUM7b0JBQ0osTUFBTUksYUFBYTt3QkFDakIsR0FBRzt3QkFDSCxHQUFHO3dCQUNILEdBQUc7d0JBQ0gsR0FBRzt3QkFDSCxHQUFHO29CQUNMO29CQUNBLE1BQU1DLFdBQVcsQ0FBQztvQkFDbEIsTUFBTUMsZ0JBQWdCVCxpQkFBQUEsMkJBQUFBLEtBQU1VLEdBQUcsQ0FBQyxDQUFDTjt3QkFDL0IsTUFBTU8sV0FBV1YsT0FBTyxDQUFDRyxJQUFJQyxVQUFVLENBQUM7d0JBQ3hDLE1BQU1PLFFBQVFELFNBQVNFLFNBQVMsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsZUFBZSxLQUFLWCxJQUFJVyxlQUFlLElBQUk7d0JBRW5GUCxRQUFRLENBQUNKLElBQUlDLFVBQVUsQ0FBQyxHQUFHLENBQUNHLFFBQVEsQ0FBQ0osSUFBSUMsVUFBVSxDQUFDLElBQUksS0FBSzt3QkFDN0QsT0FBTzs0QkFDTFcsSUFBSVosSUFBSVksRUFBRTs0QkFDVlgsWUFBWSxHQUF1QkQsT0FBcEJBLElBQUlDLFVBQVUsRUFBQyxPQUFxQixPQUFoQkQsSUFBSWEsV0FBVzs0QkFDbERDLGVBQWUsSUFBSUMsS0FBS2YsSUFBSWMsYUFBYSxFQUN0Q0UsV0FBVyxHQUNYQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7NEJBQ2hCQyxXQUFXbEIsSUFBSWtCLFNBQVM7NEJBQ3hCQyxjQUFjbkIsSUFBSW1CLFlBQVksSUFBSTs0QkFDbENDLFdBQVdwQixJQUFJcUIsU0FBUzs0QkFDeEJDLGdCQUFnQnRCLElBQUl1QixpQkFBaUI7NEJBQ3JDQyxTQUFTeEIsSUFBSXlCLFFBQVE7NEJBQ3JCRyxNQUFNNUIsSUFBSTRCLElBQUk7NEJBQ2RDLFVBQVU3QixJQUFJNkIsUUFBUTs0QkFDdEJILGlCQUFpQjFCLElBQUkyQixpQkFBaUI7NEJBQ3RDRyxRQUFRM0IsVUFBVSxDQUFDSCxJQUFJK0IsU0FBUyxDQUFDOzRCQUNqQ3BCLGlCQUFpQlgsSUFBSVcsZUFBZTt3QkFFdEM7b0JBQ0Y7b0JBQ0ExRCxrQkFBa0JvRDtvQkFDbEJoQixzQkFBc0I7b0JBQ3RCTixvQkFBb0I7Z0JBQ3RCO1lBQ0Y7UUFDSixFQUFFLE9BQU9xRixPQUFPO1lBQ2RSLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBZ0NPO1FBQzlDO0lBQ0Y7SUFFQSxxQkFDRTs7MEJBQ0UsOERBQUM1SSwwREFBY0E7Z0JBQUN1UCxPQUFPOzs7Ozs7MEJBQ3ZCLDhEQUFDOVAsMERBQU1BO2dCQUFDNkIsVUFBVUE7O2tDQUNoQiw4REFBQ2tPO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O29DQUNYbk8sQ0FBQUEsU0FBU29PLE9BQU8sS0FBSyxLQUNyQnBPLFNBQVNvTyxPQUFPLEtBQUssS0FDckJwTyxTQUFTb08sT0FBTyxLQUFLLG9CQUNyQiw4REFBQ0Y7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNEOzRDQUNDQyxXQUFXLCtEQU9YLE9BTENuTyxTQUFTb08sT0FBTyxLQUFLLEtBQ3JCcE8sU0FBU29PLE9BQU8sS0FBSyxLQUNyQnBPLFNBQVNvTyxPQUFPLEtBQUssSUFDakIsS0FDQSxVQUNMOzs4REFHQSw4REFBQ0M7b0RBQ0NGLFdBQVcseURBSVYsT0FIQ3ZOLGlCQUFpQixXQUNiLCtCQUNBO29EQUVOME4sU0FBUyxJQUFNL0YsbUJBQW1COzhEQUNuQzs7Ozs7OzhEQUdELDhEQUFDOEY7b0RBQ0NGLFdBQVcseURBSVYsT0FIQ3ZOLGlCQUFpQixhQUNiLCtCQUNBO29EQUVOME4sU0FBUzt3REFDUC9GLG1CQUFtQixhQUFhMkQ7b0RBQ2xDOzhEQUNEOzs7Ozs7Ozs7Ozs7Ozs7OztvQ0FNTnBMLDJCQUNDLDhEQUFDb047d0NBQ0MsbUNBQW1DO3dDQUNuQ0MsV0FBVTs7MERBRVYsOERBQUNEOztrRUFDQyw4REFBQ0s7d0RBQU1KLFdBQVU7a0VBQU87Ozs7OztrRUFDeEIsOERBQUNLO3dEQUNDM0csT0FBT25HO3dEQUNQK00sVUFBVSxDQUFDQyxJQUFNOUcsdUJBQXVCOEcsRUFBRTFDLE1BQU0sQ0FBQ25FLEtBQUs7d0RBQ3REc0csV0FBVTs7MEVBRVYsOERBQUNRO2dFQUFPOUcsT0FBTTswRUFBRzs7Ozs7OzREQUFtQjswRUFFcEMsOERBQUM4RztnRUFBTzlHLE9BQU07MEVBQWU7Ozs7OzswRUFDN0IsOERBQUM4RztnRUFBTzlHLE9BQU07MEVBQWM7Ozs7OzswRUFDNUIsOERBQUM4RztnRUFBTzlHLE9BQU07MEVBQXFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBT3ZDLDhEQUFDcUc7O2tFQUNDLDhEQUFDSzt3REFBTUosV0FBVTtrRUFBTzs7Ozs7O2tFQUN4Qiw4REFBQ0s7d0RBQ0MzRyxPQUFPakc7d0RBQ1A2TSxVQUFVLENBQUNDLElBQU1wRyx5QkFBeUJvRyxFQUFFMUMsTUFBTSxDQUFDbkUsS0FBSzt3REFDeERzRyxXQUFVOzswRUFFViw4REFBQ1E7Z0VBQU85RyxPQUFNOzBFQUFHOzs7Ozs7NERBQXNCOzBFQUV2Qyw4REFBQzhHO2dFQUFPOUcsT0FBTTswRUFBaUI7Ozs7OzswRUFDL0IsOERBQUM4RztnRUFBTzlHLE9BQU07MEVBQVc7Ozs7OzswRUFDekIsOERBQUM4RztnRUFBTzlHLE9BQU07MEVBQVc7Ozs7OzswRUFDekIsOERBQUM4RztnRUFBTzlHLE9BQU07MEVBQVU7Ozs7OzswRUFDeEIsOERBQUM4RztnRUFBTzlHLE9BQU07MEVBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFLMUIsOERBQUNxRzs7a0VBQ0MsOERBQUNLO3dEQUFNSixXQUFVO2tFQUFPOzs7Ozs7a0VBQ3hCLDhEQUFDOUI7d0RBQ0NsRSxNQUFLO3dEQUNMTixPQUFPL0Y7d0RBQ1AyTSxVQUFVLENBQUNDLElBQU1oRyx1QkFBdUJnRyxFQUFFMUMsTUFBTSxDQUFDbkUsS0FBSzt3REFDdERzRyxXQUFVOzs7Ozs7Ozs7Ozs7MERBSWQsOERBQUNFO2dEQUNDQyxTQUFTcEM7Z0RBQ1RpQyxXQUFVOzBEQUNYOzs7Ozs7Ozs7Ozs7a0RBS0wsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0k7Z0RBQU1KLFdBQVU7O2tFQUNmLDhEQUFDUzt3REFBS1QsV0FBVTtrRUFDZCw0RUFBQ3RQLDJFQUFlQTs0REFBQ2dRLE1BQU0vUCx3RUFBUUE7NERBQUVxUCxXQUFVOzs7Ozs7Ozs7OztrRUFFN0MsOERBQUM5Qjt3REFDQ2xFLE1BQUs7d0RBQ0xyRSxJQUFHO3dEQUNIZ0wsYUFBWTt3REFDWkMsU0FBU3ZEO3dEQUNUM0QsT0FBT3JIO3dEQUNQMk4sV0FBVTs7Ozs7Ozs7Ozs7OzBEQUdkLDhEQUFDRTtnREFDQ0YsV0FBVTtnREFDVkcsU0FBUztvREFDUCxJQUFJMU4saUJBQWlCLFVBQVU7d0RBQzdCWCxPQUFPbUQsSUFBSSxDQUFDO29EQUNkLE9BQU87d0RBQ0xqQyxrQkFBa0I7d0RBQ2xCRSxvQkFBb0I7b0RBQ3RCO2dEQUNGOzBEQUVDUCxZQUFZLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUluQyw4REFBQzVCLHFFQUFnQkE7Z0NBQ2Y4UCxNQUFNOU47Z0NBQ04rTixjQUFjLENBQUNDLEdBQUdwTTtvQ0FDaEIzQixrQkFBa0IyQixLQUFLa00sSUFBSTtvQ0FDM0IzTixvQkFBb0I7Z0NBQ3RCO2dDQUNBOE4sVUFBVTt3Q0FBQyxFQUFFQyxXQUFXLEVBQUVDLFFBQVEsRUFBRWhKLEtBQUssRUFBRTtvQ0FDekMsZ0NBQWdDO29DQUNoQ1MsUUFBUUMsR0FBRyxDQUFDO3dDQUFFcUk7d0NBQWFDO3dDQUFVaEo7b0NBQU07b0NBQzNDbEYsa0JBQWtCO29DQUNsQmxCLE9BQU9xUCxNQUFNO2dDQUNmO2dDQUNBbE8sa0JBQWtCQTtnQ0FDbEJFLGlCQUFpQkE7Z0NBQ2pCTixjQUFjQTs7Ozs7OzBDQUdoQiw4REFBQ2tOO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FDQ0MsV0FBVTtvQ0FDVm9CLE9BQU87d0NBQUVDLFFBQVE7b0NBQXNCOztzREFFdkMsOERBQUM1USxzREFBV0E7NENBQ1Y2USxTQUFTdlA7NENBQ1R3UCxLQUFLcFA7NENBQ0xnSixtQkFBbUJBOzRDQUNuQjBCLGVBQWVBOzRDQUNmMkUseUJBQXlCOzRDQUN6QkMsWUFBWTs0Q0FDWkMsb0JBQW9CelA7NENBQ3BCMFAsbUJBQW1CbEU7NENBQ25CbUUsa0JBQWtCOzRDQUNsQkMsa0JBQWtCOzRDQUNsQkMsYUFBYTdFOzs7Ozs7c0RBRWYsOERBQUM4Qzs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0k7Z0RBQU0yQixTQUFRO2dEQUE4Qi9CLFdBQVU7O29EQUFTO29EQUN6RDtrRUFDTCw4REFBQ0s7d0RBQ0MxSyxJQUFHO3dEQUNIMkssVUFBVTdDO3dEQUNWL0QsT0FBT3pIO3dEQUNQK04sV0FBVTs7MEVBRVYsOERBQUNRO2dFQUFPOUcsT0FBTzswRUFBSTs7Ozs7OzBFQUNuQiw4REFBQzhHO2dFQUFPOUcsT0FBTzswRUFBSTs7Ozs7OzBFQUNuQiw4REFBQzhHO2dFQUFPOUcsT0FBTzswRUFBSTs7Ozs7OzBFQUNuQiw4REFBQzhHO2dFQUFPOUcsT0FBTzswRUFBSTs7Ozs7OzBFQUNuQiw4REFBQzhHO2dFQUFPOUcsT0FBTzswRUFBSzs7Ozs7Ozs7Ozs7O29EQUNaO29EQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPeEIsOERBQUNqSSx1RUFBY0E7d0JBQ2J1USxPQUFPdFEsc0VBQWFBO3dCQUNwQnNPLFdBQVU7d0JBQ1ZvQixPQUFPOzRCQUFFYSxZQUFZO3dCQUFpQjs7MENBRXRDLDhEQUFDOVEsK0RBQU1BO2dDQUNMMFAsTUFBTWhOO2dDQUNOaU4sY0FBYyxDQUFDQyxHQUFHcE07b0NBQ2hCLElBQUksQ0FBQ0EsS0FBS2tNLElBQUksRUFBRTt3Q0FDZHZOLHVCQUF1Qjt3Q0FDdkJjLHNCQUFzQixLQUFLLHNCQUFzQjtvQ0FDbkQ7b0NBQ0FOLG9CQUFvQmEsS0FBS2tNLElBQUk7Z0NBQy9COztrREFFQSw4REFBQ3JQLHNFQUFhQTt3Q0FBQzBRLHdCQUF3QjtrREFDckMsNEVBQUNuQzs0Q0FBSXFCLE9BQU87Z0RBQUUzRSxTQUFTOzRDQUFPOzs7Ozs7Ozs7OztrREFFaEMsOERBQUNuTCxzRUFBYUE7a0RBQ1osNEVBQUNGLG1FQUFVQTs7OERBQ1QsOERBQUNHLG9FQUFXQTs4REFBQzs7Ozs7OzhEQUNiLDhEQUFDRixzRUFBYUE7O3NFQUNaLDhEQUFDOFE7NERBQUVuQyxXQUFVOztnRUFBTztnRUFDTztnRUFDeEIvTCxpQkFBaUIsSUFDZCxZQUNBQSxpQkFBaUIsSUFDakIsV0FDQUEsaUJBQWlCLElBQ2pCLG9CQUNBO2dFQUFVO2dFQUFJOzs7Ozs7O3dEQUduQkEsaUJBQWlCLG1CQUNoQiw4REFBQ21POzREQUNDcEMsV0FBVTs0REFDVnFDLE1BQUs7NERBQ0wzSSxPQUFPdkY7NERBQ1BtTSxVQUFVLENBQUNDO2dFQUNUbk0sc0JBQXNCbU0sRUFBRTFDLE1BQU0sQ0FBQ25FLEtBQUs7Z0VBQ3BDLElBQUk2RyxFQUFFMUMsTUFBTSxDQUFDbkUsS0FBSyxFQUFFO29FQUNsQnBHLHVCQUF1QjtnRUFDekI7NERBQ0Y7NERBQ0FnUCxRQUFRLENBQUMvQjtnRUFDUCxNQUFNZ0MsZUFBZXRFLGNBQWNzQyxFQUFFMUMsTUFBTSxDQUFDbkUsS0FBSztnRUFDakR0RixzQkFBc0JtTzs0REFDeEI7NERBQ0E1QixhQUFZOzREQUNaNkIsV0FBVTs7Ozs7O3dEQUdidk8saUJBQWlCLEtBQUssQ0FBQ1oscUNBQ3RCLDhEQUFDb047NERBQUtULFdBQVU7c0VBQWU7Ozs7OztzRUFJakMsOERBQUNEOzREQUNDcUIsT0FBTztnRUFBRTNFLFNBQVM7Z0VBQVFnRyxLQUFLO2dFQUFPQyxXQUFXOzREQUFPOzREQUN4RDFDLFdBQVU7OzhFQUVWLDhEQUFDRTtvRUFDQ0MsU0FBUzt3RUFBT3JNLG9CQUFvQjt3RUFBUU0sc0JBQXNCO29FQUFHO29FQUNyRXVPLG1CQUFnQjtvRUFDaEIzSSxNQUFLO29FQUNMZ0csV0FBVTs4RUFDWDs7Ozs7OzhFQUdELDhEQUFDRTtvRUFDQ0MsU0FBUzt3RUFDUCxrQ0FBa0M7d0VBQ2xDeEgsUUFBUUMsR0FBRyxDQUNULG9CQUNBM0UsY0FDQUY7d0VBRUZxSyx3QkFDRW5LLGNBQ0FFLG9CQUNBSjtvRUFFSjtvRUFDQTRPLG1CQUFnQjtvRUFDaEIzSSxNQUFLO29FQUNMZ0csV0FBVTs4RUFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBT0Q7Ozs7Ozs7Ozs7Ozs7OztBQUtwQjtHQTk4Qk1wTzs7UUFFV2YsbURBQVNBO1FBS0NELHFFQUFVQTs7O0tBUC9CZ0I7O0FBZzlCTiwrREFBZUEsY0FBY0EsRUFBQztBQWdFOUIsdURBQXVEO0FBQ3ZELFNBQVN5Ryw4QkFBOEJ1SyxlQUFlO0lBQ3BELElBQUksQ0FBQ0EsaUJBQWlCLE9BQU87SUFDN0IsTUFBTUMsUUFBUUQsZ0JBQWdCRSxXQUFXO0lBQ3pDLElBQUlELE1BQU1FLFFBQVEsQ0FBQyxRQUFRLE9BQU87SUFDbEMsSUFBSUYsTUFBTUUsUUFBUSxDQUFDLFNBQVMsT0FBTztJQUNuQyxJQUFJRixNQUFNRSxRQUFRLENBQUMsU0FBUyxPQUFPO0lBQ25DLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9wYWdlcy9wcGUtY29uc3VtYWJsZS5qcz8xMTM4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBMYXlvdXQgZnJvbSBcIkAvY29tcG9uZW50cy9MYXlvdXRcIjtcclxuaW1wb3J0IFJlYWN0LCB7XHJcbiAgdXNlU3RhdGUsXHJcbiAgdXNlUmVmLFxyXG4gIHVzZUVmZmVjdCxcclxuICB1c2VNZW1vLFxyXG4gIHVzZUNhbGxiYWNrLFxyXG59IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBUb2FzdENvbnRhaW5lciwgdG9hc3QgfSBmcm9tIFwicmVhY3QtdG9hc3RpZnlcIjtcclxuaW1wb3J0IFwicmVhY3QtdG9hc3RpZnkvZGlzdC9SZWFjdFRvYXN0aWZ5LmNzc1wiO1xyXG5pbXBvcnQgeyBBZ0dyaWRSZWFjdCB9IGZyb20gXCJhZy1ncmlkLXJlYWN0XCI7XHJcbmltcG9ydCBcImFnLWdyaWQtY29tbXVuaXR5L3N0eWxlcy8vYWctZ3JpZC5jc3NcIjtcclxuaW1wb3J0IFwiYWctZ3JpZC1jb21tdW5pdHkvc3R5bGVzLy9hZy10aGVtZS1hbHBpbmUuY3NzXCI7XHJcbmltcG9ydCB7IEZvbnRBd2Vzb21lSWNvbiB9IGZyb20gXCJAZm9ydGF3ZXNvbWUvcmVhY3QtZm9udGF3ZXNvbWVcIjtcclxuaW1wb3J0IHsgZmFTZWFyY2ggfSBmcm9tIFwiQGZvcnRhd2Vzb21lL2ZyZWUtc29saWQtc3ZnLWljb25zXCI7XHJcbmltcG9ydCB7IHVzZUxvYWRpbmcgfSBmcm9tIFwiQC91dGlscy9sb2FkZXJzL2xvYWRpbmdDb250ZXh0XCI7XHJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gXCJuZXh0L3JvdXRlclwiO1xyXG5pbXBvcnQgeyBhcGlDb25maWcgfSBmcm9tIFwiQC9zZXJ2aWNlcy9hcGlDb25maWdcIjtcclxuaW1wb3J0IEFkZFByb2R1Y3REaWFsb2cgZnJvbSBcIkAvY29tcG9uZW50cy9BZGRQcm9kdWN0RGlhbG9nXCI7XHJcbmltcG9ydCBwcm9kdWN0U3RhdHVzUmVuZGVyZXIgZnJvbSBcIkAvdXRpbHMvcmVuZGVyZXIvcHJvZHVjdFN0YXR1c1JlbmRlcmVyXCI7XHJcbmltcG9ydCBwcGVBY3Rpb25SZW5kZXJlciBmcm9tIFwiQC91dGlscy9yZW5kZXJlci9wcGVBY3Rpb25SZW5kZXJlclwiO1xyXG5pbXBvcnQge1xyXG4gIEJ1dHRvbixcclxuICBEaWFsb2csXHJcbiAgRGlhbG9nQm9keSxcclxuICBEaWFsb2dDb250ZW50LFxyXG4gIERpYWxvZ1N1cmZhY2UsXHJcbiAgRGlhbG9nVGl0bGUsXHJcbiAgRGlhbG9nVHJpZ2dlcixcclxuICBGbHVlbnRQcm92aWRlcixcclxuICB3ZWJMaWdodFRoZW1lLFxyXG59IGZyb20gXCJAZmx1ZW50dWkvcmVhY3QtY29tcG9uZW50c1wiO1xyXG5pbXBvcnQgcHJvZHVjdEFjdGl2ZVJlbmRlcmVyIGZyb20gXCJAL3V0aWxzL3JlbmRlcmVyL3BwZUFjdGl2ZVJlbmRlcmVyXCI7XHJcblxyXG5jb25zdCBQUEVDb25zdW1hYmxlcyA9ICh7IHVzZXJEYXRhIH0pID0+IHtcclxuICAvLyBjb25zb2xlLmxvZyhcIlBBUkVUTiBjb21wb25lbnQgcmVuZGVyZWRcIiwgdXNlckRhdGEpO1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIC8vIGNvbnN0IFtyb3dEYXRhLCBzZXRSb3dEYXRhXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbcmVxdWVzdFJvd0RhdGEsIHNldFJlcXVlc3RSb3dEYXRhXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbcGFnZVNpemUsIHNldFBhZ2VTaXplXSA9IHVzZVN0YXRlKDE1KTtcclxuICBjb25zdCBncmlkUmVmID0gdXNlUmVmKCk7XHJcbiAgY29uc3QgeyBzZXRJc0xvYWRpbmcgfSA9IHVzZUxvYWRpbmcoKTtcclxuICBjb25zdCBbc2VhcmNoSW5wdXQsIHNldFNlYXJjaElucHV0XSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtibG9ja1NjcmVlbiwgc2V0QmxvY2tTY3JlZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZFZpZXcsIHNldFNlbGVjdGVkVmlld10gPSB1c2VTdGF0ZShudWxsKTtcclxuICBjb25zdCBbb3JkZXJWaWV3LCBzZXRPcmRlclZpZXddID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgW3Byb2R1Y3RUeXBlcywgc2V0UHJvZHVjdFR5cGVzXSA9IHVzZVN0YXRlKFtdKTtcclxuXHJcbiAgLy8jcmVnaW9uIHBvcCB1cCBzdGF0ZXNcclxuICBjb25zdCBbc2hvd0FkZFByb2R1Y3QsIHNldFNob3dBZGRQcm9kdWN0XSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNFZGl0aW5nUHJvZHVjdCwgc2V0SXNFZGl0aW5nUHJvZHVjdF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2VkaXRQcm9kdWN0RGF0YSwgc2V0RWRpdFByb2R1Y3REYXRhXSA9IHVzZVN0YXRlKCk7XHJcbiAgY29uc3QgW2lzVmFsaWRDYW5jZWxSZWFzb24sIHNldElzVmFsaWRDYW5jZWxSZWFzb25dID0gdXNlU3RhdGUodHJ1ZSk7XHJcblxyXG4gIC8vIENvbnRyb2xsZWQgZmlsdGVyIHN0YXRlc1xyXG4gIGNvbnN0IFtzaXRlRmlsdGVyLCBzZXRTaXRlRmlsdGVyXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtzdGF0dXNGaWx0ZXIsIHNldFN0YXR1c0ZpbHRlcl0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbZGF0ZUZpbHRlciwgc2V0RGF0ZUZpbHRlcl0gPSB1c2VTdGF0ZShcIlwiKTtcclxuXHJcbiAgY29uc3QgW3Nob3dTdGF0dXNEaWFsb2csIHNldFNob3dTdGF0dXNEaWFsb2ddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZFJvd0RhdGEsIHNldFNlbGVjdGVkUm93RGF0YV0gPSB1c2VTdGF0ZShudWxsKTtcclxuICBjb25zdCBbc3RhdHVzQWN0aW9uLCBzZXRTdGF0dXNBY3Rpb25dID0gdXNlU3RhdGUobnVsbCk7XHJcbiAgY29uc3QgW2NhbmNlbGxlZFJlYXNvbmFwaSwgc2V0Q2FuY2VsbGVkUmVhc29uYXBpXSA9IHVzZVN0YXRlKG51bGwpO1xyXG5cclxuICAvLyBMb2FkIHNhdmVkIHZpZXcgZnJvbSBsb2NhbFN0b3JhZ2Ugb24gY29tcG9uZW50IG1vdW50XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIE9ubHkgcnVuIG9uIGNsaWVudCBzaWRlXHJcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIikge1xyXG4gICAgICBjb25zdCBzYXZlZFZpZXcgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcInBwZVNlbGVjdGVkVmlld1wiKTtcclxuICAgICAgaWYgKHNhdmVkVmlldyAmJiAoc2F2ZWRWaWV3ID09PSBcIk9yZGVyc1wiIHx8IHNhdmVkVmlldyA9PT0gXCJQcm9kdWN0c1wiKSkge1xyXG4gICAgICAgIHNldFNlbGVjdGVkVmlldyhzYXZlZFZpZXcpO1xyXG4gICAgICAgIHNldE9yZGVyVmlldyhzYXZlZFZpZXcgPT09IFwiT3JkZXJzXCIpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNldFNlbGVjdGVkVmlldyhcIk9yZGVyc1wiKTtcclxuICAgICAgICBzZXRPcmRlclZpZXcodHJ1ZSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LCBbXSk7IC8vIEVtcHR5IGRlcGVuZGVuY3kgYXJyYXkgLSBydW5zIG9ubHkgb24gbW91bnRcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmICghc2VsZWN0ZWRWaWV3KSByZXR1cm47XHJcbiAgICBjb25zdCBmZXRjaFByb2R1Y3RzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICBpZiAoc2VsZWN0ZWRWaWV3ID09PSBcIk9yZGVyc1wiKSB7XHJcbiAgICAgICAgZ2V0RGF0YSgpLnRoZW4oKGRhdGEpID0+IHtcclxuICAgICAgICAgIC8vIGNvbnNvbGUubG9nKFwiZGF0YVwiLCBkYXRhKTtcclxuICAgICAgICAgIGNvbnN0IGdyb3VwZWQgPSBkYXRhPy5yZWR1Y2UoKGFjYywgcm93KSA9PiB7XHJcbiAgICAgICAgICAgICAgaWYgKCFhY2Nbcm93LnJlcXVlc3RfaWRdKSB7XHJcbiAgICAgICAgICAgICAgICBhY2Nbcm93LnJlcXVlc3RfaWRdID0gW107XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIGFjY1tyb3cucmVxdWVzdF9pZF0ucHVzaChyb3cpO1xyXG4gICAgICAgICAgICAgIHJldHVybiBhY2M7XHJcbiAgICAgICAgICAgIH0sIHt9KTtcclxuICAgICAgICAgIGNvbnN0IFNUQVRVU19NQVAgPSB7XHJcbiAgICAgICAgICAgIDE6IFwiUGVuZGluZyBSZXZpZXdcIixcclxuICAgICAgICAgICAgMjogXCJBcHByb3ZlZFwiLFxyXG4gICAgICAgICAgICAzOiBcIlJlamVjdGVkXCIsXHJcbiAgICAgICAgICAgIDQ6IFwiT3JkZXJlZFwiLFxyXG4gICAgICAgICAgICA1OiBcIkRyYWZ0XCIsXHJcbiAgICAgICAgICB9O1xyXG4gICAgICAgICAgY29uc3QgY291bnRlcnMgPSB7fTtcclxuICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZERhdGEgPSBkYXRhPy5tYXAoKHJvdykgPT4ge1xyXG4gICAgICAgICAgICBjb3VudGVyc1tyb3cucmVxdWVzdF9pZF0gPSAoY291bnRlcnNbcm93LnJlcXVlc3RfaWRdIHx8IDApICsgMTtcclxuICAgICAgICAgICAgY29uc3Qgc2libGluZ3MgPSBncm91cGVkW3Jvdy5yZXF1ZXN0X2lkXTtcclxuICAgICAgICAgICAgICBjb25zdCBpbmRleCA9IHNpYmxpbmdzLmZpbmRJbmRleChyID0+IHIucmVxdWVzdF9pdGVtX2lkID09PSByb3cucmVxdWVzdF9pdGVtX2lkKSArIDE7XHJcblxyXG4gICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgIGlkOiByb3cuaWQsXHJcbiAgICAgICAgICAgICAgcmVxdWVzdF9pZDogYCR7cm93LnJlcXVlc3RfaWR9IC0gJHtyb3cuaXRlbV9udW1iZXJ9YCwgLy8gc3RhYmxlIHBlciByZXF1ZXN0X2lkXHJcbiAgICAgICAgICAgICAgcmVxdWlyZWRfZGF0ZTogbmV3IERhdGUocm93LnJlcXVpcmVkX2RhdGUpXHJcbiAgICAgICAgICAgICAgICAudG9JU09TdHJpbmcoKVxyXG4gICAgICAgICAgICAgICAgLnNwbGl0KFwiVFwiKVswXSxcclxuICAgICAgICAgICAgICBzaXRlX25hbWU6IHJvdy5zaXRlX25hbWUsXHJcbiAgICAgICAgICAgICAgcHJvZHVjdF9uYW1lOiByb3cucHJvZHVjdF9uYW1lIHx8IFwiVW5uYW1lZCBQcm9kdWN0XCIsXHJcbiAgICAgICAgICAgICAgcmVxdWVzdG9yOiByb3cudXNlcl9uYW1lLFxyXG4gICAgICAgICAgICAgIG9yaWduYXRvckVtYWlsOiByb3cuYWN0aW9uZWRfYnlfZW1haWwsXHJcbiAgICAgICAgICAgICAgY29tbWVudDogcm93LmNvbW1lbnRzLFxyXG4gICAgICAgICAgICAgIE5hbWVGb3JQcmludGluZzpyb3cubmFtZV9mb3JfcHJpbnRpbmcsXHJcbiAgICAgICAgICAgICAgc2l6ZTogcm93LnNpemUsXHJcbiAgICAgICAgICAgICAgcXVhbnRpdHk6IHJvdy5xdWFudGl0eSxcclxuICAgICAgICAgICAgICBzdGF0dXM6IFNUQVRVU19NQVBbcm93LmFjdGlvbl9pZF0sXHJcbiAgICAgICAgICAgICAgcmVxdWVzdF9pdGVtX2lkOiByb3cucmVxdWVzdF9pdGVtX2lkLFxyXG4gICAgICAgICAgICB9O1xyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgICAvLyBjb25zb2xlLmxvZyhcIkZvcm1hdHRlZCBEYXRhOlwiLCBmb3JtYXR0ZWREYXRhKTtcclxuICAgICAgICAgIC8vIHNldFJvd0RhdGEoZm9ybWF0dGVkRGF0YSk7XHJcbiAgICAgICAgICBzZXRSZXF1ZXN0Um93RGF0YShmb3JtYXR0ZWREYXRhKTtcclxuICAgICAgICB9KTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zdCBbcHJvZHVjdHNKc29uLCBwcm9kdWN0VHlwZXNKc29uXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcclxuICAgICAgICAgIGdldEFsbFByb2R1Y3RzKCksXHJcbiAgICAgICAgICBnZXRQcm9kdWN0VHlwZXMoKSxcclxuICAgICAgICBdKTtcclxuXHJcbiAgICAgICAgLy8gY29uc29sZS5sb2coXCJGZXRjaGVkIFByb2R1Y3RzIEpTT046XCIsIHByb2R1Y3RzSnNvbik7XHJcbiAgICAgICAgLy8gY29uc29sZS5sb2coXCJGZXRjaGVkIFByb2R1Y3QgVHlwZXMgSlNPTjpcIiwgcHJvZHVjdFR5cGVzSnNvbik7XHJcblxyXG4gICAgICAgIC8vIFNhdmUgcHJvZHVjdCB0eXBlcyBmb3IgbGF0ZXIgdXNlIChlLmcuLCBmaWx0ZXJzIG9yIGRpYWxvZ3MpXHJcbiAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkocHJvZHVjdFR5cGVzSnNvbikpIHtcclxuICAgICAgICAgIHNldFByb2R1Y3RUeXBlcyhwcm9kdWN0VHlwZXNKc29uKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgc2V0UHJvZHVjdFR5cGVzKFtdKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgLy8gY29uc29sZS5sb2coXCJGZXRjaGVkIFByb2R1Y3RzIEpTT046XCIsIHByb2R1Y3RzSnNvbik7XHJcbiAgICAgICAgaWYgKHByb2R1Y3RzSnNvbiAmJiBwcm9kdWN0c0pzb24ubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgLy8gY29uc29sZS5sb2coXHJcbiAgICAgICAgICAvLyAgIFwiRmV0Y2hlZCBQcm9kdWN0cyBKU09OIGluc2lkZSBjb2RlIGV4ZWN1dGlvblwiLFxyXG4gICAgICAgICAgLy8gICBwcm9kdWN0c0pzb25cclxuICAgICAgICAgIC8vICk7XHJcbiAgICAgICAgICBjb25zdCBmb3JtYXR0ZWRQcm9kdWN0RGF0YSA9IHByb2R1Y3RzSnNvbi5tYXAoKHByb2R1Y3QpID0+ICh7XHJcbiAgICAgICAgICAgIHByb2R1Y3RfaWQ6IHByb2R1Y3QuUHJvZHVjdElkLFxyXG4gICAgICAgICAgICBwcm9kdWN0X25hbWU6IHByb2R1Y3QuUHJvZHVjdE5hbWUgfHwgXCJVbm5hbWVkIFByb2R1Y3RcIixcclxuICAgICAgICAgICAgY2F0ZWdvcnk6IHByb2R1Y3QuUHJvZHVjdFR5cGUgfHwgXCJVbmNhdGVnb3JpemVkXCIsXHJcbiAgICAgICAgICAgIHR5cGVfaWQ6IHByb2R1Y3QuVHlwZUlkLFxyXG4gICAgICAgICAgICBzdG9jazogMCxcclxuICAgICAgICAgICAgc2l6ZXM6IHByb2R1Y3QuQXZhaWxhYmxlU2l6ZXMgfHwgXCJPbmUgU2l6ZVwiLFxyXG4gICAgICAgICAgICB1bml0OiBkZXJpdmVVbml0RnJvbVBhY2thZ2VRdWFudGl0eShwcm9kdWN0LlBhY2thZ2VRdWFudGl0eSksXHJcbiAgICAgICAgICAgIG5hbWVfcHJpbnRhYmxlOiBwcm9kdWN0Lm5hbWVfcHJpbnRhYmxlLFxyXG4gICAgICAgICAgICBJc1Byb2R1Y3RIaWRkZW46IHByb2R1Y3QuSXNQcm9kdWN0SGlkZGVuLFxyXG4gICAgICAgICAgfSkpO1xyXG4gICAgICAgICAgLy8gc2V0Um93RGF0YShmb3JtYXR0ZWRQcm9kdWN0RGF0YSk7XHJcbiAgICAgICAgICBzZXRSZXF1ZXN0Um93RGF0YShmb3JtYXR0ZWRQcm9kdWN0RGF0YSk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGNvbnN0IGZhbGxiYWNrUHJvZHVjdERhdGEgPSBbXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICBwcm9kdWN0X25hbWU6IFwiTml0cmlsZSBHbG92ZXNcIixcclxuICAgICAgICAgICAgICBjYXRlZ29yeTogXCJHbG92ZXNcIixcclxuICAgICAgICAgICAgICBzdG9jazogMTIwMCxcclxuICAgICAgICAgICAgICB1bml0OiBcIkJveFwiLFxyXG4gICAgICAgICAgICAgIHN0YXR1czogXCJBdmFpbGFibGVcIixcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgIF07XHJcbiAgICAgICAgICAvLyBzZXRSb3dEYXRhKGZhbGxiYWNrUHJvZHVjdERhdGEpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfTtcclxuICAgIGZldGNoUHJvZHVjdHMoKTtcclxuICB9LCBbc2VsZWN0ZWRWaWV3XSk7XHJcblxyXG4gIC8vICNyZWdpb24gZ2V0IGRhdGFcclxuICBjb25zdCBnZXREYXRhID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgLy8gc2V0Um93RGF0YShbXSk7XHJcbiAgICBzZXRSZXF1ZXN0Um93RGF0YShbXSk7XHJcbiAgICBsZXQgc2VydmVyQWRkcmVzcyA9IGFwaUNvbmZpZy5zZXJ2ZXJBZGRyZXNzO1xyXG4gICAgY29uc29sZS5sb2coXCJcIik7XHJcbiAgICByZXR1cm4gZmV0Y2goYCR7c2VydmVyQWRkcmVzc31wcGUtY29uc3VtYWJsZXMvYWxsLXBwZS1yZXF1ZXN0c2AsIHtcclxuICAgICAgbWV0aG9kOiBcIkdFVFwiLFxyXG4gICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgIH0sXHJcbiAgICAgIGNyZWRlbnRpYWxzOiBcImluY2x1ZGVcIiwgLy8gVXNlIHNlc3Npb24gYXV0aGVudGljYXRpb25cclxuICAgIH0pXHJcbiAgICAgIC50aGVuKGFzeW5jIChyZXMpID0+IHtcclxuICAgICAgICBpZiAocmVzLnN0YXR1cyA9PSA1MDIpIHtcclxuICAgICAgICAgIHNldEJsb2NrU2NyZWVuKHRydWUpO1xyXG4gICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgIH1cclxuICAgICAgICBzZXRCbG9ja1NjcmVlbihmYWxzZSk7XHJcbiAgICAgICAgaWYgKHJlcy5zdGF0dXMgPT09IDIwMCkge1xyXG4gICAgICAgICAgcmV0dXJuIHJlcy5qc29uKCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChyZXMuc3RhdHVzID09PSA0MDApIHtcclxuICAgICAgICAgIHRvYXN0LmVycm9yKFxyXG4gICAgICAgICAgICBcIlRoZXJlIHdhcyBhbiBlcnJvciB3aXRoIHlvdXIgcmVxdWVzdC4gUGxlYXNlIGNoZWNrIHlvdXIgZGF0YSBhbmQgdHJ5IGFnYWluLlwiXHJcbiAgICAgICAgICApO1xyXG4gICAgICAgIH0gZWxzZSBpZiAocmVzLnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICB0b2FzdC5lcnJvcihcIllvdXIgc2Vzc2lvbiBoYXMgZXhwaXJlZC4gUGxlYXNlIGxvZyBpbiBhZ2Fpbi5cIik7XHJcbiAgICAgICAgICBzZXRUaW1lb3V0KGFzeW5jICgpID0+IHtcclxuICAgICAgICAgICAgYXdhaXQgbG9nb3V0KCk7XHJcbiAgICAgICAgICAgIHJvdXRlci5wdXNoKFwiL2xvZ2luXCIpO1xyXG4gICAgICAgICAgfSwgMzAwMCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBmZXRjaCBkYXRhXCIpO1xyXG4gICAgICB9KVxyXG4gICAgICAuY2F0Y2goKGVycm9yKSA9PiB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihlcnJvcik7XHJcbiAgICAgIH0pO1xyXG4gIH07XHJcbiAgYXN5bmMgZnVuY3Rpb24gZ2V0QWxsUHJvZHVjdHMoKSB7XHJcbiAgICAvLyBzZXRSb3dEYXRhKFtdKTtcclxuICAgIGxldCBzZXJ2ZXJBZGRyZXNzID0gYXBpQ29uZmlnLnNlcnZlckFkZHJlc3M7XHJcbiAgICByZXR1cm4gZmV0Y2goYCR7c2VydmVyQWRkcmVzc31wcGUtY29uc3VtYWJsZXMvYWxsLXByb2R1Y3RzYCwge1xyXG4gICAgICBtZXRob2Q6IFwiR0VUXCIsXHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcclxuICAgICAgfSxcclxuICAgICAgY3JlZGVudGlhbHM6IFwiaW5jbHVkZVwiLCAvLyBVc2Ugc2Vzc2lvbiBhdXRoZW50aWNhdGlvblxyXG4gICAgfSlcclxuICAgICAgLnRoZW4oYXN5bmMgKHJlcykgPT4ge1xyXG4gICAgICAgIGlmIChyZXMuc3RhdHVzID09IDUwMikge1xyXG4gICAgICAgICAgc2V0QmxvY2tTY3JlZW4odHJ1ZSk7XHJcbiAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHNldEJsb2NrU2NyZWVuKGZhbHNlKTtcclxuICAgICAgICBpZiAocmVzLnN0YXR1cyA9PT0gMjAwKSB7XHJcbiAgICAgICAgICByZXR1cm4gcmVzLmpzb24oKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHJlcy5zdGF0dXMgPT09IDQwMCkge1xyXG4gICAgICAgICAgdG9hc3QuZXJyb3IoXHJcbiAgICAgICAgICAgIFwiVGhlcmUgd2FzIGFuIGVycm9yIHdpdGggeW91ciByZXF1ZXN0LiBQbGVhc2UgY2hlY2sgeW91ciBkYXRhIGFuZCB0cnkgYWdhaW4uXCJcclxuICAgICAgICAgICk7XHJcbiAgICAgICAgfSBlbHNlIGlmIChyZXMuc3RhdHVzID09PSA0MDEpIHtcclxuICAgICAgICAgIHRvYXN0LmVycm9yKFwiWW91ciBzZXNzaW9uIGhhcyBleHBpcmVkLiBQbGVhc2UgbG9nIGluIGFnYWluLlwiKTtcclxuICAgICAgICAgIHNldFRpbWVvdXQoYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgICBhd2FpdCBsb2dvdXQoKTtcclxuICAgICAgICAgICAgcm91dGVyLnB1c2goXCIvbG9naW5cIik7XHJcbiAgICAgICAgICB9LCAzMDAwKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIGZldGNoIHByb2R1Y3RzXCIpO1xyXG4gICAgICB9KVxyXG4gICAgICAuY2F0Y2goKGVycm9yKSA9PiB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihlcnJvcik7XHJcbiAgICAgICAgdG9hc3QuZXJyb3IoYEZhaWxlZCB0byBmZXRjaCBwcm9kdWN0czogJHtlcnJvci5tZXNzYWdlfWApO1xyXG4gICAgICAgIHRocm93IGVycm9yO1xyXG4gICAgICB9KTtcclxuICB9XHJcblxyXG4gIGFzeW5jIGZ1bmN0aW9uIGdldFByb2R1Y3RUeXBlcygpIHtcclxuICAgIGxldCBzZXJ2ZXJBZGRyZXNzID0gYXBpQ29uZmlnLnNlcnZlckFkZHJlc3M7XHJcbiAgICByZXR1cm4gZmV0Y2goYCR7c2VydmVyQWRkcmVzc31wcGUtY29uc3VtYWJsZXMvZ2V0LXByb2R1Y3RfdHlwZXNgLCB7XHJcbiAgICAgIG1ldGhvZDogXCJHRVRcIixcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICB9LFxyXG4gICAgICBjcmVkZW50aWFsczogXCJpbmNsdWRlXCIsXHJcbiAgICB9KVxyXG4gICAgICAudGhlbihhc3luYyAocmVzKSA9PiB7XHJcbiAgICAgICAgaWYgKHJlcy5zdGF0dXMgPT0gNTAyKSB7XHJcbiAgICAgICAgICBzZXRCbG9ja1NjcmVlbih0cnVlKTtcclxuICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgICAgc2V0QmxvY2tTY3JlZW4oZmFsc2UpO1xyXG4gICAgICAgIGlmIChyZXMuc3RhdHVzID09PSAyMDApIHtcclxuICAgICAgICAgIHJldHVybiByZXMuanNvbigpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAocmVzLnN0YXR1cyA9PT0gNDAwKSB7XHJcbiAgICAgICAgICB0b2FzdC5lcnJvcihcclxuICAgICAgICAgICAgXCJUaGVyZSB3YXMgYW4gZXJyb3Igd2l0aCB5b3VyIHJlcXVlc3QuIFBsZWFzZSBjaGVjayB5b3VyIGRhdGEgYW5kIHRyeSBhZ2Fpbi5cIlxyXG4gICAgICAgICAgKTtcclxuICAgICAgICB9IGVsc2UgaWYgKHJlcy5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgICAgdG9hc3QuZXJyb3IoXCJZb3VyIHNlc3Npb24gaGFzIGV4cGlyZWQuIFBsZWFzZSBsb2cgaW4gYWdhaW4uXCIpO1xyXG4gICAgICAgICAgc2V0VGltZW91dChhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICAgIGF3YWl0IGxvZ291dCgpO1xyXG4gICAgICAgICAgICByb3V0ZXIucHVzaChcIi9sb2dpblwiKTtcclxuICAgICAgICAgIH0sIDMwMDApO1xyXG4gICAgICAgIH1cclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggcHJvZHVjdCB0eXBlc1wiKTtcclxuICAgICAgfSlcclxuICAgICAgLmNhdGNoKChlcnJvcikgPT4ge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IpO1xyXG4gICAgICAgIHRvYXN0LmVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggcHJvZHVjdCB0eXBlczogJHtlcnJvci5tZXNzYWdlfWApO1xyXG4gICAgICAgIHRocm93IGVycm9yO1xyXG4gICAgICB9KTtcclxuICB9XHJcblxyXG4gIC8vI3JlZ2lvbiBjb2x1bW4gZGVmXHJcbiAgLy8gRXhhbXBsZSBjb2x1bW4gZGVmaW5pdGlvbnMgZm9yIFByb2R1Y3RzXHJcbiAgLy8gUmVwbGFjZSB3aXRoIHByb3BlciBmaWx0ZXIgaGFuZGxpbmdcclxuICBjb25zdCBoYW5kbGVTaXRlRmlsdGVyQ2hhbmdlID0gKHZhbHVlKSA9PiB7XHJcbiAgICBzZXRTaXRlRmlsdGVyKHZhbHVlKTtcclxuICAgIGlmIChncmlkUmVmLmN1cnJlbnQgJiYgZ3JpZFJlZi5jdXJyZW50LmFwaSkge1xyXG4gICAgICBjb25zdCBmaWx0ZXJJbnN0YW5jZSA9IGdyaWRSZWYuY3VycmVudC5hcGkuZ2V0RmlsdGVySW5zdGFuY2UoXCJzaXRlX25hbWVcIik7XHJcbiAgICAgIGlmIChmaWx0ZXJJbnN0YW5jZSkge1xyXG4gICAgICAgIGlmICh2YWx1ZSkge1xyXG4gICAgICAgICAgZmlsdGVySW5zdGFuY2Uuc2V0TW9kZWwoe1xyXG4gICAgICAgICAgICB0eXBlOiBcImVxdWFsc1wiLFxyXG4gICAgICAgICAgICBmaWx0ZXI6IHZhbHVlLFxyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGZpbHRlckluc3RhbmNlLnNldE1vZGVsKG51bGwpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBncmlkUmVmLmN1cnJlbnQuYXBpLm9uRmlsdGVyQ2hhbmdlZCgpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU3RhdHVzRmlsdGVyQ2hhbmdlID0gKHZhbHVlKSA9PiB7XHJcbiAgICBzZXRTdGF0dXNGaWx0ZXIodmFsdWUpO1xyXG4gICAgaWYgKGdyaWRSZWYuY3VycmVudCAmJiBncmlkUmVmLmN1cnJlbnQuYXBpKSB7XHJcbiAgICAgIGNvbnN0IGZpbHRlckluc3RhbmNlID0gZ3JpZFJlZi5jdXJyZW50LmFwaS5nZXRGaWx0ZXJJbnN0YW5jZShcInN0YXR1c1wiKTtcclxuICAgICAgaWYgKGZpbHRlckluc3RhbmNlKSB7XHJcbiAgICAgICAgaWYgKHZhbHVlKSB7XHJcbiAgICAgICAgICBmaWx0ZXJJbnN0YW5jZS5zZXRNb2RlbCh7XHJcbiAgICAgICAgICAgIHR5cGU6IFwiZXF1YWxzXCIsXHJcbiAgICAgICAgICAgIGZpbHRlcjogdmFsdWUsXHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgZmlsdGVySW5zdGFuY2Uuc2V0TW9kZWwobnVsbCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGdyaWRSZWYuY3VycmVudC5hcGkub25GaWx0ZXJDaGFuZ2VkKCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVTZWxlY3RlZFZpZXcgPSAoc2VsZWN0ZWRUeXBlKSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZFZpZXcoc2VsZWN0ZWRUeXBlKTtcclxuICAgIHNldE9yZGVyVmlldyhzZWxlY3RlZFR5cGUgPT09IFwiT3JkZXJzXCIpO1xyXG4gICAgLy8gT25seSBzYXZlIHRvIGxvY2FsU3RvcmFnZSBvbiBjbGllbnQgc2lkZVxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIpIHtcclxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJwcGVTZWxlY3RlZFZpZXdcIiwgc2VsZWN0ZWRUeXBlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVEYXRlRmlsdGVyQ2hhbmdlID0gKHZhbHVlKSA9PiB7XHJcbiAgICBzZXREYXRlRmlsdGVyKHZhbHVlKTtcclxuICAgIGlmIChncmlkUmVmLmN1cnJlbnQgJiYgZ3JpZFJlZi5jdXJyZW50LmFwaSkge1xyXG4gICAgICBjb25zdCBmaWx0ZXJJbnN0YW5jZSA9XHJcbiAgICAgICAgZ3JpZFJlZi5jdXJyZW50LmFwaS5nZXRGaWx0ZXJJbnN0YW5jZShcInJlcXVpcmVkX2RhdGVcIik7XHJcbiAgICAgIGlmIChmaWx0ZXJJbnN0YW5jZSkge1xyXG4gICAgICAgIGlmICh2YWx1ZSkge1xyXG4gICAgICAgICAgY29uc3Qgc2VsZWN0ZWREYXRlID0gbmV3IERhdGUodmFsdWUpO1xyXG4gICAgICAgICAgY29uc3QgeXl5eSA9IHNlbGVjdGVkRGF0ZS5nZXRGdWxsWWVhcigpO1xyXG4gICAgICAgICAgY29uc3QgbW0gPSBTdHJpbmcoc2VsZWN0ZWREYXRlLmdldE1vbnRoKCkgKyAxKS5wYWRTdGFydCgyLCBcIjBcIik7XHJcbiAgICAgICAgICBjb25zdCBkZCA9IFN0cmluZyhzZWxlY3RlZERhdGUuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCBcIjBcIik7XHJcblxyXG4gICAgICAgICAgY29uc3QgZm9ybWF0dGVkRGF0ZSA9IGAke3l5eXl9LSR7bW19LSR7ZGR9YDtcclxuXHJcbiAgICAgICAgICBmaWx0ZXJJbnN0YW5jZS5zZXRNb2RlbCh7XHJcbiAgICAgICAgICAgIHR5cGU6IFwiZXF1YWxzXCIsXHJcbiAgICAgICAgICAgIGRhdGVGcm9tOiBmb3JtYXR0ZWREYXRlLFxyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGZpbHRlckluc3RhbmNlLnNldE1vZGVsKG51bGwpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBncmlkUmVmLmN1cnJlbnQuYXBpLm9uRmlsdGVyQ2hhbmdlZCgpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8gRXhhbXBsZSBjb2x1bW4gZGVmaW5pdGlvbnNcclxuICBjb25zdCByZXF1ZXN0Q29sdW1uRGVmcyA9IHVzZU1lbW8oXHJcbiAgICAoKSA9PiBbXHJcbiAgICAgIHtcclxuICAgICAgICBoZWFkZXJOYW1lOiBcIlJlcXVlc3QgSURcIixcclxuICAgICAgICBmaWVsZDogXCJyZXF1ZXN0X2lkXCIsXHJcbiAgICAgICAgZmxleDogMSxcclxuICAgICAgICBoaWRlOiAhb3JkZXJWaWV3LFxyXG4gICAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIGhlYWRlck5hbWU6IFwiUmVxdWlyZWQgRGF0ZVwiLFxyXG4gICAgICAgIGZpZWxkOiBcInJlcXVpcmVkX2RhdGVcIixcclxuICAgICAgICBmbGV4OiAxLFxyXG4gICAgICAgIGhpZGU6ICFvcmRlclZpZXcsXHJcbiAgICAgICAgZmlsdGVyOiBcImFnRGF0ZUNvbHVtbkZpbHRlclwiLFxyXG4gICAgICAgIGZpbHRlclBhcmFtczoge1xyXG4gICAgICAgICAgY29tcGFyYXRvcjogKGZpbHRlckxvY2FsRGF0ZUF0TWlkbmlnaHQsIGNlbGxWYWx1ZSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBkYXRlQXNTdHJpbmcgPSBjZWxsVmFsdWU7XHJcbiAgICAgICAgICAgIGlmICghZGF0ZUFzU3RyaW5nKSByZXR1cm4gMDtcclxuXHJcbiAgICAgICAgICAgIGNvbnN0IGNlbGxEYXRlID0gbmV3IERhdGUoZGF0ZUFzU3RyaW5nKTtcclxuICAgICAgICAgICAgY29uc3QgY2VsbERhdGVBdE1pZG5pZ2h0ID0gbmV3IERhdGUoY2VsbERhdGUpO1xyXG4gICAgICAgICAgICBjZWxsRGF0ZUF0TWlkbmlnaHQuc2V0SG91cnMoMCwgMCwgMCwgMCk7XHJcblxyXG4gICAgICAgICAgICBpZiAoY2VsbERhdGVBdE1pZG5pZ2h0IDwgZmlsdGVyTG9jYWxEYXRlQXRNaWRuaWdodCkge1xyXG4gICAgICAgICAgICAgIHJldHVybiAtMTtcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChjZWxsRGF0ZUF0TWlkbmlnaHQgPiBmaWx0ZXJMb2NhbERhdGVBdE1pZG5pZ2h0KSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIDE7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIDA7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICB2YWx1ZUZvcm1hdHRlcjogKHBhcmFtcykgPT4ge1xyXG4gICAgICAgICAgcmV0dXJuIHBhcmFtcy52YWx1ZVxyXG4gICAgICAgICAgICA/IG5ldyBEYXRlKHBhcmFtcy52YWx1ZSkudG9Mb2NhbGVEYXRlU3RyaW5nKClcclxuICAgICAgICAgICAgOiBcIlwiO1xyXG4gICAgICAgIH0sXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBoZWFkZXJOYW1lOiBcIlNpdGVcIixcclxuICAgICAgICBmaWVsZDogXCJzaXRlX25hbWVcIixcclxuICAgICAgICBmbGV4OiAxLFxyXG4gICAgICAgIGhpZGU6ICFvcmRlclZpZXcsXHJcbiAgICAgICAgZmlsdGVyOiBcImFnVGV4dENvbHVtbkZpbHRlclwiLFxyXG4gICAgICAgIGZpbHRlclBhcmFtczoge1xyXG4gICAgICAgICAgdmFsdWVzOiBbXCJJU1MxLVRleW5oYW1cIiwgXCJJU1MyLUxpbnRvblwiLCBcIklTUzMtU2l0dGluZ2JvdXJuZVwiXSxcclxuICAgICAgICB9LFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgaGVhZGVyTmFtZTogXCJQcm9kdWN0XCIsXHJcbiAgICAgICAgZmllbGQ6IFwicHJvZHVjdF9uYW1lXCIsXHJcbiAgICAgICAgZmxleDogMSxcclxuICAgICAgICBoaWRlOiAhb3JkZXJWaWV3LFxyXG4gICAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIGhlYWRlck5hbWU6IFwiU2l6ZVwiLFxyXG4gICAgICAgIGZpZWxkOiBcInNpemVcIixcclxuICAgICAgICBmbGV4OiAwLjc1LFxyXG4gICAgICAgIGhpZGU6ICFvcmRlclZpZXcsXHJcbiAgICAgICAgZmlsdGVyOiBcImFnVGV4dENvbHVtbkZpbHRlclwiLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgaGVhZGVyTmFtZTogXCJRdHlcIixcclxuICAgICAgICBmaWVsZDogXCJxdWFudGl0eVwiLFxyXG4gICAgICAgIGZsZXg6IDAuNzUsXHJcbiAgICAgICAgaGlkZTogIW9yZGVyVmlldyxcclxuICAgICAgICBmaWx0ZXI6IFwiYWdUZXh0Q29sdW1uRmlsdGVyXCIsXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBoZWFkZXJOYW1lOiBcIlJlcXVlc3RvclwiLFxyXG4gICAgICAgIGZpZWxkOiBcInJlcXVlc3RvclwiLFxyXG4gICAgICAgIGZsZXg6IDEsXHJcbiAgICAgICAgaGlkZTogIW9yZGVyVmlldyxcclxuICAgICAgICBmaWx0ZXI6IFwiYWdUZXh0Q29sdW1uRmlsdGVyXCIsXHJcbiAgICAgIH0sXHJcbiAgICAgIHsgaGVhZGVyTmFtZTogXCJDb21tZW50XCIsIGZpZWxkOiBcImNvbW1lbnRcIiwgZmxleDogMSwgaGlkZTogIW9yZGVyVmlldyB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgaGVhZGVyTmFtZTogXCJTdGF0dXNcIixcclxuICAgICAgICBmaWVsZDogXCJzdGF0dXNcIixcclxuICAgICAgICBoaWRlOiAhb3JkZXJWaWV3LFxyXG4gICAgICAgIGNlbGxSZW5kZXJlcjogcHJvZHVjdFN0YXR1c1JlbmRlcmVyLFxyXG4gICAgICAgIGNlbGxTdHlsZTogKCkgPT4gKHsganVzdGlmeUNvbnRlbnQ6IFwiY2VudGVyXCIgfSksXHJcbiAgICAgICAgZmxleDogMS4yNSxcclxuICAgICAgICBmaWx0ZXJQYXJhbXM6IHtcclxuICAgICAgICAgIHZhbHVlczogW1xyXG4gICAgICAgICAgICBcIlBlbmRpbmcgUmV2aWV3XCIsXHJcbiAgICAgICAgICAgIFwiQXBwcm92ZWRcIixcclxuICAgICAgICAgICAgXCJSZWplY3RlZFwiLFxyXG4gICAgICAgICAgICBcIk9yZGVyZWRcIixcclxuICAgICAgICAgICAgXCJEcmFmdFwiLFxyXG4gICAgICAgICAgXSxcclxuICAgICAgICB9LFxyXG4gICAgICB9LFxyXG5cclxuICAgICAgLy9wcm9kdWN0IHZpZXdcclxuICAgICAge1xyXG4gICAgICAgIGhlYWRlck5hbWU6IFwiUHJvZHVjdCBOYW1lXCIsXHJcbiAgICAgICAgZmllbGQ6IFwicHJvZHVjdF9uYW1lXCIsXHJcbiAgICAgICAgZmxleDogMSxcclxuICAgICAgICBoaWRlOiBvcmRlclZpZXcsXHJcbiAgICAgIH0sXHJcbiAgICAgIHsgaGVhZGVyTmFtZTogXCJDYXRlZ29yeVwiLCBmaWVsZDogXCJjYXRlZ29yeVwiLCBmbGV4OiAwLjUsIGhpZGU6IG9yZGVyVmlldyB9LFxyXG4gICAgICB7IGhlYWRlck5hbWU6IFwiU2l6ZXNcIiwgZmllbGQ6IFwic2l6ZXNcIiwgZmxleDogMC41LCBoaWRlOiBvcmRlclZpZXcgfSxcclxuICAgICAge1xyXG4gICAgICAgIGhlYWRlck5hbWU6IFwiTmFtZSBQcmludGluZ1wiLFxyXG4gICAgICAgIGZpZWxkOiBcIm5hbWVfcHJpbnRhYmxlXCIsXHJcbiAgICAgICAgZmxleDogMC41LFxyXG4gICAgICAgIGhpZGU6IG9yZGVyVmlldyxcclxuICAgICAgICBjZWxsUmVuZGVyZXI6IFwiYWdDaGVja2JveENlbGxSZW5kZXJlclwiLFxyXG4gICAgICAgIGNlbGxSZW5kZXJlclBhcmFtczoge1xyXG4gICAgICAgICAgZGlzYWJsZWQ6IHRydWUsIC8vIE1ha2VzIGl0IHJlYWQtb25seVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgY2VsbFN0eWxlOiB7XHJcbiAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcclxuICAgICAgICAgIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsXHJcbiAgICAgICAgICBqdXN0aWZ5Q29udGVudDogXCJjZW50ZXJcIixcclxuICAgICAgICB9LFxyXG4gICAgICAgIGhlYWRlckNsYXNzOiBcImFnLWhlYWRlci1jZWxsLWNlbnRlcmVkXCIsXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBoZWFkZXJOYW1lOiBcIlN0YXR1c1wiLFxyXG4gICAgICAgIC8vIGZpZWxkOiBcInNpdGVfaWRcIixcclxuICAgICAgICBmbGV4OiAwLjUsXHJcbiAgICAgICAgY2VsbFJlbmRlcmVyOiAocGFyYW1zKSA9PlxyXG4gICAgICAgICAgcHJvZHVjdEFjdGl2ZVJlbmRlcmVyKFxyXG4gICAgICAgICAgICBwYXJhbXMsXHJcbiAgICAgICAgICAgIHNldFNob3dBZGRQcm9kdWN0LFxyXG4gICAgICAgICAgICBvcmRlclZpZXcsXHJcbiAgICAgICAgICAgIHNldElzRWRpdGluZ1Byb2R1Y3QsXHJcbiAgICAgICAgICAgIHNldEVkaXRQcm9kdWN0RGF0YSxcclxuICAgICAgICAgICAgLy9iYW5hbmFcclxuICAgICAgICAgICAgc2V0U2hvd1N0YXR1c0RpYWxvZyxcclxuICAgICAgICAgICAgc2V0U2VsZWN0ZWRSb3dEYXRhLFxyXG4gICAgICAgICAgICBzZXRTdGF0dXNBY3Rpb25cclxuICAgICAgICAgICksXHJcbiAgICAgICAgc29ydGFibGU6IGZhbHNlLFxyXG4gICAgICAgIGhpZGU6IG9yZGVyVmlldyxcclxuICAgICAgICAvLyB9LFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgaGVhZGVyTmFtZTogXCJBY3Rpb25zXCIsXHJcbiAgICAgICAgZmllbGQ6IFwic2l0ZV9pZFwiLFxyXG4gICAgICAgIGZsZXg6IDEsXHJcbiAgICAgICAgY2VsbFJlbmRlcmVyOiAocGFyYW1zKSA9PlxyXG4gICAgICAgICAgcHBlQWN0aW9uUmVuZGVyZXIoXHJcbiAgICAgICAgICAgIHBhcmFtcyxcclxuICAgICAgICAgICAgc2V0U2hvd0FkZFByb2R1Y3QsXHJcbiAgICAgICAgICAgIG9yZGVyVmlldyxcclxuICAgICAgICAgICAgc2V0SXNFZGl0aW5nUHJvZHVjdCxcclxuICAgICAgICAgICAgc2V0RWRpdFByb2R1Y3REYXRhLFxyXG4gICAgICAgICAgICAvL2JhbmFuYVxyXG4gICAgICAgICAgICBzZXRTaG93U3RhdHVzRGlhbG9nLFxyXG4gICAgICAgICAgICBzZXRTZWxlY3RlZFJvd0RhdGEsXHJcbiAgICAgICAgICAgIHNldFN0YXR1c0FjdGlvbixcclxuICAgICAgICAgICAgdXNlckRhdGFcclxuICAgICAgICAgICksXHJcbiAgICAgICAgc29ydGFibGU6IGZhbHNlLFxyXG4gICAgICB9LFxyXG4gICAgXSxcclxuICAgIFtvcmRlclZpZXddXHJcbiAgKTtcclxuXHJcbiAgLy8gY29uc29sZS5sb2coXCJvcmRlciB2Ymlld1wiLCBvcmRlclZpZXcpO1xyXG4gIGNvbnN0IGRlZmF1bHRDb2xEZWYgPSB1c2VNZW1vKFxyXG4gICAgKCkgPT4gKHtcclxuICAgICAgc29ydGFibGU6IHRydWUsXHJcbiAgICAgIGZpbHRlcjogdHJ1ZSxcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgZGVib3VuY2VNczogMzAwLCAvLyBBZGQgZGVib3VuY2UgZm9yIGJldHRlciBwZXJmb3JtYW5jZVxyXG4gICAgICB9LFxyXG4gICAgICByZXNpemFibGU6IHRydWUsXHJcbiAgICAgIGZsZXg6IDEsXHJcbiAgICAgIHN1cHByZXNzTWVudTogZmFsc2UsXHJcbiAgICB9KSxcclxuICAgIFtdXHJcbiAgKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICAvLyBjb25zb2xlLmxvZyhcclxuICAgIC8vICAgXCJTZWxlY3RlZCBWaWV3OlwiLFxyXG4gICAgLy8gICBzZWxlY3RlZFZpZXcsXHJcbiAgICAvLyAgIFwiT3JkZXIgVmlldzpcIixcclxuICAgIC8vICAgb3JkZXJWaWV3LFxyXG4gICAgLy8gICBzZWxlY3RlZFZpZXcgPT09IFwiT3JkZXJzXCJcclxuICAgIC8vICk7XHJcbiAgfSwgW3NlbGVjdGVkVmlld10pO1xyXG5cclxuICBjb25zdCBoYW5kbGVHcmlkUmVhZHkgPSAocGFyYW1zKSA9PiB7XHJcbiAgICBwYXJhbXMuYXBpLnNldENvbHVtbkRlZnMocmVxdWVzdENvbHVtbkRlZnMpO1xyXG4gIH07XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoZ3JpZFJlZi5jdXJyZW50ICYmIGdyaWRSZWYuY3VycmVudC5hcGkpIHtcclxuICAgICAgZ3JpZFJlZi5jdXJyZW50LmFwaS5zZXRDb2x1bW5EZWZzKHJlcXVlc3RDb2x1bW5EZWZzKTtcclxuICAgICAgLy8gUmVmcmVzaCB0aGUgZ3JpZCB0byBhcHBseSBuZXcgY29sdW1uIGRlZmluaXRpb25zXHJcbiAgICAgIGdyaWRSZWYuY3VycmVudC5hcGkucmVmcmVzaEhlYWRlcigpO1xyXG4gICAgICBncmlkUmVmLmN1cnJlbnQuYXBpLnJlZnJlc2hDZWxscygpO1xyXG4gICAgfVxyXG4gIH0sIFtyZXF1ZXN0Q29sdW1uRGVmcywgb3JkZXJWaWV3XSk7IC8vIEFkZCBvcmRlclZpZXcgZGVwZW5kZW5jeVxyXG5cclxuICBjb25zdCBvbkZpbHRlclRleHRCb3hDaGFuZ2VkID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgZ3JpZFJlZi5jdXJyZW50LmFwaS5zZXRRdWlja0ZpbHRlcihcclxuICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoXCJmaWx0ZXItdGV4dC1ib3hcIikudmFsdWVcclxuICAgICk7XHJcbiAgICBzZXRTZWFyY2hJbnB1dChkb2N1bWVudC5nZXRFbGVtZW50QnlJZChcImZpbHRlci10ZXh0LWJveFwiKS52YWx1ZSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBoYW5kbGVQYWdlU2l6ZUNoYW5nZSA9IChldmVudCkgPT4ge1xyXG4gICAgY29uc3QgbmV3UGFnZVNpemUgPSBwYXJzZUludChldmVudC50YXJnZXQudmFsdWUsIDE1KTtcclxuICAgIHNldFBhZ2VTaXplKG5ld1BhZ2VTaXplKTtcclxuICAgIGdyaWRSZWYuY3VycmVudC5hcGkucGFnaW5hdGlvblNldFBhZ2VTaXplKG5ld1BhZ2VTaXplKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVDbGVhckZpbHRlcnMgPSAoKSA9PiB7XHJcbiAgICAvLyBDbGVhciBBRyBHcmlkIGZpbHRlcnNcclxuICAgIGlmIChncmlkUmVmLmN1cnJlbnQgJiYgZ3JpZFJlZi5jdXJyZW50LmFwaSkge1xyXG4gICAgICBncmlkUmVmLmN1cnJlbnQuYXBpLnNldEZpbHRlck1vZGVsKG51bGwpO1xyXG4gICAgICBncmlkUmVmLmN1cnJlbnQuYXBpLm9uRmlsdGVyQ2hhbmdlZCgpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFJlc2V0IFVJIGNvbnRyb2wgc3RhdGVzXHJcbiAgICBzZXRTaXRlRmlsdGVyKFwiXCIpO1xyXG4gICAgc2V0U3RhdHVzRmlsdGVyKFwiXCIpO1xyXG4gICAgc2V0RGF0ZUZpbHRlcihcIlwiKTtcclxuICB9O1xyXG4gIGNvbnN0IHRyaW1JbnB1dFRleHQgPSAoaW5wdXQpID0+IHtcclxuICAgIHJldHVybiBpbnB1dC50cmltKCk7XHJcbiAgfTtcclxuXHJcbiAgLy8jcmVnaW9uIGFwaVxyXG4gIGNvbnN0IHVwZGF0ZVNlbGVjdGVkUm93U3RhdHVzID0gKHVwZGF0ZWRTdGF0dXNJRCwgY29tbWVudCwgcGFyYW1zKSA9PiB7XHJcbiAgICBpZiAodXBkYXRlZFN0YXR1c0lEID09IDMpe1xyXG4gICAgICBpZihjb21tZW50KSB7XHJcbiAgICAgICAgc2V0SXNWYWxpZENhbmNlbFJlYXNvbih0cnVlKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRJc1ZhbGlkQ2FuY2VsUmVhc29uKGZhbHNlKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuICAgIH0gXHJcblxyXG4gICAgY29uc29sZS5sb2coXCJzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3NzXCIsIHBhcmFtcyk7XHJcbiAgICBjb25zdCBhcGlQYXlsb2FkID0ge1xyXG4gICAgICByZXF1ZXN0X25vOiBwYXJhbXMuaWQsXHJcbiAgICAgIGl0ZW1fbnVtYmVyOiBwYXJhbXMuaXRlbV9udW1iZXIsXHJcbiAgICAgIFByb2R1Y3ROYW1lOiBwYXJhbXMucHJvZHVjdF9uYW1lLFxyXG4gICAgICBTaXplOiBwYXJhbXMuc2l6ZSxcclxuICAgICAgUXVhbnRpdHk6IHBhcmFtcy5xdWFudGl0eSxcclxuICAgICAgTmFtZUZvclByaW50aW5nOiBwYXJhbXMuTmFtZUZvclByaW50aW5nLFxyXG4gICAgICBDb21tZW50czogcGFyYW1zLmNvbW1lbnQsXHJcbiAgICAgIFJlcXVlc3RJdGVtSUQ6IHBhcmFtcy5yZXF1ZXN0X2l0ZW1faWQsXHJcbiAgICAgIGNvbW1lbnRPblVwZGF0aW5nUmVxdWVzdDogY29tbWVudCxcclxuICAgICAgYWN0aW9uX2lkOiB1cGRhdGVkU3RhdHVzSUQsXHJcbiAgICAgIFN1Ym1pdHRlclVzZXJJRDogdXNlckRhdGEudXNlcl9pZCxcclxuICAgICAgU3VibWl0dGVyRW1haWw6IHVzZXJEYXRhPy5lbWFpbCxcclxuICAgICAgb3JpZ25hdG9yRW1haWw6IHBhcmFtcz8ub3JpZ25hdG9yRW1haWwsXHJcbiAgICB9O1xyXG5cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBsZXQgc2VydmVyQWRkcmVzcyA9IGFwaUNvbmZpZy5zZXJ2ZXJBZGRyZXNzO1xyXG4gICAgICBmZXRjaChcclxuICAgICAgICBgJHtzZXJ2ZXJBZGRyZXNzfXBwZS1jb25zdW1hYmxlcy91cGRhdGUtcHJvZHVjdC1yZXF1ZXN0LWl0ZW1zLyR7cGFyYW1zLmlkfWAsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgbWV0aG9kOiBcIlBvc3RcIixcclxuICAgICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICAgXCJjb250ZW50LXR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgY3JlZGVudGlhbHM6IFwiaW5jbHVkZVwiLFxyXG4gICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoYXBpUGF5bG9hZCksXHJcbiAgICAgICAgfVxyXG4gICAgICApXHJcbiAgICAgICAgLnRoZW4oKHJlcykgPT4ge1xyXG4gICAgICAgICAgaWYgKHJlcy5zdGF0dXMgPT09IDIwMCkge1xyXG4gICAgICAgICAgICByZXR1cm4gcmVzLmpzb24oKTtcclxuICAgICAgICAgIH0gZWxzZSBpZiAocmVzLnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICAgIHRvYXN0LmVycm9yKFwiWW91ciBzZXNzaW9uIGhhcyBleHBpcmVkLiBQbGVhc2UgbG9nIGluIGFnYWluLlwiKTtcclxuICAgICAgICAgICAgc2V0VGltZW91dChhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICAgICAgYXdhaXQgbG9nb3V0KCk7XHJcbiAgICAgICAgICAgICAgY29uc3QgcmVkaXJlY3RVcmwgPSBgL2xvZ2luP3JlZGlyZWN0PSR7ZW5jb2RlVVJJQ29tcG9uZW50KFxyXG4gICAgICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uLnBhdGhuYW1lXHJcbiAgICAgICAgICAgICAgKX1gO1xyXG4gICAgICAgICAgICAgIHJvdXRlci5wdXNoKHJlZGlyZWN0VXJsKTtcclxuICAgICAgICAgICAgfSwgMzAwMCk7XHJcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgdG9hc3QuZXJyb3IoXCJGYWlsZWQgdG8gc2F2ZSBwcm9kdWN0LlwiKTtcclxuICAgICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSlcclxuICAgICAgICAudGhlbigoanNvbikgPT4ge1xyXG4gICAgICAgICAgaWYgKGpzb24gJiYganNvbi5tc2cpIHtcclxuICAgICAgICAgICAgdG9hc3Quc3VjY2Vzcyhqc29uLm1zZywge1xyXG4gICAgICAgICAgICAgIHBvc2l0aW9uOiBcInRvcC1yaWdodFwiLFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgcm91dGVyLnJlcGxhY2UoXCIvcHBlLWNvbnN1bWFibGVcIik7XHJcbiAgICAgICAgICAgIH0sIDIwMDApO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgZ2V0RGF0YSgpLnRoZW4oKGRhdGEpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgZ3JvdXBlZCA9IGRhdGE/LnJlZHVjZSgoYWNjLCByb3cpID0+IHtcclxuICAgICAgICAgICAgICBpZiAoIWFjY1tyb3cucmVxdWVzdF9pZF0pIHtcclxuICAgICAgICAgICAgICAgIGFjY1tyb3cucmVxdWVzdF9pZF0gPSBbXTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgYWNjW3Jvdy5yZXF1ZXN0X2lkXS5wdXNoKHJvdyk7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIGFjYztcclxuICAgICAgICAgICAgfSwge30pO1xyXG4gICAgICAgICAgICBjb25zdCBTVEFUVVNfTUFQID0ge1xyXG4gICAgICAgICAgICAgIDE6IFwiUGVuZGluZyBSZXZpZXdcIixcclxuICAgICAgICAgICAgICAyOiBcIkFwcHJvdmVkXCIsXHJcbiAgICAgICAgICAgICAgMzogXCJSZWplY3RlZFwiLFxyXG4gICAgICAgICAgICAgIDQ6IFwiT3JkZXJlZFwiLFxyXG4gICAgICAgICAgICAgIDU6IFwiRHJhZnRcIixcclxuICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgY29uc3QgY291bnRlcnMgPSB7fTtcclxuICAgICAgICAgICAgY29uc3QgZm9ybWF0dGVkRGF0YSA9IGRhdGE/Lm1hcCgocm93KSA9PiB7XHJcbiAgICAgICAgICAgICAgY29uc3Qgc2libGluZ3MgPSBncm91cGVkW3Jvdy5yZXF1ZXN0X2lkXTtcclxuICAgICAgICAgICAgICBjb25zdCBpbmRleCA9IHNpYmxpbmdzLmZpbmRJbmRleChyID0+IHIucmVxdWVzdF9pdGVtX2lkID09PSByb3cucmVxdWVzdF9pdGVtX2lkKSArIDE7XHJcblxyXG4gICAgICAgICAgICAgIGNvdW50ZXJzW3Jvdy5yZXF1ZXN0X2lkXSA9IChjb3VudGVyc1tyb3cucmVxdWVzdF9pZF0gfHwgMCkgKyAxO1xyXG4gICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICBpZDogcm93LmlkLFxyXG4gICAgICAgICAgICAgICAgcmVxdWVzdF9pZDogYCR7cm93LnJlcXVlc3RfaWR9IC0gJHtyb3cuaXRlbV9udW1iZXJ9YCwgLy8gc3RhYmxlIHBlciByZXF1ZXN0X2lkXHJcbiAgICAgICAgICAgICAgICByZXF1aXJlZF9kYXRlOiBuZXcgRGF0ZShyb3cucmVxdWlyZWRfZGF0ZSlcclxuICAgICAgICAgICAgICAgICAgLnRvSVNPU3RyaW5nKClcclxuICAgICAgICAgICAgICAgICAgLnNwbGl0KFwiVFwiKVswXSxcclxuICAgICAgICAgICAgICAgIHNpdGVfbmFtZTogcm93LnNpdGVfbmFtZSxcclxuICAgICAgICAgICAgICAgIHByb2R1Y3RfbmFtZTogcm93LnByb2R1Y3RfbmFtZSB8fCBcIlVubmFtZWQgUHJvZHVjdFwiLFxyXG4gICAgICAgICAgICAgICAgcmVxdWVzdG9yOiByb3cudXNlcl9uYW1lLFxyXG4gICAgICAgICAgICAgICAgb3JpZ25hdG9yRW1haWw6IHJvdy5hY3Rpb25lZF9ieV9lbWFpbCxcclxuICAgICAgICAgICAgICAgIGNvbW1lbnQ6IHJvdy5jb21tZW50cyxcclxuICAgICAgICAgICAgICAgIHNpemU6IHJvdy5zaXplLFxyXG4gICAgICAgICAgICAgICAgcXVhbnRpdHk6IHJvdy5xdWFudGl0eSxcclxuICAgICAgICAgICAgICAgIE5hbWVGb3JQcmludGluZzogcm93Lm5hbWVfZm9yX3ByaW50aW5nLFxyXG4gICAgICAgICAgICAgICAgc3RhdHVzOiBTVEFUVVNfTUFQW3Jvdy5hY3Rpb25faWRdLFxyXG4gICAgICAgICAgICAgICAgcmVxdWVzdF9pdGVtX2lkOiByb3cucmVxdWVzdF9pdGVtX2lkLFxyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIHNldFJlcXVlc3RSb3dEYXRhKGZvcm1hdHRlZERhdGEpO1xyXG4gICAgICAgICAgICBzZXRDYW5jZWxsZWRSZWFzb25hcGkoXCJcIik7XHJcbiAgICAgICAgICAgIHNldFNob3dTdGF0dXNEaWFsb2coZmFsc2UpO1xyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgfSk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmxvZyhcImVycm9yaW4gc3VibWl0dGluZyB0aGUgb3JkZXJcIiwgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8VG9hc3RDb250YWluZXIgbGltaXQ9ezF9IC8+XHJcbiAgICAgIDxMYXlvdXQgdXNlckRhdGE9e3VzZXJEYXRhfT5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1yLTIwIG1kOm1yLTEyIGxnOm1yLTE0XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgbWQ6ZmxleC1jb2wgbGc6ZmxleC1yb3cganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgIHsodXNlckRhdGEucm9sZV9pZCA9PT0gNiB8fFxyXG4gICAgICAgICAgICAgIHVzZXJEYXRhLnJvbGVfaWQgPT09IDEgfHxcclxuICAgICAgICAgICAgICB1c2VyRGF0YS5yb2xlX2lkID09PSA1KSAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNlwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCByb3VuZGVkLWZ1bGwgYmctZ3JheS0yMDAgcC0xIFxyXG4gICAgICAgICAgICAgICAgICR7XHJcbiAgICAgICAgICAgICAgICAgICB1c2VyRGF0YS5yb2xlX2lkID09PSA2IHx8XHJcbiAgICAgICAgICAgICAgICAgICB1c2VyRGF0YS5yb2xlX2lkID09PSAxIHx8XHJcbiAgICAgICAgICAgICAgICAgICB1c2VyRGF0YS5yb2xlX2lkID09PSA1XHJcbiAgICAgICAgICAgICAgICAgICAgID8gXCJcIlxyXG4gICAgICAgICAgICAgICAgICAgICA6IFwiaGlkZGVuXCJcclxuICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBgfVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtNCBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24gJHtcclxuICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkVmlldyA9PT0gXCJPcmRlcnNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctd2hpdGUgc2hhZG93IHRleHQtYmxhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWJsYWNrXCJcclxuICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTZWxlY3RlZFZpZXcoXCJPcmRlcnNcIil9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICBSZXF1ZXN0c1xyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTQgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZFZpZXcgPT09IFwiUHJvZHVjdHNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctd2hpdGUgc2hhZG93IHRleHQtYmxhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWJsYWNrXCJcclxuICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVTZWxlY3RlZFZpZXcoXCJQcm9kdWN0c1wiKSwgaGFuZGxlQ2xlYXJGaWx0ZXJzKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIFByb2R1Y3RzXHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIHtvcmRlclZpZXcgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgIC8vIHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogXCIxMHB4XCIgfX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggZ2FwLTQgaXRlbXMtY2VudGVyXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwibXItMlwiPlNpdGU6PC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgPHNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzaXRlRmlsdGVyfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2l0ZUZpbHRlckNoYW5nZShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIHAtMSByb3VuZGVkXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5BbGwgU2l0ZXM8L29wdGlvbj57XCIgXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgey8qVE9ETyBnZXQgZGF0YSBmcm9tIGRiKi99XHJcbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIklTUzEtVGV5bmhhbVwiPklTUzEtVGV5bmhhbTwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJJU1MyLUxpbnRvblwiPklTUzItTGludG9uPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIklTUzMtU2l0dGluZ2JvdXJuZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgSVNTMy1TaXR0aW5nYm91cm5lXHJcbiAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIFN0YXR1cyBGaWx0ZXIgKi99XHJcbiAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwibXItMlwiPlN0YXR1czo8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICA8c2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3N0YXR1c0ZpbHRlcn1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVN0YXR1c0ZpbHRlckNoYW5nZShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIHAtMSByb3VuZGVkXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5BbGwgU3RhdHVzZXM8L29wdGlvbj57XCIgXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgey8qVE9ETyBnZXQgZGF0YSBmcm9tIGRiKi99XHJcbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlBlbmRpbmcgUmV2aWV3XCI+UGVuZGluZzwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJBcHByb3ZlZFwiPkFwcHJvdmVkPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlJlamVjdGVkXCI+UmVqZWN0ZWQ8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiT3JkZXJlZFwiPk9yZGVyZWQ8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiRHJhZnRcIj5EcmFmdDwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgIHsvKiBEYXRlIEZpbHRlciAqL31cclxuICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJtci0yXCI+RGF0ZTo8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2RhdGVGaWx0ZXJ9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVEYXRlRmlsdGVyQ2hhbmdlKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXIgcC0xIHJvdW5kZWRcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICB7LyogQ2xlYXIgRmlsdGVycyBCdXR0b24gKi99XHJcbiAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsZWFyRmlsdGVyc31cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWwtNCBweC0zIHB5LTEgYm9yZGVyIHJvdW5kZWQgYmctZ3JheS0yMDAgaG92ZXI6YmctZ3JheS0zMDBcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICBDbGVhciBGaWx0ZXJzXHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93XCI+XHJcbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInJlbGF0aXZlIGJsb2NrIHctWzQ3dmhdIHRleHQtZ3JheS00MDAgIG10LTAgcHktMlwiPlxyXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgcHgtNCBweS0xIHB0LVswLjRyZW1dIDJ4bDpwdC0xLjUgdGV4dC1ibGFja1wiPlxyXG4gICAgICAgICAgICAgICAgICA8Rm9udEF3ZXNvbWVJY29uIGljb249e2ZhU2VhcmNofSBjbGFzc05hbWU9XCJmdy1ib2xkXCIgLz5cclxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgIGlkPVwiZmlsdGVyLXRleHQtYm94XCJcclxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2guLi5cIlxyXG4gICAgICAgICAgICAgICAgICBvbklucHV0PXtvbkZpbHRlclRleHRCb3hDaGFuZ2VkfVxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoSW5wdXR9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCBweC00IHBsLTEwIHRleHQtZ3JheS01MDAgcGxhY2Vob2xkZXItZ3JheS00MDAgc2VhcmNoYmFyIGJvcmRlciByb3VuZGVkLWxnIGFwcGVhcmFuY2Utbm9uZSBmb3JtLWlucHV0IGZvY3VzOm91dGxpbmUtbm9uZSBzaGFkb3ctWzBfMF81cHhfcmdiYSgwLDAsMCwwLjEpXVwiXHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWwtMiBteS0yIHB4LTMgcHktMSBwLVs2cHhdIGJvcmRlciByb3VuZGVkLW1kIGJnLXNraW4tcHJpbWFyeSB0ZXh0LXdoaXRlIHRleHQtc20gY3Vyc29yLXBvaW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICBpZiAoc2VsZWN0ZWRWaWV3ID09PSBcIk9yZGVyc1wiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgcm91dGVyLnB1c2goXCIvcHBlLWNvbnN1bWFibGUvYWRkXCIpO1xyXG4gICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldFNob3dBZGRQcm9kdWN0KHRydWUpO1xyXG4gICAgICAgICAgICAgICAgICAgIHNldElzRWRpdGluZ1Byb2R1Y3QoZmFsc2UpO1xyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtvcmRlclZpZXcgPyBcIk5ldyBSZXF1ZXN0XCIgOiBcIkFkZCBQcm9kdWN0XCJ9XHJcbiAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8QWRkUHJvZHVjdERpYWxvZ1xyXG4gICAgICAgICAgICBvcGVuPXtzaG93QWRkUHJvZHVjdH1cclxuICAgICAgICAgICAgb25PcGVuQ2hhbmdlPXsoXywgZGF0YSkgPT4ge1xyXG4gICAgICAgICAgICAgIHNldFNob3dBZGRQcm9kdWN0KGRhdGEub3Blbik7XHJcbiAgICAgICAgICAgICAgc2V0SXNFZGl0aW5nUHJvZHVjdChmYWxzZSk7XHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIG9uU3VibWl0PXsoeyBwcm9kdWN0TmFtZSwgcHJpbnRpbmcsIHNpemVzIH0pID0+IHtcclxuICAgICAgICAgICAgICAvLyBoYW5kbGUgeW91ciBzdWJtaXQgbG9naWMgaGVyZVxyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKHsgcHJvZHVjdE5hbWUsIHByaW50aW5nLCBzaXplcyB9KTtcclxuICAgICAgICAgICAgICBzZXRTaG93QWRkUHJvZHVjdChmYWxzZSk7XHJcbiAgICAgICAgICAgICAgcm91dGVyLnJlbG9hZCgpO1xyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICBpc0VkaXRpbmdQcm9kdWN0PXtpc0VkaXRpbmdQcm9kdWN0fVxyXG4gICAgICAgICAgICBlZGl0UHJvZHVjdERhdGE9e2VkaXRQcm9kdWN0RGF0YX1cclxuICAgICAgICAgICAgcHJvZHVjdFR5cGVzPXtwcm9kdWN0VHlwZXN9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICAgey8qIC8vIEFkZCB0aGlzIGJlZm9yZSB0aGUgY2xvc2luZyBMYXlvdXQgdGFnICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteS01XCI+XHJcbiAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSBhZy10aGVtZS1hbHBpbmUgIXJvdW5kZWQtbWRcIlxyXG4gICAgICAgICAgICAgIHN0eWxlPXt7IGhlaWdodDogXCJjYWxjKDEwMHZoIC0gMTgwcHgpXCIgfX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxBZ0dyaWRSZWFjdFxyXG4gICAgICAgICAgICAgICAgcm93RGF0YT17cmVxdWVzdFJvd0RhdGF9XHJcbiAgICAgICAgICAgICAgICByZWY9e2dyaWRSZWZ9XHJcbiAgICAgICAgICAgICAgICByZXF1ZXN0Q29sdW1uRGVmcz17cmVxdWVzdENvbHVtbkRlZnN9XHJcbiAgICAgICAgICAgICAgICBkZWZhdWx0Q29sRGVmPXtkZWZhdWx0Q29sRGVmfVxyXG4gICAgICAgICAgICAgICAgc3VwcHJlc3NSb3dDbGlja1NlbGVjdGlvblxyXG4gICAgICAgICAgICAgICAgcGFnaW5hdGlvbj17dHJ1ZX1cclxuICAgICAgICAgICAgICAgIHBhZ2luYXRpb25QYWdlU2l6ZT17cGFnZVNpemV9XHJcbiAgICAgICAgICAgICAgICBvblBhZ2VTaXplQ2hhbmdlZD17aGFuZGxlUGFnZVNpemVDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICB0b29sdGlwU2hvd0RlbGF5PXswfVxyXG4gICAgICAgICAgICAgICAgdG9vbHRpcEhpZGVEZWxheT17MTAwMH1cclxuICAgICAgICAgICAgICAgIG9uR3JpZFJlYWR5PXtoYW5kbGVHcmlkUmVhZHl9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1zdGFydCBtdC0yIHBhZ2luYXRpb24tc3R5bGVcIj5cclxuICAgICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwicGFnZS1zaXplLXNlbGVjdCBwYWdpbmF0aW9uXCIgY2xhc3NOYW1lPVwiaW5wdXRzXCI+XHJcbiAgICAgICAgICAgICAgICAgIFNob3d7XCIgXCJ9XHJcbiAgICAgICAgICAgICAgICAgIDxzZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICBpZD1cInBhZ2Utc2l6ZS1zZWxlY3RcIlxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVQYWdlU2l6ZUNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cGFnZVNpemV9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9jdXM6b3V0bGluZS1ub25lXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezEwfT4xMDwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezE1fT4xNTwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezI1fT4yNTwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezUwfT41MDwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezEwMH0+MTAwPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PntcIiBcIn1cclxuICAgICAgICAgICAgICAgICAgRW50cmllc1xyXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8Rmx1ZW50UHJvdmlkZXJcclxuICAgICAgICAgIHRoZW1lPXt3ZWJMaWdodFRoZW1lfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwiIWJnLXRyYW5zcGFyZW50XCJcclxuICAgICAgICAgIHN0eWxlPXt7IGZvbnRGYW1pbHk6IFwicG9wcGluc3JlZ3VsYXJcIiB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxEaWFsb2dcclxuICAgICAgICAgICAgb3Blbj17c2hvd1N0YXR1c0RpYWxvZ31cclxuICAgICAgICAgICAgb25PcGVuQ2hhbmdlPXsoXywgZGF0YSkgPT4ge1xyXG4gICAgICAgICAgICAgIGlmICghZGF0YS5vcGVuKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRJc1ZhbGlkQ2FuY2VsUmVhc29uKHRydWUpO1xyXG4gICAgICAgICAgICAgICAgc2V0Q2FuY2VsbGVkUmVhc29uYXBpKFwiXCIpOyAvLyBBbHNvIGNsZWFyIHRoZSB0ZXh0XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIHNldFNob3dTdGF0dXNEaWFsb2coZGF0YS5vcGVuKTtcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPERpYWxvZ1RyaWdnZXIgZGlzYWJsZUJ1dHRvbkVuaGFuY2VtZW50PlxyXG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogXCJub25lXCIgfX0gLz5cclxuICAgICAgICAgICAgPC9EaWFsb2dUcmlnZ2VyPlxyXG4gICAgICAgICAgICA8RGlhbG9nU3VyZmFjZT5cclxuICAgICAgICAgICAgICA8RGlhbG9nQm9keT5cclxuICAgICAgICAgICAgICAgIDxEaWFsb2dUaXRsZT5VcGRhdGUgU3RhdHVzPC9EaWFsb2dUaXRsZT5cclxuICAgICAgICAgICAgICAgIDxEaWFsb2dDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtYi02XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRve1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgIHtzdGF0dXNBY3Rpb24gPT09IDJcclxuICAgICAgICAgICAgICAgICAgICAgID8gXCJhcHByb3ZlXCJcclxuICAgICAgICAgICAgICAgICAgICAgIDogc3RhdHVzQWN0aW9uID09PSAzXHJcbiAgICAgICAgICAgICAgICAgICAgICA/IFwicmVqZWN0XCJcclxuICAgICAgICAgICAgICAgICAgICAgIDogc3RhdHVzQWN0aW9uID09PSA0XHJcbiAgICAgICAgICAgICAgICAgICAgICA/IFwibWFyayBhcyBvcmRlcmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgIDogXCJ1cGRhdGVcIn17XCIgXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcyByZXF1ZXN0ID9cclxuICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICB7c3RhdHVzQWN0aW9uID09PSAzICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8dGV4dGFyZWFcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgdy1mdWxsIHJvdW5kZWQtbWQgcC0yIHB4LTMgIGJvcmRlciBib3JkZXItbGlnaHQtZ3JheTJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgcm93cz1cIjhcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NhbmNlbGxlZFJlYXNvbmFwaX1cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRDYW5jZWxsZWRSZWFzb25hcGkoZS50YXJnZXQudmFsdWUpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoZS50YXJnZXQudmFsdWUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRJc1ZhbGlkQ2FuY2VsUmVhc29uKHRydWUpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25CbHVyPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0cmltbWVkVmFsdWUgPSB0cmltSW5wdXRUZXh0KGUudGFyZ2V0LnZhbHVlKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q2FuY2VsbGVkUmVhc29uYXBpKHRyaW1tZWRWYWx1ZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJQcm92aWRlIHJlYXNvbiBmb3IgcmVqZWN0aW9uLi4uXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG1heExlbmd0aD1cIjUwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgPjwvdGV4dGFyZWE+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgIHtzdGF0dXNBY3Rpb24gPT09IDMgJiYgIWlzVmFsaWRDYW5jZWxSZWFzb24gJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgUGxlYXNlIFByb3ZpZGUgcmVhc29uIGZvciBjYW5jZWxsYXRpb25cclxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBkaXNwbGF5OiBcImZsZXhcIiwgZ2FwOiBcIjhweFwiLCBtYXJnaW5Ub3A6IFwiMTZweFwiIH19XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZFwiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7c2V0U2hvd1N0YXR1c0RpYWxvZyhmYWxzZSk7IHNldENhbmNlbGxlZFJlYXNvbmFwaShcIlwiKX19XHJcbiAgICAgICAgICAgICAgICAgICAgICBkYXRhLW1vZGFsLWhpZGU9XCJkZWZhdWx0LW1vZGFsXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1za2luLXByaW1hcnkgdGV4dC1za2luLXByaW1hcnkgZm9jdXM6cmluZy00IGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtMzAwIGZvbnQtbWVkaXVtIHJvdW5kZWQtbWQgdGV4dC1tZCBweC02IHB5LTIgdGV4dC1jZW50ZXIgXCJcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICBDYW5jZWxcclxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIEhhbmRsZSBzdGF0dXMgdXBkYXRlIGxvZ2ljIGhlcmVcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgXCJVcGRhdGluZyBzdGF0dXM6XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzQWN0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkUm93RGF0YVxyXG4gICAgICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVTZWxlY3RlZFJvd1N0YXR1cyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXNBY3Rpb24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2FuY2VsbGVkUmVhc29uYXBpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkUm93RGF0YVxyXG4gICAgICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgIGRhdGEtbW9kYWwtaGlkZT1cImRlZmF1bHQtbW9kYWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGJnLXNraW4tcHJpbWFyeSBob3ZlcjpiZy1za2luLXByaW1hcnkgZm9jdXM6cmluZy00IGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtMzAwIGZvbnQtbWVkaXVtIHJvdW5kZWQtbWQgdGV4dC1tZCBweC02IHB5LTIgdGV4dC1jZW50ZXIgXCJcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICBDb250aW51ZVxyXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvRGlhbG9nQ29udGVudD5cclxuICAgICAgICAgICAgICA8L0RpYWxvZ0JvZHk+XHJcbiAgICAgICAgICAgIDwvRGlhbG9nU3VyZmFjZT5cclxuICAgICAgICAgIDwvRGlhbG9nPntcIiBcIn1cclxuICAgICAgICA8L0ZsdWVudFByb3ZpZGVyPlxyXG4gICAgICA8L0xheW91dD5cclxuICAgIDwvPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBQUEVDb25zdW1hYmxlcztcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRTZXJ2ZXJTaWRlUHJvcHMgPSBhc3luYyAoY29udGV4dCkgPT4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCB7IHJlcSwgcmVzb2x2ZWRVcmwgfSA9IGNvbnRleHQ7XHJcbiAgICBjb25zdCBzZXNzaW9uSWQgPSByZXEuY29va2llcy50aGxfc2Vzc2lvbjtcclxuXHJcbiAgICBpZiAoIXNlc3Npb25JZCkge1xyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIHJlZGlyZWN0OiB7XHJcbiAgICAgICAgICBkZXN0aW5hdGlvbjogYC9sb2dpbj9yZWRpcmVjdD0ke2VuY29kZVVSSUNvbXBvbmVudChyZXNvbHZlZFVybCl9YCxcclxuICAgICAgICAgIHBlcm1hbmVudDogZmFsc2UsXHJcbiAgICAgICAgfSxcclxuICAgICAgfTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBhcGlCYXNlID1cclxuICAgICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX0JBU0VfVVJMIHx8IFwiaHR0cDovL2xvY2FsaG9zdDo4MDgxXCI7XHJcblxyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHthcGlCYXNlfS9hcGkvYXV0aC9tZWAsIHtcclxuICAgICAgbWV0aG9kOiBcIkdFVFwiLFxyXG4gICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgQ29va2llOiBgdGhsX3Nlc3Npb249JHtzZXNzaW9uSWR9YCxcclxuICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcclxuICAgICAgfSxcclxuICAgIH0pO1xyXG5cclxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICByZWRpcmVjdDoge1xyXG4gICAgICAgICAgZGVzdGluYXRpb246IGAvbG9naW4/cmVkaXJlY3Q9JHtlbmNvZGVVUklDb21wb25lbnQocmVzb2x2ZWRVcmwpfWAsXHJcbiAgICAgICAgICBwZXJtYW5lbnQ6IGZhbHNlLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH07XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgeyB1c2VyIH0gPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcblxyXG4gICAgICAvLyAgICBjb25zdCBhbGxvd2VkUm9sZXMgPSBbMSwgNSwgNl07XHJcbiAgICAgIC8vIGlmICghYWxsb3dlZFJvbGVzLmluY2x1ZGVzKHVzZXIucm9sZV9pZCkpIHtcclxuICAgICAgLy8gICByZXR1cm4ge1xyXG4gICAgICAvLyAgICAgcmVkaXJlY3Q6IHtcclxuICAgICAgLy8gICAgICAgZGVzdGluYXRpb246ICcvdW5hdXRob3JpemVkJyxcclxuICAgICAgLy8gICAgICAgcGVybWFuZW50OiBmYWxzZSxcclxuICAgICAgLy8gICAgIH0sXHJcbiAgICAgIC8vICAgfTtcclxuICAgICAgLy8gfVxyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIHByb3BzOiB7XHJcbiAgICAgICAgdXNlckRhdGE6IHVzZXIsXHJcbiAgICAgIH0sXHJcbiAgICB9O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgaW4gZ2V0U2VydmVyU2lkZVByb3BzOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICByZWRpcmVjdDoge1xyXG4gICAgICAgIGRlc3RpbmF0aW9uOiBcIi9sb2dpblwiLFxyXG4gICAgICAgIHBlcm1hbmVudDogZmFsc2UsXHJcbiAgICAgIH0sXHJcbiAgICB9O1xyXG4gIH1cclxufTtcclxuXHJcbi8vIEhlbHBlciBmdW5jdGlvbiB0byBkZXJpdmUgdW5pdCBmcm9tIHBhY2thZ2UgcXVhbnRpdHlcclxuZnVuY3Rpb24gZGVyaXZlVW5pdEZyb21QYWNrYWdlUXVhbnRpdHkocGFja2FnZVF1YW50aXR5KSB7XHJcbiAgaWYgKCFwYWNrYWdlUXVhbnRpdHkpIHJldHVybiBcIlVuaXRcIjtcclxuICBjb25zdCBsb3dlciA9IHBhY2thZ2VRdWFudGl0eS50b0xvd2VyQ2FzZSgpO1xyXG4gIGlmIChsb3dlci5pbmNsdWRlcyhcImJveFwiKSkgcmV0dXJuIFwiQm94XCI7XHJcbiAgaWYgKGxvd2VyLmluY2x1ZGVzKFwicGFja1wiKSkgcmV0dXJuIFwiUGFja1wiO1xyXG4gIGlmIChsb3dlci5pbmNsdWRlcyhcInBhaXJcIikpIHJldHVybiBcIlBhaXJcIjtcclxuICByZXR1cm4gXCJVbml0XCI7XHJcbn1cclxuIl0sIm5hbWVzIjpbIkxheW91dCIsIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJ1c2VNZW1vIiwidXNlQ2FsbGJhY2siLCJUb2FzdENvbnRhaW5lciIsInRvYXN0IiwiQWdHcmlkUmVhY3QiLCJGb250QXdlc29tZUljb24iLCJmYVNlYXJjaCIsInVzZUxvYWRpbmciLCJ1c2VSb3V0ZXIiLCJhcGlDb25maWciLCJBZGRQcm9kdWN0RGlhbG9nIiwicHJvZHVjdFN0YXR1c1JlbmRlcmVyIiwicHBlQWN0aW9uUmVuZGVyZXIiLCJCdXR0b24iLCJEaWFsb2ciLCJEaWFsb2dCb2R5IiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ1N1cmZhY2UiLCJEaWFsb2dUaXRsZSIsIkRpYWxvZ1RyaWdnZXIiLCJGbHVlbnRQcm92aWRlciIsIndlYkxpZ2h0VGhlbWUiLCJwcm9kdWN0QWN0aXZlUmVuZGVyZXIiLCJQUEVDb25zdW1hYmxlcyIsInVzZXJEYXRhIiwicm91dGVyIiwicmVxdWVzdFJvd0RhdGEiLCJzZXRSZXF1ZXN0Um93RGF0YSIsInBhZ2VTaXplIiwic2V0UGFnZVNpemUiLCJncmlkUmVmIiwic2V0SXNMb2FkaW5nIiwic2VhcmNoSW5wdXQiLCJzZXRTZWFyY2hJbnB1dCIsImJsb2NrU2NyZWVuIiwic2V0QmxvY2tTY3JlZW4iLCJzZWxlY3RlZFZpZXciLCJzZXRTZWxlY3RlZFZpZXciLCJvcmRlclZpZXciLCJzZXRPcmRlclZpZXciLCJwcm9kdWN0VHlwZXMiLCJzZXRQcm9kdWN0VHlwZXMiLCJzaG93QWRkUHJvZHVjdCIsInNldFNob3dBZGRQcm9kdWN0IiwiaXNFZGl0aW5nUHJvZHVjdCIsInNldElzRWRpdGluZ1Byb2R1Y3QiLCJlZGl0UHJvZHVjdERhdGEiLCJzZXRFZGl0UHJvZHVjdERhdGEiLCJpc1ZhbGlkQ2FuY2VsUmVhc29uIiwic2V0SXNWYWxpZENhbmNlbFJlYXNvbiIsInNpdGVGaWx0ZXIiLCJzZXRTaXRlRmlsdGVyIiwic3RhdHVzRmlsdGVyIiwic2V0U3RhdHVzRmlsdGVyIiwiZGF0ZUZpbHRlciIsInNldERhdGVGaWx0ZXIiLCJzaG93U3RhdHVzRGlhbG9nIiwic2V0U2hvd1N0YXR1c0RpYWxvZyIsInNlbGVjdGVkUm93RGF0YSIsInNldFNlbGVjdGVkUm93RGF0YSIsInN0YXR1c0FjdGlvbiIsInNldFN0YXR1c0FjdGlvbiIsImNhbmNlbGxlZFJlYXNvbmFwaSIsInNldENhbmNlbGxlZFJlYXNvbmFwaSIsInNhdmVkVmlldyIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJmZXRjaFByb2R1Y3RzIiwiZ2V0RGF0YSIsInRoZW4iLCJkYXRhIiwiZ3JvdXBlZCIsInJlZHVjZSIsImFjYyIsInJvdyIsInJlcXVlc3RfaWQiLCJwdXNoIiwiU1RBVFVTX01BUCIsImNvdW50ZXJzIiwiZm9ybWF0dGVkRGF0YSIsIm1hcCIsInNpYmxpbmdzIiwiaW5kZXgiLCJmaW5kSW5kZXgiLCJyIiwicmVxdWVzdF9pdGVtX2lkIiwiaWQiLCJpdGVtX251bWJlciIsInJlcXVpcmVkX2RhdGUiLCJEYXRlIiwidG9JU09TdHJpbmciLCJzcGxpdCIsInNpdGVfbmFtZSIsInByb2R1Y3RfbmFtZSIsInJlcXVlc3RvciIsInVzZXJfbmFtZSIsIm9yaWduYXRvckVtYWlsIiwiYWN0aW9uZWRfYnlfZW1haWwiLCJjb21tZW50IiwiY29tbWVudHMiLCJOYW1lRm9yUHJpbnRpbmciLCJuYW1lX2Zvcl9wcmludGluZyIsInNpemUiLCJxdWFudGl0eSIsInN0YXR1cyIsImFjdGlvbl9pZCIsInByb2R1Y3RzSnNvbiIsInByb2R1Y3RUeXBlc0pzb24iLCJQcm9taXNlIiwiYWxsIiwiZ2V0QWxsUHJvZHVjdHMiLCJnZXRQcm9kdWN0VHlwZXMiLCJBcnJheSIsImlzQXJyYXkiLCJsZW5ndGgiLCJmb3JtYXR0ZWRQcm9kdWN0RGF0YSIsInByb2R1Y3QiLCJwcm9kdWN0X2lkIiwiUHJvZHVjdElkIiwiUHJvZHVjdE5hbWUiLCJjYXRlZ29yeSIsIlByb2R1Y3RUeXBlIiwidHlwZV9pZCIsIlR5cGVJZCIsInN0b2NrIiwic2l6ZXMiLCJBdmFpbGFibGVTaXplcyIsInVuaXQiLCJkZXJpdmVVbml0RnJvbVBhY2thZ2VRdWFudGl0eSIsIlBhY2thZ2VRdWFudGl0eSIsIm5hbWVfcHJpbnRhYmxlIiwiSXNQcm9kdWN0SGlkZGVuIiwiZmFsbGJhY2tQcm9kdWN0RGF0YSIsInNlcnZlckFkZHJlc3MiLCJjb25zb2xlIiwibG9nIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiY3JlZGVudGlhbHMiLCJyZXMiLCJqc29uIiwiZXJyb3IiLCJzZXRUaW1lb3V0IiwibG9nb3V0IiwiRXJyb3IiLCJjYXRjaCIsIm1lc3NhZ2UiLCJoYW5kbGVTaXRlRmlsdGVyQ2hhbmdlIiwidmFsdWUiLCJjdXJyZW50IiwiYXBpIiwiZmlsdGVySW5zdGFuY2UiLCJnZXRGaWx0ZXJJbnN0YW5jZSIsInNldE1vZGVsIiwidHlwZSIsImZpbHRlciIsIm9uRmlsdGVyQ2hhbmdlZCIsImhhbmRsZVN0YXR1c0ZpbHRlckNoYW5nZSIsImhhbmRsZVNlbGVjdGVkVmlldyIsInNlbGVjdGVkVHlwZSIsInNldEl0ZW0iLCJoYW5kbGVEYXRlRmlsdGVyQ2hhbmdlIiwic2VsZWN0ZWREYXRlIiwieXl5eSIsImdldEZ1bGxZZWFyIiwibW0iLCJTdHJpbmciLCJnZXRNb250aCIsInBhZFN0YXJ0IiwiZGQiLCJnZXREYXRlIiwiZm9ybWF0dGVkRGF0ZSIsImRhdGVGcm9tIiwicmVxdWVzdENvbHVtbkRlZnMiLCJoZWFkZXJOYW1lIiwiZmllbGQiLCJmbGV4IiwiaGlkZSIsImZpbHRlclBhcmFtcyIsImNvbXBhcmF0b3IiLCJmaWx0ZXJMb2NhbERhdGVBdE1pZG5pZ2h0IiwiY2VsbFZhbHVlIiwiZGF0ZUFzU3RyaW5nIiwiY2VsbERhdGUiLCJjZWxsRGF0ZUF0TWlkbmlnaHQiLCJzZXRIb3VycyIsInZhbHVlRm9ybWF0dGVyIiwicGFyYW1zIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwidmFsdWVzIiwiY2VsbFJlbmRlcmVyIiwiY2VsbFN0eWxlIiwianVzdGlmeUNvbnRlbnQiLCJjZWxsUmVuZGVyZXJQYXJhbXMiLCJkaXNhYmxlZCIsImRpc3BsYXkiLCJhbGlnbkl0ZW1zIiwiaGVhZGVyQ2xhc3MiLCJzb3J0YWJsZSIsImRlZmF1bHRDb2xEZWYiLCJkZWJvdW5jZU1zIiwicmVzaXphYmxlIiwic3VwcHJlc3NNZW51IiwiaGFuZGxlR3JpZFJlYWR5Iiwic2V0Q29sdW1uRGVmcyIsInJlZnJlc2hIZWFkZXIiLCJyZWZyZXNoQ2VsbHMiLCJvbkZpbHRlclRleHRCb3hDaGFuZ2VkIiwic2V0UXVpY2tGaWx0ZXIiLCJkb2N1bWVudCIsImdldEVsZW1lbnRCeUlkIiwiaGFuZGxlUGFnZVNpemVDaGFuZ2UiLCJldmVudCIsIm5ld1BhZ2VTaXplIiwicGFyc2VJbnQiLCJ0YXJnZXQiLCJwYWdpbmF0aW9uU2V0UGFnZVNpemUiLCJoYW5kbGVDbGVhckZpbHRlcnMiLCJzZXRGaWx0ZXJNb2RlbCIsInRyaW1JbnB1dFRleHQiLCJpbnB1dCIsInRyaW0iLCJ1cGRhdGVTZWxlY3RlZFJvd1N0YXR1cyIsInVwZGF0ZWRTdGF0dXNJRCIsImFwaVBheWxvYWQiLCJyZXF1ZXN0X25vIiwiU2l6ZSIsIlF1YW50aXR5IiwiQ29tbWVudHMiLCJSZXF1ZXN0SXRlbUlEIiwiY29tbWVudE9uVXBkYXRpbmdSZXF1ZXN0IiwiU3VibWl0dGVyVXNlcklEIiwidXNlcl9pZCIsIlN1Ym1pdHRlckVtYWlsIiwiZW1haWwiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInJlZGlyZWN0VXJsIiwiZW5jb2RlVVJJQ29tcG9uZW50Iiwid2luZG93IiwibG9jYXRpb24iLCJwYXRobmFtZSIsInNldExvYWRpbmciLCJtc2ciLCJzdWNjZXNzIiwicG9zaXRpb24iLCJyZXBsYWNlIiwibGltaXQiLCJkaXYiLCJjbGFzc05hbWUiLCJyb2xlX2lkIiwiYnV0dG9uIiwib25DbGljayIsImxhYmVsIiwic2VsZWN0Iiwib25DaGFuZ2UiLCJlIiwib3B0aW9uIiwic3BhbiIsImljb24iLCJwbGFjZWhvbGRlciIsIm9uSW5wdXQiLCJvcGVuIiwib25PcGVuQ2hhbmdlIiwiXyIsIm9uU3VibWl0IiwicHJvZHVjdE5hbWUiLCJwcmludGluZyIsInJlbG9hZCIsInN0eWxlIiwiaGVpZ2h0Iiwicm93RGF0YSIsInJlZiIsInN1cHByZXNzUm93Q2xpY2tTZWxlY3Rpb24iLCJwYWdpbmF0aW9uIiwicGFnaW5hdGlvblBhZ2VTaXplIiwib25QYWdlU2l6ZUNoYW5nZWQiLCJ0b29sdGlwU2hvd0RlbGF5IiwidG9vbHRpcEhpZGVEZWxheSIsIm9uR3JpZFJlYWR5IiwiaHRtbEZvciIsInRoZW1lIiwiZm9udEZhbWlseSIsImRpc2FibGVCdXR0b25FbmhhbmNlbWVudCIsInAiLCJ0ZXh0YXJlYSIsInJvd3MiLCJvbkJsdXIiLCJ0cmltbWVkVmFsdWUiLCJtYXhMZW5ndGgiLCJnYXAiLCJtYXJnaW5Ub3AiLCJkYXRhLW1vZGFsLWhpZGUiLCJwYWNrYWdlUXVhbnRpdHkiLCJsb3dlciIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/ppe-consumable.js\n"));

/***/ })

});
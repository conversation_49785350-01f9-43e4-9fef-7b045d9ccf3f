"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/getUrl.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/getUrl.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _assets_fonts_Poppins_Black_ttf__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../assets/fonts/Poppins-Black.ttf */ \"./assets/fonts/Poppins-Black.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_BlackItalic_ttf__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../assets/fonts/Poppins-BlackItalic.ttf */ \"./assets/fonts/Poppins-BlackItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Bold_ttf__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../assets/fonts/Poppins-Bold.ttf */ \"./assets/fonts/Poppins-Bold.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_BoldItalic_ttf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../assets/fonts/Poppins-BoldItalic.ttf */ \"./assets/fonts/Poppins-BoldItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraBold_ttf__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraBold.ttf */ \"./assets/fonts/Poppins-ExtraBold.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraBoldItalic.ttf */ \"./assets/fonts/Poppins-ExtraBoldItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraLight_ttf__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraLight.ttf */ \"./assets/fonts/Poppins-ExtraLight.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraLightItalic_ttf__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraLightItalic.ttf */ \"./assets/fonts/Poppins-ExtraLightItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Italic_ttf__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../assets/fonts/Poppins-Italic.ttf */ \"./assets/fonts/Poppins-Italic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Light_ttf__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../assets/fonts/Poppins-Light.ttf */ \"./assets/fonts/Poppins-Light.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_LightItalic_ttf__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../assets/fonts/Poppins-LightItalic.ttf */ \"./assets/fonts/Poppins-LightItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Medium_ttf__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../assets/fonts/Poppins-Medium.ttf */ \"./assets/fonts/Poppins-Medium.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_MediumItalic_ttf__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../assets/fonts/Poppins-MediumItalic.ttf */ \"./assets/fonts/Poppins-MediumItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Regular_ttf__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../assets/fonts/Poppins-Regular.ttf */ \"./assets/fonts/Poppins-Regular.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_SemiBold_ttf__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../assets/fonts/Poppins-SemiBold.ttf */ \"./assets/fonts/Poppins-SemiBold.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_SemiBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../assets/fonts/Poppins-SemiBoldItalic.ttf */ \"./assets/fonts/Poppins-SemiBoldItalic.ttf\");\n// Imports\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Black_ttf__WEBPACK_IMPORTED_MODULE_2__);\nvar ___CSS_LOADER_URL_REPLACEMENT_1___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_BlackItalic_ttf__WEBPACK_IMPORTED_MODULE_3__);\nvar ___CSS_LOADER_URL_REPLACEMENT_2___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Bold_ttf__WEBPACK_IMPORTED_MODULE_4__);\nvar ___CSS_LOADER_URL_REPLACEMENT_3___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_BoldItalic_ttf__WEBPACK_IMPORTED_MODULE_5__);\nvar ___CSS_LOADER_URL_REPLACEMENT_4___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraBold_ttf__WEBPACK_IMPORTED_MODULE_6__);\nvar ___CSS_LOADER_URL_REPLACEMENT_5___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_7__);\nvar ___CSS_LOADER_URL_REPLACEMENT_6___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraLight_ttf__WEBPACK_IMPORTED_MODULE_8__);\nvar ___CSS_LOADER_URL_REPLACEMENT_7___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraLightItalic_ttf__WEBPACK_IMPORTED_MODULE_9__);\nvar ___CSS_LOADER_URL_REPLACEMENT_8___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Italic_ttf__WEBPACK_IMPORTED_MODULE_10__);\nvar ___CSS_LOADER_URL_REPLACEMENT_9___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Light_ttf__WEBPACK_IMPORTED_MODULE_11__);\nvar ___CSS_LOADER_URL_REPLACEMENT_10___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_LightItalic_ttf__WEBPACK_IMPORTED_MODULE_12__);\nvar ___CSS_LOADER_URL_REPLACEMENT_11___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Medium_ttf__WEBPACK_IMPORTED_MODULE_13__);\nvar ___CSS_LOADER_URL_REPLACEMENT_12___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_MediumItalic_ttf__WEBPACK_IMPORTED_MODULE_14__);\nvar ___CSS_LOADER_URL_REPLACEMENT_13___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Regular_ttf__WEBPACK_IMPORTED_MODULE_15__);\nvar ___CSS_LOADER_URL_REPLACEMENT_14___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_SemiBold_ttf__WEBPACK_IMPORTED_MODULE_16__);\nvar ___CSS_LOADER_URL_REPLACEMENT_15___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_SemiBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_17__);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/*\\n! tailwindcss v3.3.3 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n*/\\n\\nhtml {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, \\\"Helvetica Neue\\\", Arial, \\\"Noto Sans\\\", sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font family by default.\\n2. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-size: 1em; /* 2 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\n[type='button'],\\n[type='reset'],\\n[type='submit'] {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden] {\\n  display: none;\\n}\\n\\n*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n}\\r\\n.container {\\n  width: 100%;\\n}\\r\\n@media (min-width: 640px) {\\n\\n  .container {\\n    max-width: 640px;\\n  }\\n}\\r\\n@media (min-width: 765px) {\\n\\n  .container {\\n    max-width: 765px;\\n  }\\n}\\r\\n@media (min-width: 1024px) {\\n\\n  .container {\\n    max-width: 1024px;\\n  }\\n}\\r\\n@media (min-width: 1280px) {\\n\\n  .container {\\n    max-width: 1280px;\\n  }\\n}\\r\\n@media (min-width: 1536px) {\\n\\n  .container {\\n    max-width: 1536px;\\n  }\\n}\\r\\n@media (min-width: 1920px) {\\n\\n  .container {\\n    max-width: 1920px;\\n  }\\n}\\r\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\r\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\r\\n.visible {\\n  visibility: visible;\\n}\\r\\n.fixed {\\n  position: fixed;\\n}\\r\\n.\\\\!absolute {\\n  position: absolute !important;\\n}\\r\\n.absolute {\\n  position: absolute;\\n}\\r\\n.relative {\\n  position: relative;\\n}\\r\\n.sticky {\\n  position: sticky;\\n}\\r\\n.inset-0 {\\n  inset: 0px;\\n}\\r\\n.\\\\!right-1 {\\n  right: 0.25rem !important;\\n}\\r\\n.\\\\!right-2 {\\n  right: 0.5rem !important;\\n}\\r\\n.\\\\!top-1 {\\n  top: 0.25rem !important;\\n}\\r\\n.-top-0 {\\n  top: -0px;\\n}\\r\\n.-top-2 {\\n  top: -0.5rem;\\n}\\r\\n.-top-3 {\\n  top: -0.75rem;\\n}\\r\\n.-top-\\\\[0\\\\.9\\\\] {\\n  top: -0.9;\\n}\\r\\n.-top-\\\\[2px\\\\] {\\n  top: -2px;\\n}\\r\\n.bottom-0 {\\n  bottom: 0px;\\n}\\r\\n.bottom-\\\\[-20px\\\\] {\\n  bottom: -20px;\\n}\\r\\n.left-0 {\\n  left: 0px;\\n}\\r\\n.left-1\\\\/2 {\\n  left: 50%;\\n}\\r\\n.left-5 {\\n  left: 1.25rem;\\n}\\r\\n.right-0 {\\n  right: 0px;\\n}\\r\\n.right-1 {\\n  right: 0.25rem;\\n}\\r\\n.right-10 {\\n  right: 2.5rem;\\n}\\r\\n.right-2 {\\n  right: 0.5rem;\\n}\\r\\n.right-5 {\\n  right: 1.25rem;\\n}\\r\\n.top-0 {\\n  top: 0px;\\n}\\r\\n.top-1 {\\n  top: 0.25rem;\\n}\\r\\n.top-1\\\\/2 {\\n  top: 50%;\\n}\\r\\n.top-\\\\[52px\\\\] {\\n  top: 52px;\\n}\\r\\n.top-full {\\n  top: 100%;\\n}\\r\\n.\\\\!z-\\\\[10\\\\] {\\n  z-index: 10 !important;\\n}\\r\\n.\\\\!z-\\\\[9999999\\\\] {\\n  z-index: 9999999 !important;\\n}\\r\\n.\\\\!z-\\\\[9\\\\] {\\n  z-index: 9 !important;\\n}\\r\\n.z-10 {\\n  z-index: 10;\\n}\\r\\n.z-20 {\\n  z-index: 20;\\n}\\r\\n.z-50 {\\n  z-index: 50;\\n}\\r\\n.z-\\\\[5\\\\] {\\n  z-index: 5;\\n}\\r\\n.col-span-2 {\\n  grid-column: span 2 / span 2;\\n}\\r\\n.col-start-6 {\\n  grid-column-start: 6;\\n}\\r\\n.m-1 {\\n  margin: 0.25rem;\\n}\\r\\n.m-3 {\\n  margin: 0.75rem;\\n}\\r\\n.m-4 {\\n  margin: 1rem;\\n}\\r\\n.m-5 {\\n  margin: 1.25rem;\\n}\\r\\n.\\\\!my-3 {\\n  margin-top: 0.75rem !important;\\n  margin-bottom: 0.75rem !important;\\n}\\r\\n.mx-4 {\\n  margin-left: 1rem;\\n  margin-right: 1rem;\\n}\\r\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\r\\n.my-1 {\\n  margin-top: 0.25rem;\\n  margin-bottom: 0.25rem;\\n}\\r\\n.my-2 {\\n  margin-top: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\r\\n.my-32 {\\n  margin-top: 8rem;\\n  margin-bottom: 8rem;\\n}\\r\\n.my-4 {\\n  margin-top: 1rem;\\n  margin-bottom: 1rem;\\n}\\r\\n.my-5 {\\n  margin-top: 1.25rem;\\n  margin-bottom: 1.25rem;\\n}\\r\\n.my-\\\\[24px\\\\] {\\n  margin-top: 24px;\\n  margin-bottom: 24px;\\n}\\r\\n.\\\\!mt-2 {\\n  margin-top: 0.5rem !important;\\n}\\r\\n.\\\\!mt-3 {\\n  margin-top: 0.75rem !important;\\n}\\r\\n.-mt-1 {\\n  margin-top: -0.25rem;\\n}\\r\\n.-mt-\\\\[1px\\\\] {\\n  margin-top: -1px;\\n}\\r\\n.-mt-\\\\[2px\\\\] {\\n  margin-top: -2px;\\n}\\r\\n.mb-0 {\\n  margin-bottom: 0px;\\n}\\r\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\r\\n.mb-10 {\\n  margin-bottom: 2.5rem;\\n}\\r\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\r\\n.mb-20 {\\n  margin-bottom: 5rem;\\n}\\r\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\r\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\r\\n.mb-5 {\\n  margin-bottom: 1.25rem;\\n}\\r\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\r\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\r\\n.mb-\\\\[18px\\\\] {\\n  margin-bottom: 18px;\\n}\\r\\n.mb-\\\\[2px\\\\] {\\n  margin-bottom: 2px;\\n}\\r\\n.me-10 {\\n  margin-inline-end: 2.5rem;\\n}\\r\\n.me-4 {\\n  margin-inline-end: 1rem;\\n}\\r\\n.me-5 {\\n  margin-inline-end: 1.25rem;\\n}\\r\\n.ml-0 {\\n  margin-left: 0px;\\n}\\r\\n.ml-1 {\\n  margin-left: 0.25rem;\\n}\\r\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\r\\n.ml-3 {\\n  margin-left: 0.75rem;\\n}\\r\\n.ml-4 {\\n  margin-left: 1rem;\\n}\\r\\n.ml-5 {\\n  margin-left: 1.25rem;\\n}\\r\\n.ml-6 {\\n  margin-left: 1.5rem;\\n}\\r\\n.ml-8 {\\n  margin-left: 2rem;\\n}\\r\\n.ml-\\\\[18\\\\%\\\\] {\\n  margin-left: 18%;\\n}\\r\\n.ml-\\\\[28px\\\\] {\\n  margin-left: 28px;\\n}\\r\\n.ml-\\\\[2px\\\\] {\\n  margin-left: 2px;\\n}\\r\\n.ml-\\\\[46px\\\\] {\\n  margin-left: 46px;\\n}\\r\\n.ml-auto {\\n  margin-left: auto;\\n}\\r\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\r\\n.mr-12 {\\n  margin-right: 3rem;\\n}\\r\\n.mr-14 {\\n  margin-right: 3.5rem;\\n}\\r\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\r\\n.mr-20 {\\n  margin-right: 5rem;\\n}\\r\\n.mr-3 {\\n  margin-right: 0.75rem;\\n}\\r\\n.mr-4 {\\n  margin-right: 1rem;\\n}\\r\\n.mr-\\\\[23px\\\\] {\\n  margin-right: 23px;\\n}\\r\\n.mr-\\\\[25px\\\\] {\\n  margin-right: 25px;\\n}\\r\\n.mr-\\\\[55px\\\\] {\\n  margin-right: 55px;\\n}\\r\\n.mr-\\\\[5px\\\\] {\\n  margin-right: 5px;\\n}\\r\\n.mt-0 {\\n  margin-top: 0px;\\n}\\r\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\r\\n.mt-14 {\\n  margin-top: 3.5rem;\\n}\\r\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\r\\n.mt-3 {\\n  margin-top: 0.75rem;\\n}\\r\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\r\\n.mt-5 {\\n  margin-top: 1.25rem;\\n}\\r\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\r\\n.mt-7 {\\n  margin-top: 1.75rem;\\n}\\r\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\r\\n.mt-9 {\\n  margin-top: 2.25rem;\\n}\\r\\n.mt-\\\\[17px\\\\] {\\n  margin-top: 17px;\\n}\\r\\n.mt-\\\\[2px\\\\] {\\n  margin-top: 2px;\\n}\\r\\n.mt-\\\\[45px\\\\] {\\n  margin-top: 45px;\\n}\\r\\n.mt-\\\\[60px\\\\] {\\n  margin-top: 60px;\\n}\\r\\n.block {\\n  display: block;\\n}\\r\\n.inline-block {\\n  display: inline-block;\\n}\\r\\n.inline {\\n  display: inline;\\n}\\r\\n.\\\\!flex {\\n  display: flex !important;\\n}\\r\\n.flex {\\n  display: flex;\\n}\\r\\n.inline-flex {\\n  display: inline-flex;\\n}\\r\\n.table {\\n  display: table;\\n}\\r\\n.grid {\\n  display: grid;\\n}\\r\\n.hidden {\\n  display: none;\\n}\\r\\n.\\\\!h-0 {\\n  height: 0px !important;\\n}\\r\\n.\\\\!h-16 {\\n  height: 4rem !important;\\n}\\r\\n.h-10 {\\n  height: 2.5rem;\\n}\\r\\n.h-14 {\\n  height: 3.5rem;\\n}\\r\\n.h-24 {\\n  height: 6rem;\\n}\\r\\n.h-4 {\\n  height: 1rem;\\n}\\r\\n.h-5 {\\n  height: 1.25rem;\\n}\\r\\n.h-52 {\\n  height: 13rem;\\n}\\r\\n.h-6 {\\n  height: 1.5rem;\\n}\\r\\n.h-8 {\\n  height: 2rem;\\n}\\r\\n.h-9 {\\n  height: 2.25rem;\\n}\\r\\n.h-\\\\[100vh\\\\] {\\n  height: 100vh;\\n}\\r\\n.h-\\\\[20px\\\\] {\\n  height: 20px;\\n}\\r\\n.h-\\\\[23px\\\\] {\\n  height: 23px;\\n}\\r\\n.h-\\\\[25px\\\\] {\\n  height: 25px;\\n}\\r\\n.h-\\\\[28px\\\\] {\\n  height: 28px;\\n}\\r\\n.h-\\\\[30px\\\\] {\\n  height: 30px;\\n}\\r\\n.h-\\\\[31px\\\\] {\\n  height: 31px;\\n}\\r\\n.h-\\\\[36px\\\\] {\\n  height: 36px;\\n}\\r\\n.h-\\\\[38px\\\\] {\\n  height: 38px;\\n}\\r\\n.h-\\\\[40px\\\\] {\\n  height: 40px;\\n}\\r\\n.h-\\\\[85vh\\\\] {\\n  height: 85vh;\\n}\\r\\n.h-\\\\[calc\\\\(100vh-100px\\\\)\\\\] {\\n  height: calc(100vh - 100px);\\n}\\r\\n.h-full {\\n  height: 100%;\\n}\\r\\n.h-12 {\\n  height: 3rem;\\n}\\r\\n.h-2 {\\n  height: 0.5rem;\\n}\\r\\n.\\\\!max-h-\\\\[28rem\\\\] {\\n  max-height: 28rem !important;\\n}\\r\\n.\\\\!max-h-\\\\[70vh\\\\] {\\n  max-height: 70vh !important;\\n}\\r\\n.\\\\!max-h-full {\\n  max-height: 100% !important;\\n}\\r\\n.min-h-full {\\n  min-height: 100%;\\n}\\r\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\r\\n.\\\\!w-10 {\\n  width: 2.5rem !important;\\n}\\r\\n.\\\\!w-2\\\\/5 {\\n  width: 40% !important;\\n}\\r\\n.\\\\!w-28 {\\n  width: 7rem !important;\\n}\\r\\n.\\\\!w-32 {\\n  width: 8rem !important;\\n}\\r\\n.\\\\!w-40 {\\n  width: 10rem !important;\\n}\\r\\n.\\\\!w-44 {\\n  width: 11rem !important;\\n}\\r\\n.\\\\!w-48 {\\n  width: 12rem !important;\\n}\\r\\n.\\\\!w-52 {\\n  width: 13rem !important;\\n}\\r\\n.\\\\!w-60 {\\n  width: 15rem !important;\\n}\\r\\n.\\\\!w-72 {\\n  width: 18rem !important;\\n}\\r\\n.\\\\!w-80 {\\n  width: 20rem !important;\\n}\\r\\n.\\\\!w-\\\\[20\\\\%\\\\] {\\n  width: 20% !important;\\n}\\r\\n.\\\\!w-\\\\[450px\\\\] {\\n  width: 450px !important;\\n}\\r\\n.\\\\!w-auto {\\n  width: auto !important;\\n}\\r\\n.\\\\!w-full {\\n  width: 100% !important;\\n}\\r\\n.w-1\\\\/2 {\\n  width: 50%;\\n}\\r\\n.w-1\\\\/3 {\\n  width: 33.333333%;\\n}\\r\\n.w-1\\\\/4 {\\n  width: 25%;\\n}\\r\\n.w-1\\\\/5 {\\n  width: 20%;\\n}\\r\\n.w-1\\\\/6 {\\n  width: 16.666667%;\\n}\\r\\n.w-11 {\\n  width: 2.75rem;\\n}\\r\\n.w-2\\\\/3 {\\n  width: 66.666667%;\\n}\\r\\n.w-2\\\\/5 {\\n  width: 40%;\\n}\\r\\n.w-2\\\\/6 {\\n  width: 33.333333%;\\n}\\r\\n.w-28 {\\n  width: 7rem;\\n}\\r\\n.w-3\\\\/4 {\\n  width: 75%;\\n}\\r\\n.w-4 {\\n  width: 1rem;\\n}\\r\\n.w-5 {\\n  width: 1.25rem;\\n}\\r\\n.w-6 {\\n  width: 1.5rem;\\n}\\r\\n.w-8 {\\n  width: 2rem;\\n}\\r\\n.w-80 {\\n  width: 20rem;\\n}\\r\\n.w-96 {\\n  width: 24rem;\\n}\\r\\n.w-\\\\[10\\\\%\\\\] {\\n  width: 10%;\\n}\\r\\n.w-\\\\[100\\\\%-70px\\\\] {\\n  width: 100%-70px;\\n}\\r\\n.w-\\\\[100px\\\\] {\\n  width: 100px;\\n}\\r\\n.w-\\\\[115px\\\\] {\\n  width: 115px;\\n}\\r\\n.w-\\\\[130px\\\\] {\\n  width: 130px;\\n}\\r\\n.w-\\\\[15\\\\%\\\\] {\\n  width: 15%;\\n}\\r\\n.w-\\\\[150px\\\\] {\\n  width: 150px;\\n}\\r\\n.w-\\\\[160px\\\\] {\\n  width: 160px;\\n}\\r\\n.w-\\\\[20\\\\%\\\\] {\\n  width: 20%;\\n}\\r\\n.w-\\\\[25\\\\%\\\\] {\\n  width: 25%;\\n}\\r\\n.w-\\\\[25px\\\\] {\\n  width: 25px;\\n}\\r\\n.w-\\\\[30\\\\%\\\\] {\\n  width: 30%;\\n}\\r\\n.w-\\\\[30px\\\\] {\\n  width: 30px;\\n}\\r\\n.w-\\\\[40\\\\%\\\\] {\\n  width: 40%;\\n}\\r\\n.w-\\\\[44\\\\%\\\\] {\\n  width: 44%;\\n}\\r\\n.w-\\\\[45\\\\%\\\\] {\\n  width: 45%;\\n}\\r\\n.w-\\\\[47vh\\\\] {\\n  width: 47vh;\\n}\\r\\n.w-\\\\[48\\\\%\\\\] {\\n  width: 48%;\\n}\\r\\n.w-\\\\[5\\\\%\\\\] {\\n  width: 5%;\\n}\\r\\n.w-\\\\[50\\\\%\\\\] {\\n  width: 50%;\\n}\\r\\n.w-\\\\[500px\\\\] {\\n  width: 500px;\\n}\\r\\n.w-\\\\[60\\\\%\\\\] {\\n  width: 60%;\\n}\\r\\n.w-\\\\[75\\\\%\\\\] {\\n  width: 75%;\\n}\\r\\n.w-\\\\[8\\\\%\\\\] {\\n  width: 8%;\\n}\\r\\n.w-\\\\[93\\\\%\\\\] {\\n  width: 93%;\\n}\\r\\n.w-\\\\[94\\\\%\\\\] {\\n  width: 94%;\\n}\\r\\n.w-\\\\[95\\\\%\\\\] {\\n  width: 95%;\\n}\\r\\n.w-\\\\[96\\\\%\\\\] {\\n  width: 96%;\\n}\\r\\n.w-\\\\[calc\\\\(100\\\\%-20px\\\\)\\\\] {\\n  width: calc(100% - 20px);\\n}\\r\\n.w-auto {\\n  width: auto;\\n}\\r\\n.w-full {\\n  width: 100%;\\n}\\r\\n.w-12 {\\n  width: 3rem;\\n}\\r\\n.w-32 {\\n  width: 8rem;\\n}\\r\\n.\\\\!min-w-0 {\\n  min-width: 0px !important;\\n}\\r\\n.\\\\!min-w-fit {\\n  min-width: -moz-fit-content !important;\\n  min-width: fit-content !important;\\n}\\r\\n.min-w-\\\\[200px\\\\] {\\n  min-width: 200px;\\n}\\r\\n.min-w-\\\\[260px\\\\] {\\n  min-width: 260px;\\n}\\r\\n.min-w-full {\\n  min-width: 100%;\\n}\\r\\n.\\\\!max-w-\\\\[60\\\\%\\\\] {\\n  max-width: 60% !important;\\n}\\r\\n.\\\\!max-w-\\\\[600px\\\\] {\\n  max-width: 600px !important;\\n}\\r\\n.\\\\!max-w-\\\\[90\\\\%\\\\] {\\n  max-width: 90% !important;\\n}\\r\\n.max-w-full {\\n  max-width: 100%;\\n}\\r\\n.max-w-md {\\n  max-width: 28rem;\\n}\\r\\n.max-w-7xl {\\n  max-width: 80rem;\\n}\\r\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\r\\n.table-fixed {\\n  table-layout: fixed;\\n}\\r\\n.border-collapse {\\n  border-collapse: collapse;\\n}\\r\\n.-translate-x-1\\\\/2 {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-y-1\\\\/2 {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-rotate-90 {\\n  --tw-rotate: -90deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-180 {\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-45 {\\n  --tw-rotate: 45deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-100 {\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-95 {\\n  --tw-scale-x: .95;\\n  --tw-scale-y: .95;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-x-100 {\\n  --tw-scale-x: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\r\\n.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\r\\n.cursor-default {\\n  cursor: default;\\n}\\r\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\r\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\r\\n.resize-none {\\n  resize: none;\\n}\\r\\n.resize {\\n  resize: both;\\n}\\r\\n.list-inside {\\n  list-style-position: inside;\\n}\\r\\n.list-disc {\\n  list-style-type: disc;\\n}\\r\\n.appearance-none {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n}\\r\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\r\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\r\\n.grid-cols-3 {\\n  grid-template-columns: repeat(3, minmax(0, 1fr));\\n}\\r\\n.grid-cols-4 {\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n}\\r\\n.grid-cols-8 {\\n  grid-template-columns: repeat(8, minmax(0, 1fr));\\n}\\r\\n.\\\\!flex-row {\\n  flex-direction: row !important;\\n}\\r\\n.flex-row {\\n  flex-direction: row;\\n}\\r\\n.flex-col {\\n  flex-direction: column;\\n}\\r\\n.flex-wrap {\\n  flex-wrap: wrap;\\n}\\r\\n.items-start {\\n  align-items: flex-start;\\n}\\r\\n.items-end {\\n  align-items: flex-end;\\n}\\r\\n.\\\\!items-center {\\n  align-items: center !important;\\n}\\r\\n.items-center {\\n  align-items: center;\\n}\\r\\n.items-baseline {\\n  align-items: baseline;\\n}\\r\\n.items-stretch {\\n  align-items: stretch;\\n}\\r\\n.justify-start {\\n  justify-content: flex-start;\\n}\\r\\n.justify-end {\\n  justify-content: flex-end;\\n}\\r\\n.justify-center {\\n  justify-content: center;\\n}\\r\\n.\\\\!justify-between {\\n  justify-content: space-between !important;\\n}\\r\\n.justify-between {\\n  justify-content: space-between;\\n}\\r\\n.justify-around {\\n  justify-content: space-around;\\n}\\r\\n.\\\\!gap-0 {\\n  gap: 0px !important;\\n}\\r\\n.gap-1 {\\n  gap: 0.25rem;\\n}\\r\\n.gap-10 {\\n  gap: 2.5rem;\\n}\\r\\n.gap-12 {\\n  gap: 3rem;\\n}\\r\\n.gap-2 {\\n  gap: 0.5rem;\\n}\\r\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\r\\n.gap-4 {\\n  gap: 1rem;\\n}\\r\\n.gap-5 {\\n  gap: 1.25rem;\\n}\\r\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\r\\n.gap-8 {\\n  gap: 2rem;\\n}\\r\\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\r\\n.overflow-auto {\\n  overflow: auto;\\n}\\r\\n.\\\\!overflow-hidden {\\n  overflow: hidden !important;\\n}\\r\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\r\\n.overflow-x-auto {\\n  overflow-x: auto;\\n}\\r\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\r\\n.whitespace-nowrap {\\n  white-space: nowrap;\\n}\\r\\n.\\\\!rounded-md {\\n  border-radius: 0.375rem !important;\\n}\\r\\n.\\\\!rounded-xl {\\n  border-radius: 0.75rem !important;\\n}\\r\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\r\\n.rounded-\\\\[4px\\\\] {\\n  border-radius: 4px;\\n}\\r\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\r\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\r\\n.rounded-md {\\n  border-radius: 0.375rem;\\n}\\r\\n.rounded-sm {\\n  border-radius: 0.125rem;\\n}\\r\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\r\\n.rounded-2xl {\\n  border-radius: 1rem;\\n}\\r\\n.rounded-b-lg {\\n  border-bottom-right-radius: 0.5rem;\\n  border-bottom-left-radius: 0.5rem;\\n}\\r\\n.rounded-t {\\n  border-top-left-radius: 0.25rem;\\n  border-top-right-radius: 0.25rem;\\n}\\r\\n.rounded-t-lg {\\n  border-top-left-radius: 0.5rem;\\n  border-top-right-radius: 0.5rem;\\n}\\r\\n.rounded-bl-lg {\\n  border-bottom-left-radius: 0.5rem;\\n}\\r\\n.rounded-bl-md {\\n  border-bottom-left-radius: 0.375rem;\\n}\\r\\n.rounded-br-lg {\\n  border-bottom-right-radius: 0.5rem;\\n}\\r\\n.rounded-br-md {\\n  border-bottom-right-radius: 0.375rem;\\n}\\r\\n.rounded-tl-lg {\\n  border-top-left-radius: 0.5rem;\\n}\\r\\n.rounded-tl-md {\\n  border-top-left-radius: 0.375rem;\\n}\\r\\n.rounded-tl-xl {\\n  border-top-left-radius: 0.75rem;\\n}\\r\\n.rounded-tr-lg {\\n  border-top-right-radius: 0.5rem;\\n}\\r\\n.rounded-tr-md {\\n  border-top-right-radius: 0.375rem;\\n}\\r\\n.rounded-tr-xl {\\n  border-top-right-radius: 0.75rem;\\n}\\r\\n.\\\\!border {\\n  border-width: 1px !important;\\n}\\r\\n.\\\\!border-0 {\\n  border-width: 0px !important;\\n}\\r\\n.border {\\n  border-width: 1px;\\n}\\r\\n.border-0 {\\n  border-width: 0px;\\n}\\r\\n.border-2 {\\n  border-width: 2px;\\n}\\r\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\r\\n.border-b-2 {\\n  border-bottom-width: 2px;\\n}\\r\\n.border-e-\\\\[1px\\\\] {\\n  border-inline-end-width: 1px;\\n}\\r\\n.border-r {\\n  border-right-width: 1px;\\n}\\r\\n.border-t {\\n  border-top-width: 1px;\\n}\\r\\n.border-dashed {\\n  border-style: dashed;\\n}\\r\\n.\\\\!border-bright-red {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgb(249 54 71 / var(--tw-border-opacity)) !important;\\n}\\r\\n.\\\\!border-red-500 {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity)) !important;\\n}\\r\\n.\\\\!border-seablue {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgb(0 224 213 / var(--tw-border-opacity)) !important;\\n}\\r\\n.\\\\!border-skin-primary {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgba(var(--color-primary), var(--tw-border-opacity)) !important;\\n}\\r\\n.border-\\\\[\\\\#0066ff\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 102 255 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#FBB522\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(251 181 34 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#cccccc\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(204 204 204 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#d3d3d3\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(211 211 211 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#ddd\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(221 221 221 / var(--tw-border-opacity));\\n}\\r\\n.border-blue-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(191 219 254 / var(--tw-border-opacity));\\n}\\r\\n.border-blue-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(147 197 253 / var(--tw-border-opacity));\\n}\\r\\n.border-blue-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity));\\n}\\r\\n.border-bright-green {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(62 171 88 / var(--tw-border-opacity));\\n}\\r\\n.border-bright-red {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(249 54 71 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-100 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(243 244 246 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(156 163 175 / var(--tw-border-opacity));\\n}\\r\\n.border-green-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity));\\n}\\r\\n.border-light-gray {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(214 214 214 / var(--tw-border-opacity));\\n}\\r\\n.border-light-gray2 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(176 176 176 / var(--tw-border-opacity));\\n}\\r\\n.border-light-gray3 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(211 211 211 / var(--tw-border-opacity));\\n}\\r\\n.border-red-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity));\\n}\\r\\n.border-save-green {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(37 174 101 / var(--tw-border-opacity));\\n}\\r\\n.border-skin-primary {\\n  --tw-border-opacity: 1;\\n  border-color: rgba(var(--color-primary), var(--tw-border-opacity));\\n}\\r\\n.border-theme-blue2 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 102 255 / var(--tw-border-opacity));\\n}\\r\\n.border-transparent {\\n  border-color: transparent;\\n}\\r\\n.border-white {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity));\\n}\\r\\n.border-blue-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(96 165 250 / var(--tw-border-opacity));\\n}\\r\\n.border-red-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity));\\n}\\r\\n.\\\\!bg-\\\\[\\\\#f3f8ff\\\\] {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(243 248 255 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-be-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 154 3 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-confirm-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(51 202 127 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-gray-100 {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-issue-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(251 70 70 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-locked-products {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(211 234 255 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-needsupdate-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 245 208 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-pricechange-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(44 176 250 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-seablue {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(0 224 213 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-transparent {\\n  background-color: transparent !important;\\n}\\r\\n.\\\\!bg-volumechange-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(179 187 221 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-white {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-wip-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 223 55 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.bg-\\\\[\\\\#00E0D5\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 224 213 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#3EAB58\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(62 171 88 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#54C5ED\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(84 197 237 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#F3F8FF\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 248 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#FF6C09\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 108 9 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#FFAE00\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 174 0 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#f3f8ff\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 248 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#ff2929\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 41 41 / var(--tw-bg-opacity));\\n}\\r\\n.bg-be-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 154 3 / var(--tw-bg-opacity));\\n}\\r\\n.bg-black {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity));\\n}\\r\\n.bg-black\\\\/25 {\\n  background-color: rgb(0 0 0 / 0.25);\\n}\\r\\n.bg-blue-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity));\\n}\\r\\n.bg-blue-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity));\\n}\\r\\n.bg-cancelled-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 108 9 / var(--tw-bg-opacity));\\n}\\r\\n.bg-complete-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(62 171 88 / var(--tw-bg-opacity));\\n}\\r\\n.bg-confirm-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 202 127 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity));\\n}\\r\\n.bg-green-300 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(134 239 172 / var(--tw-bg-opacity));\\n}\\r\\n.bg-green-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity));\\n}\\r\\n.bg-green-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity));\\n}\\r\\n.bg-green-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity));\\n}\\r\\n.bg-issue-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(251 70 70 / var(--tw-bg-opacity));\\n}\\r\\n.bg-locked-products {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(211 234 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-needsupdate-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 245 208 / var(--tw-bg-opacity));\\n}\\r\\n.bg-pricechange-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(44 176 250 / var(--tw-bg-opacity));\\n}\\r\\n.bg-qtydiff-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 203 71 / var(--tw-bg-opacity));\\n}\\r\\n.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity));\\n}\\r\\n.bg-red-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity));\\n}\\r\\n.bg-save-green {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 174 101 / var(--tw-bg-opacity));\\n}\\r\\n.bg-skin-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgba(var(--color-primary), var(--tw-bg-opacity));\\n}\\r\\n.bg-skin-primary\\\\/10 {\\n  background-color: rgba(var(--color-primary), 0.1);\\n}\\r\\n.bg-theme-blue {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 45 115 / var(--tw-bg-opacity));\\n}\\r\\n.bg-theme-blue2 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 102 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-transparent {\\n  background-color: transparent;\\n}\\r\\n.bg-volumechange-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(179 187 221 / var(--tw-bg-opacity));\\n}\\r\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-wip-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 223 55 / var(--tw-bg-opacity));\\n}\\r\\n.bg-blue-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity));\\n}\\r\\n.bg-red-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity));\\n}\\r\\n.bg-opacity-25 {\\n  --tw-bg-opacity: 0.25;\\n}\\r\\n.bg-cover {\\n  background-size: cover;\\n}\\r\\n.fill-current {\\n  fill: currentColor;\\n}\\r\\n.fill-gray-300 {\\n  fill: #d1d5db;\\n}\\r\\n.fill-skin-primary {\\n  fill: rgb(var(--color-primary));\\n}\\r\\n.fill-white {\\n  fill: #FFFFFF;\\n}\\r\\n.\\\\!p-0 {\\n  padding: 0px !important;\\n}\\r\\n.\\\\!p-1 {\\n  padding: 0.25rem !important;\\n}\\r\\n.\\\\!p-3 {\\n  padding: 0.75rem !important;\\n}\\r\\n.\\\\!p-5 {\\n  padding: 1.25rem !important;\\n}\\r\\n.p-0 {\\n  padding: 0px;\\n}\\r\\n.p-1 {\\n  padding: 0.25rem;\\n}\\r\\n.p-2 {\\n  padding: 0.5rem;\\n}\\r\\n.p-3 {\\n  padding: 0.75rem;\\n}\\r\\n.p-4 {\\n  padding: 1rem;\\n}\\r\\n.p-6 {\\n  padding: 1.5rem;\\n}\\r\\n.p-8 {\\n  padding: 2rem;\\n}\\r\\n.p-\\\\[6px\\\\] {\\n  padding: 6px;\\n}\\r\\n.p-10 {\\n  padding: 2.5rem;\\n}\\r\\n.\\\\!px-1 {\\n  padding-left: 0.25rem !important;\\n  padding-right: 0.25rem !important;\\n}\\r\\n.\\\\!px-3 {\\n  padding-left: 0.75rem !important;\\n  padding-right: 0.75rem !important;\\n}\\r\\n.\\\\!px-4 {\\n  padding-left: 1rem !important;\\n  padding-right: 1rem !important;\\n}\\r\\n.\\\\!py-1 {\\n  padding-top: 0.25rem !important;\\n  padding-bottom: 0.25rem !important;\\n}\\r\\n.\\\\!py-3 {\\n  padding-top: 0.75rem !important;\\n  padding-bottom: 0.75rem !important;\\n}\\r\\n.px-0 {\\n  padding-left: 0px;\\n  padding-right: 0px;\\n}\\r\\n.px-1 {\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\r\\n.px-14 {\\n  padding-left: 3.5rem;\\n  padding-right: 3.5rem;\\n}\\r\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n.px-2\\\\.5 {\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n}\\r\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\r\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\r\\n.px-5 {\\n  padding-left: 1.25rem;\\n  padding-right: 1.25rem;\\n}\\r\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\r\\n.px-8 {\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\r\\n.px-9 {\\n  padding-left: 2.25rem;\\n  padding-right: 2.25rem;\\n}\\r\\n.px-\\\\[9px\\\\] {\\n  padding-left: 9px;\\n  padding-right: 9px;\\n}\\r\\n.py-0 {\\n  padding-top: 0px;\\n  padding-bottom: 0px;\\n}\\r\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\r\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\r\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\r\\n.py-5 {\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\r\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\r\\n.py-\\\\[5px\\\\] {\\n  padding-top: 5px;\\n  padding-bottom: 5px;\\n}\\r\\n.\\\\!pb-3 {\\n  padding-bottom: 0.75rem !important;\\n}\\r\\n.pb-0 {\\n  padding-bottom: 0px;\\n}\\r\\n.pb-1 {\\n  padding-bottom: 0.25rem;\\n}\\r\\n.pb-2 {\\n  padding-bottom: 0.5rem;\\n}\\r\\n.pb-20 {\\n  padding-bottom: 5rem;\\n}\\r\\n.pb-3 {\\n  padding-bottom: 0.75rem;\\n}\\r\\n.pb-4 {\\n  padding-bottom: 1rem;\\n}\\r\\n.pe-1 {\\n  padding-inline-end: 0.25rem;\\n}\\r\\n.pe-8 {\\n  padding-inline-end: 2rem;\\n}\\r\\n.pl-0 {\\n  padding-left: 0px;\\n}\\r\\n.pl-1 {\\n  padding-left: 0.25rem;\\n}\\r\\n.pl-10 {\\n  padding-left: 2.5rem;\\n}\\r\\n.pl-12 {\\n  padding-left: 3rem;\\n}\\r\\n.pl-2 {\\n  padding-left: 0.5rem;\\n}\\r\\n.pl-3 {\\n  padding-left: 0.75rem;\\n}\\r\\n.pl-4 {\\n  padding-left: 1rem;\\n}\\r\\n.pl-\\\\[2px\\\\] {\\n  padding-left: 2px;\\n}\\r\\n.pl-\\\\[88px\\\\] {\\n  padding-left: 88px;\\n}\\r\\n.pr-12 {\\n  padding-right: 3rem;\\n}\\r\\n.pr-3 {\\n  padding-right: 0.75rem;\\n}\\r\\n.pr-4 {\\n  padding-right: 1rem;\\n}\\r\\n.pr-\\\\[10px\\\\] {\\n  padding-right: 10px;\\n}\\r\\n.pr-\\\\[12px\\\\] {\\n  padding-right: 12px;\\n}\\r\\n.pr-\\\\[18px\\\\] {\\n  padding-right: 18px;\\n}\\r\\n.pr-\\\\[5px\\\\] {\\n  padding-right: 5px;\\n}\\r\\n.pr-\\\\[70px\\\\] {\\n  padding-right: 70px;\\n}\\r\\n.ps-8 {\\n  padding-inline-start: 2rem;\\n}\\r\\n.pt-0 {\\n  padding-top: 0px;\\n}\\r\\n.pt-1 {\\n  padding-top: 0.25rem;\\n}\\r\\n.pt-10 {\\n  padding-top: 2.5rem;\\n}\\r\\n.pt-2 {\\n  padding-top: 0.5rem;\\n}\\r\\n.pt-3 {\\n  padding-top: 0.75rem;\\n}\\r\\n.pt-4 {\\n  padding-top: 1rem;\\n}\\r\\n.pt-5 {\\n  padding-top: 1.25rem;\\n}\\r\\n.pt-6 {\\n  padding-top: 1.5rem;\\n}\\r\\n.pt-\\\\[0\\\\.4rem\\\\] {\\n  padding-top: 0.4rem;\\n}\\r\\n.pt-\\\\[80px\\\\] {\\n  padding-top: 80px;\\n}\\r\\n.\\\\!text-left {\\n  text-align: left !important;\\n}\\r\\n.text-left {\\n  text-align: left;\\n}\\r\\n.\\\\!text-center {\\n  text-align: center !important;\\n}\\r\\n.text-center {\\n  text-align: center;\\n}\\r\\n.align-top {\\n  vertical-align: top;\\n}\\r\\n.align-middle {\\n  vertical-align: middle;\\n}\\r\\n.font-poppinsregular {\\n  font-family: poppinsregular;\\n}\\r\\n.font-poppinssemibold {\\n  font-family: poppinssemibold;\\n}\\r\\n.\\\\!text-base {\\n  font-size: 1rem !important;\\n  line-height: 1.5rem !important;\\n}\\r\\n.\\\\!text-xs {\\n  font-size: 0.75rem !important;\\n  line-height: 1rem !important;\\n}\\r\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\r\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\r\\n.text-\\\\[10px\\\\] {\\n  font-size: 10px;\\n}\\r\\n.text-\\\\[16px\\\\] {\\n  font-size: 16px;\\n}\\r\\n.text-\\\\[20px\\\\] {\\n  font-size: 20px;\\n}\\r\\n.text-base {\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\r\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n.\\\\!font-medium {\\n  font-weight: 500 !important;\\n}\\r\\n.\\\\!font-normal {\\n  font-weight: 400 !important;\\n}\\r\\n.font-bold {\\n  font-weight: 700;\\n}\\r\\n.font-extrabold {\\n  font-weight: 800;\\n}\\r\\n.font-medium {\\n  font-weight: 500;\\n}\\r\\n.font-normal {\\n  font-weight: 400;\\n}\\r\\n.font-semibold {\\n  font-weight: 600;\\n}\\r\\n.uppercase {\\n  text-transform: uppercase;\\n}\\r\\n.capitalize {\\n  text-transform: capitalize;\\n}\\r\\n.italic {\\n  font-style: italic;\\n}\\r\\n.leading-5 {\\n  line-height: 1.25rem;\\n}\\r\\n.leading-8 {\\n  line-height: 2rem;\\n}\\r\\n.leading-\\\\[30px\\\\] {\\n  line-height: 30px;\\n}\\r\\n.leading-relaxed {\\n  line-height: 1.625;\\n}\\r\\n.tracking-\\\\[0\\\\.05em\\\\] {\\n  letter-spacing: 0.05em;\\n}\\r\\n.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\r\\n.tracking-wide {\\n  letter-spacing: 0.025em;\\n}\\r\\n.tracking-wider {\\n  letter-spacing: 0.05em;\\n}\\r\\n.\\\\!text-\\\\[\\\\#333333\\\\] {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(51 51 51 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-gray-300 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(209 213 219 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-gray-500 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(107 114 128 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-gray-700 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(55 65 81 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-red-500 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(239 68 68 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-skin-primary {\\n  --tw-text-opacity: 1 !important;\\n  color: rgba(var(--color-primary), var(--tw-text-opacity)) !important;\\n}\\r\\n.text-\\\\[\\\\#B31312\\\\] {\\n  --tw-text-opacity: 1;\\n  color: rgb(179 19 18 / var(--tw-text-opacity));\\n}\\r\\n.text-\\\\[\\\\#FBB522\\\\] {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 181 34 / var(--tw-text-opacity));\\n}\\r\\n.text-\\\\[\\\\#ffffff\\\\] {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity));\\n}\\r\\n.text-black {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity));\\n}\\r\\n.text-blue-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(59 130 246 / var(--tw-text-opacity));\\n}\\r\\n.text-blue-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity));\\n}\\r\\n.text-blue-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity));\\n}\\r\\n.text-blue-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity));\\n}\\r\\n.text-bright-green {\\n  --tw-text-opacity: 1;\\n  color: rgb(62 171 88 / var(--tw-text-opacity));\\n}\\r\\n.text-bright-red {\\n  --tw-text-opacity: 1;\\n  color: rgb(249 54 71 / var(--tw-text-opacity));\\n}\\r\\n.text-bright-yellow {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 181 34 / var(--tw-text-opacity));\\n}\\r\\n.text-dark-gray {\\n  --tw-text-opacity: 1;\\n  color: rgb(114 114 114 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity));\\n}\\r\\n.text-green-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 197 94 / var(--tw-text-opacity));\\n}\\r\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity));\\n}\\r\\n.text-green-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity));\\n}\\r\\n.text-issue-status {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 70 70 / var(--tw-text-opacity));\\n}\\r\\n.text-light-gray2 {\\n  --tw-text-opacity: 1;\\n  color: rgb(176 176 176 / var(--tw-text-opacity));\\n}\\r\\n.text-orange-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(249 115 22 / var(--tw-text-opacity));\\n}\\r\\n.text-red-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity));\\n}\\r\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity));\\n}\\r\\n.text-skin-a11y {\\n  --tw-text-opacity: 1;\\n  color: rgba(var(--color-a11y), var(--tw-text-opacity));\\n}\\r\\n.text-skin-primary {\\n  --tw-text-opacity: 1;\\n  color: rgba(var(--color-primary), var(--tw-text-opacity));\\n}\\r\\n.text-theme-blue2 {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 102 255 / var(--tw-text-opacity));\\n}\\r\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity));\\n}\\r\\n.text-green-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(21 128 61 / var(--tw-text-opacity));\\n}\\r\\n.text-red-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(185 28 28 / var(--tw-text-opacity));\\n}\\r\\n.underline {\\n  text-decoration-line: underline;\\n}\\r\\n.placeholder-gray-400::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity));\\n}\\r\\n.placeholder-gray-400::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity));\\n}\\r\\n.accent-skin-primary {\\n  accent-color: rgb(var(--color-primary));\\n}\\r\\n.opacity-0 {\\n  opacity: 0;\\n}\\r\\n.opacity-100 {\\n  opacity: 1;\\n}\\r\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\r\\n.opacity-70 {\\n  opacity: 0.7;\\n}\\r\\n.\\\\!shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color) !important;\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;\\n}\\r\\n.shadow {\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-\\\\[0_0_1px_rgba\\\\(0\\\\2c 0\\\\2c 0\\\\2c 0\\\\.1\\\\)\\\\] {\\n  --tw-shadow: 0 0 1px rgba(0,0,0,0.1);\\n  --tw-shadow-colored: 0 0 1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-\\\\[0_0_2px_rgba\\\\(0\\\\2c 0\\\\2c 0\\\\2c 0\\\\.2\\\\)\\\\] {\\n  --tw-shadow: 0 0 2px rgba(0,0,0,0.2);\\n  --tw-shadow-colored: 0 0 2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-\\\\[0_0_5px_rgba\\\\(0\\\\2c 0\\\\2c 0\\\\2c 0\\\\.1\\\\)\\\\] {\\n  --tw-shadow: 0 0 5px rgba(0,0,0,0.1);\\n  --tw-shadow-colored: 0 0 5px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-md {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-xl {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.outline {\\n  outline-style: solid;\\n}\\r\\n.ring-2 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.backdrop-blur-none {\\n  --tw-backdrop-blur: blur(0);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-shadow {\\n  transition-property: box-shadow;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\r\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\r\\n.ease-in {\\n  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);\\n}\\r\\n.ease-in-out {\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\r\\n.ease-out {\\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n}\\r\\n/*@import \\\"bootstrap/bootstrap\\\";*/\\r\\n\\r\\n@font-face {\\r\\n  font-family: poppinsblack;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsblackitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_1___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbold;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_2___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbolditalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_3___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabold;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_4___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabolditalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_5___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralight;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_6___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralightitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_7___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_8___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslight;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_9___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslightitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_10___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmedium;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_11___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmediumitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_12___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsregular;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_13___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibold;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_14___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibolditalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_15___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_8___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_8___ + \");\\r\\n}\\r\\n\\r\\nbody,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  overflow-x: hidden;\\r\\n  background-color: #f3f8ff;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark body,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\" !important;\\r\\n  overflow-x: hidden;\\r\\n  background-color: #0e0e10;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.ag-header .ag-header-cell.header-with-border  {\\r\\n  border-bottom: 1px solid #ccc;\\r\\n}\\r\\n\\r\\n.ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\nbutton[disabled] {\\r\\n  opacity: 0.5;\\r\\n  cursor: not-allowed;\\r\\n}\\r\\n\\r\\n.dark .ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #fff;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell {\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark .ag-ltr .ag-cell {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-panel {\\r\\n  justify-content: between;\\r\\n}\\r\\n.dark .ag-paging-panel {\\r\\n  justify-content: between;\\r\\n  color: white;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.dark .ag-center-cols-viewport {\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.contentsectionbg {\\r\\n  background-color: white;\\r\\n}\\r\\n\\r\\n.dark .contentsectionbg {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark\\r\\n  .ag-header.ag-header-allow-overflow\\r\\n  .ag-header-row\\r\\n  .ag-root-wrapper.ag-layout-normal {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .ag-header-container {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .pagination-style {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.dark .ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n.ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: #000;\\r\\n}\\r\\n.dark .ag-paging-button.ag-disabled {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-body-horizontal-scroll-viewport {\\r\\n  display: none;\\r\\n} \\r\\n\\r\\n\\r\\n.ag-overlay-no-rows-center {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:hover,\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:focus {\\r\\n  color: #ccc;\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\nnav:hover .forAnimate {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n.mainmenu {\\r\\n  background: #002d73;\\r\\n  border: #002d73;\\r\\n  border-radius: 0px;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > .active > a,\\r\\n.mainmenu .navbar-nav > .active > a:hover,\\r\\n.mainmenu .navbar-nav > .active > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: #2a3344;\\r\\n  border-left: 2px solid #1ca9c0;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > li > a:hover,\\r\\n.mainmenu .navbar-nav > li > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: lightgray;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n}\\r\\n\\r\\n.navbar-default .navbar-nav > li > a {\\r\\n  color: #00b1a3;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n  text-decoration: none;\\r\\n}\\r\\n\\r\\n.navbar {\\r\\n  min-height: 45px;\\r\\n}\\r\\n\\r\\n.page-heading {\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n  margin-top: 0px !important;\\r\\n  margin-left: 25px;\\r\\n  /* position: absolute; */\\r\\n  top: 10px;\\r\\n  left: 90px;\\r\\n  font-weight: 600;\\r\\n}\\r\\n\\r\\n.page-heading h2 {\\r\\n  font-size: 16px;\\r\\n  margin-top: 15px !important;\\r\\n}\\r\\n\\r\\n/* ----------- Notification dropdown end ----------------------- */\\r\\n\\r\\nnav.sidebar .brand a {\\r\\n  padding: 0;\\r\\n}\\r\\n\\r\\n.brand {\\r\\n  font-size: 24px;\\r\\n  /* padding: 0px 5px; */\\r\\n  color: #fff;\\r\\n  width: 69px;\\r\\n  /* background-color: #002d73; */\\r\\n  text-align: center;\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.titlebar {\\r\\n  width: 100%;\\r\\n  position: fixed;\\r\\n  /* height: 55px; */\\r\\n  z-index: 98;\\r\\n  top: 0;\\r\\n  padding-left: 69px;\\r\\n}\\r\\n\\r\\n.accordion {\\r\\n  cursor: pointer;\\r\\n  width: 100%;\\r\\n  text-align: left;\\r\\n  outline: none;\\r\\n  transition: 0.4s;\\r\\n}\\r\\n\\r\\n.active,\\r\\n.accordion:hover {\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\n.panel {\\r\\n  background-color: white;\\r\\n  max-height: 0;\\r\\n  overflow: hidden;\\r\\n  transition: max-height 0.2s ease-out;\\r\\n}\\r\\n\\r\\n/* .reactSelectCustom .css-1fdsijx-ValueContainer , .reactSelectCustom .css-b62m3t-ValueContainer {\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n}\\r\\n\\r\\n.reactSelectCustom .css-1hb7zxy-IndicatorsContainer, .reactSelectCustom .css-1xc3v61-IndicatorsContainer{\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n} */\\r\\n/* .reactSelectCustom .css-1jgx7bw-control{\\r\\n  flex-wrap: nowrap !important;\\r\\n} */\\r\\ninput[type=date]:invalid::-ms-datetime-edit {\\r\\n  color: #808080;\\r\\n}\\r\\n/* label {\\r\\n  font-size: 14px;\\r\\n  color: #505050;\\r\\n} */\\r\\n.top-navbar {\\r\\n  background-color: white;\\r\\n}\\r\\n.dark .top-navbar {\\r\\n  background-color: #000;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.dark .pageName {\\r\\n  color: white;\\r\\n}\\r\\n\\r\\ninput[type=\\\"number\\\"] {\\r\\n  -webkit-appearance: textfield;\\r\\n     -moz-appearance: textfield;\\r\\n          appearance: textfield;\\r\\n}\\r\\ninput[type=number]::-webkit-inner-spin-button, \\r\\ninput[type=number]::-webkit-outer-spin-button { \\r\\n  -webkit-appearance: none;\\r\\n}\\r\\n\\r\\ninput[type=\\\"text\\\"],\\r\\ninput[type=\\\"tel\\\"],\\r\\ninput[type=\\\"date\\\"],\\r\\ninput[type=\\\"email\\\"],\\r\\ninput[type=\\\"number\\\"],\\r\\nselect {\\r\\n  padding-top: 4px;\\r\\n  padding-bottom: 4px;\\r\\n  border-color: #d6d6d6;\\r\\n  outline: 2px solid #ffffff;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\ninput:focus, select:focus , textarea:focus{\\r\\n  outline-color: #0066ff;\\r\\n}\\r\\n\\r\\n.button {\\r\\n  padding: 4px 12px 4px 12px;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #505050;\\r\\n}\\r\\n.dark .labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #AFAFAF;\\r\\n  background-color: #FFFFFF;\\r\\n}\\r\\n\\r\\n.dark .searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #c0c0c0;\\r\\n  background-color: #1d212d;\\r\\n  outline-color: #1d212d;\\r\\n}\\r\\n\\r\\n.checkboxbg {\\r\\n  background-color: #D9D9D9;\\r\\n}\\r\\n.dark .checkboxbg {\\r\\n  background-color: #4d4d4d;\\r\\n}\\r\\n\\r\\n.dark select {\\r\\n  background-color: #1d1d1d;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.borderc-theme-blue2 {\\r\\n  border-color: #0066ff;\\r\\n}\\r\\n.dark .borderc-theme-blue2 {\\r\\n  border-color: #6699ff;\\r\\n}\\r\\n.textc-theme-blue2 {\\r\\n  color: #0066ff;\\r\\n}\\r\\n.dark .textc-theme-blue2 {\\r\\n  color: #6699ff;\\r\\n}\\r\\n\\r\\n.bgc-theme-blue2 {\\r\\n  background-color: #0066ff;\\r\\n}\\r\\n.dark .bgc-theme-blue2 {\\r\\n  background-color: #6699ff;\\r\\n}\\r\\n.textc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .textc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n.borderc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .borderc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n\\r\\n.input {\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .inputs {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.confirmInputs {\\r\\n    font-size: 12px;\\r\\n    color: #333333;\\r\\n    font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.formtitle {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.subtitles {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.text-blackcolor {\\r\\n  color:#505050;\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within {\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-row-odd{\\r\\n  background-color: #f3f3f3 !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value span {\\r\\n  height: auto !important;\\r\\n  line-height: normal !important;\\r\\n}\\r\\n\\r\\n.ag-cell{\\r\\n  display:flex;\\r\\n  align-items:center;\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value, .ag-header-cell-text{\\r\\n  text-overflow: unset !important;\\r\\n}\\r\\n\\r\\n.pagination-style {\\r\\n  position: absolute;\\r\\n  bottom: 10px;\\r\\n  left: 20px;\\r\\n}\\r\\n\\r\\n.pagination-style select {\\r\\n  background-color: #f2f2f2;\\r\\n  border-radius: 3px;\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-header-select-all{\\r\\n  margin-right: 12px !important; \\r\\n}\\r\\n\\r\\n.ag-root-wrapper.ag-layout-normal{\\r\\n  border-radius: 5px !important;\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-row.ag-header-row-column {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-center-cols-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.general_section .ag-header-container, .general_section .ag-center-cols-container, .general_section .ag-header-row  {\\r\\n  width: 100% !important;\\r\\n}\\r\\n\\r\\n.general_section .ag-cell {\\r\\n  /* display: flex; */\\r\\n  margin-right: 10px;\\r\\n}\\r\\n\\r\\n.product_link_def .ag-row .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.product_data_def .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n/* @media (min-width: 640px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 10px;\\r\\n  }\\r\\n} */\\r\\n\\r\\n.viewlog .ag-cell-value {\\r\\n    align-items: center;\\r\\n    display: flex;\\r\\n}\\r\\n\\r\\n@media (min-width: 765px) {\\r\\n  .main {\\r\\n    position: absolute;\\r\\n    width: calc(100% - 40px);\\r\\n    margin-left: 40px;\\r\\n    float: right;\\r\\n  }\\r\\n  nav.sidebar:hover + .main {\\r\\n    margin-left: 200px;\\r\\n  }\\r\\n  nav.sidebar.navbar.sidebar > .container .navbar-brand,\\r\\n  .navbar > .container-fluid .navbar-brand {\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  nav.sidebar .navbar-brand,\\r\\n  nav.sidebar .navbar-header {\\r\\n    text-align: center;\\r\\n    width: 100%;\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  /* nav.sidebar a {\\r\\n    padding-bottom: 34px;\\r\\n  } */\\r\\n\\r\\n  nav.sidebar .navbar-nav > li {\\r\\n    font-size: 13px;\\r\\n  }\\r\\n  nav.sidebar .navbar-nav .open .dropdown-menu {\\r\\n    position: static;\\r\\n    float: none;\\r\\n    width: auto;\\r\\n    margin-top: 0;\\r\\n    background-color: transparent;\\r\\n    border: 0;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  nav.sidebar .navbar-collapse,\\r\\n  nav.sidebar .container-fluid {\\r\\n    padding: 0 0px 0 0px;\\r\\n  }\\r\\n  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {\\r\\n    color: #777;\\r\\n  }\\r\\n  nav.sidebar {\\r\\n    width: 69px;\\r\\n    height: 100%;\\r\\n    margin-bottom: 0px;\\r\\n    position: fixed;\\r\\n    top: 0px;\\r\\n    display: flex;\\r\\n    align-items: flex-start;\\r\\n  }\\r\\n  nav.sidebar li {\\r\\n    width: 100%;\\r\\n  }\\r\\n\\r\\n  .forAnimate {\\r\\n    opacity: 0;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n@media (min-width: 1024px) {\\r\\n  \\r\\n}\\r\\n\\r\\n/* @media (min-width: 1280px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 14px;\\r\\n  }}\\r\\n\\r\\n@media (min-width: 1536px) {\\r\\n  \\r\\n}\\r\\n@media (min-width: 1940px) {\\r\\n  \\r\\n} */\\r\\n\\r\\n.desktop-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n.block-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(214, 21, 21, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n\\r\\n.message-content {\\r\\n  text-align: center;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.message-content h2 {\\r\\n  font-size: 24px;\\r\\n  margin-bottom: 10px;\\r\\n}\\r\\n\\r\\n.message-content p {\\r\\n  font-size: 16px;\\r\\n}\\r\\n/* quarter filter css */\\r\\n\\r\\n #q1:checked  + .labelcheck, #q2:checked  + .labelcheck, #q3:checked  + .labelcheck, #q4:checked  + .labelcheck, #all:checked  + .labelcheck, #needsupdate:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* section filter css */\\r\\n #volume:checked  + .labelcheck, #breakeven:checked  + .labelcheck, #unitprice:checked  + .labelcheck, #grossprofit:checked  + .labelcheck, #gppercent:checked  + .labelcheck, #value:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n #bestatus-1:checked  + .labelcheck {\\r\\n  background-color: #FF9A03;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-2:checked  + .labelcheck{\\r\\n  background-color: #33CA7F;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#444;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-3:checked  + .labelcheck{\\r\\n  background-color: #FFDF37;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-4:checked  + .labelcheck{\\r\\n  background-color: #FB4646;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.planningtoolgrid\\r\\n{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n   /* border: 1px solid #ddd; */\\r\\n}\\r\\n.input-number::-webkit-inner-spin-button,\\r\\n.input-number::-webkit-outer-spin-button {\\r\\n  -webkit-appearance: none;\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n\\r\\n.planningtoolgrid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: center;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* .planningtoolgrid thead tr, .planningtoolgrid tbody tr{\\r\\n  border: 1px solid #ddd;\\r\\n} */\\r\\n.planningtoolgrid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n}\\r\\n/* .planningtoolgrid tbody tr{\\r\\n height:70px !important;\\r\\n} */\\r\\n.planningtoolgrid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n}\\r\\n.planningtoolgrid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.planningtoolgrid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  background:#f3f8ff;\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.quartertotals{\\r\\n  /* border:0; */\\r\\n  table-layout: fixed;\\r\\n  width:50%;\\r\\n  text-align:center;\\r\\n}\\r\\n.quartertotals thead th, .quartertotals tbody td{\\r\\n  text-align: center;\\r\\n  border:0;\\r\\n}\\r\\n.quartertotals thead tr, .quartertotals tbody tr {\\r\\n    border: none;\\r\\n}\\r\\n.quartertotals thead th{\\r\\n  border-top-left-radius: 20px;\\r\\n  border-top-right-radius: 20px;\\r\\n  background-color: #fff !important;\\r\\n}\\r\\n.quartertotals thead{\\r\\n  z-index: 5 !important;\\r\\n}\\r\\n\\r\\n.quartertotals thead th:first-child, .quartertotals tbody td:first-child{\\r\\n  background-color: #f3f8ff !important;\\r\\n  width:50px !important;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(2){\\r\\n  background-color: #201E50 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #504F66;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(3){\\r\\n  background-color: #D499B9 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #C0749E;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(4){\\r\\n  background-color: #EE6C4D !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #CE4B2C;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody tr:last-child td{\\r\\n    border-bottom-left-radius: 15px;\\r\\n    border-bottom-right-radius: 15px;\\r\\n    border-bottom: none;\\r\\n}\\r\\n\\r\\n.titlerow{\\r\\n  background-color: #00E0D5;\\r\\n  color:#fff;\\r\\n  z-index: 9;\\r\\n  font-size: 15px;\\r\\n  height: 28px !important;\\r\\n}\\r\\ntd.sectionrow,th.sectionrow{\\r\\n  background-color: #DDD;\\r\\n  color:#444;\\r\\n  font-weight: 600;\\r\\n  z-index: 9;\\r\\n  height: 28px !important;\\r\\n  font-size: 13px !important;\\r\\n}\\r\\ninput[type=\\\"text\\\"]:disabled, input[type=\\\"number\\\"]:disabled, select:disabled{\\r\\n  background-color: #F6F3F3;\\r\\n}\\r\\n\\r\\n#wrapper.closed .list {\\r\\n  display: none;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n#wrapper.open .list {\\r\\n  display: block;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n.bg-currentWeek{\\r\\n  background-color: #fcf6b1;\\r\\n}\\r\\n.nodata{\\r\\n  background-color:#ffecd4;\\r\\n  color:#b31818\\r\\n}\\r\\n.selected{\\r\\n  color: #0066FF;\\r\\n  border-bottom: 3px solid #0066FF;\\r\\n}\\r\\n.buttonText{\\r\\n  font-family: 'poppinsmedium';\\r\\n  \\r\\n}\\r\\n\\r\\n/*--- SLP CSS ---*/\\r\\n\\r\\n.service-level-grid{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n}\\r\\n\\r\\n.service-level-grid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: left;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  white-space: normal;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n/* .service-level-grid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n} */\\r\\n.service-level-grid thead tr th:last-child{\\r\\n  background:#f3f8ff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n.service-level-grid tbody tr td:last-child{\\r\\n  background:#fff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n\\r\\n.service-level-grid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n  border: 1px solid #ddd;\\r\\n  padding:5px;\\r\\n  z-index: 5;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  background-color:white;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  /* background:#f3f8ff; */\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.ag-grid-checkbox-cell .ag-checkbox-input {\\r\\n  cursor: pointer;\\r\\n}\\r\\n\\r\\n.depotdaterange .rdrDateDisplayWrapper{\\r\\n  display:none;\\r\\n}\\r\\n\\r\\nlabel{\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\nselect::-moz-placeholder {\\r\\n  color: #333333 !important;\\r\\n  opacity: 0.5;\\r\\n  font-weight: 500;\\r\\n  font-family:\\\"poppinsregular\\\";\\r\\n}\\r\\nselect::placeholder, .css-1jqq78o-placeholder, .placeholdertext {\\r\\n  color: #333333 !important;\\r\\n  opacity: 0.5;\\r\\n  font-weight: 500;\\r\\n  font-family:\\\"poppinsregular\\\";\\r\\n}\\r\\n.text-truncate2L {\\r\\n  display: -webkit-box;\\r\\n  -webkit-box-orient: vertical;\\r\\n  -webkit-line-clamp: 2;\\r\\n  overflow: hidden;\\r\\n  text-overflow: ellipsis;\\r\\n  }\\r\\n\\r\\n  .variety-disabled-block{\\r\\n    background-color: #f6f6f6;\\r\\n    color: #888888;\\r\\n    border:1px solid #E2E2E2;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  .variety-disabled-block h4, .variety-disabled-block label{\\r\\n    color:#797979;\\r\\n  }\\r\\n  .after\\\\:absolute::after {\\n  content: var(--tw-content);\\n  position: absolute;\\n}\\r\\n  .after\\\\:left-\\\\[1px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 1px;\\n}\\r\\n  .after\\\\:top-2::after {\\n  content: var(--tw-content);\\n  top: 0.5rem;\\n}\\r\\n  .after\\\\:h-5::after {\\n  content: var(--tw-content);\\n  height: 1.25rem;\\n}\\r\\n  .after\\\\:w-5::after {\\n  content: var(--tw-content);\\n  width: 1.25rem;\\n}\\r\\n  .after\\\\:translate-x-0::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n  .after\\\\:rounded-full::after {\\n  content: var(--tw-content);\\n  border-radius: 9999px;\\n}\\r\\n  .after\\\\:border::after {\\n  content: var(--tw-content);\\n  border-width: 1px;\\n}\\r\\n  .after\\\\:border-light-gray::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(214 214 214 / var(--tw-border-opacity));\\n}\\r\\n  .after\\\\:bg-white::after {\\n  content: var(--tw-content);\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity));\\n}\\r\\n  .after\\\\:transition-all::after {\\n  content: var(--tw-content);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n  .after\\\\:content-\\\\[\\\\'\\\\'\\\\]::after {\\n  --tw-content: '';\\n  content: var(--tw-content);\\n}\\r\\n  .focus-within\\\\:text-gray-600:focus-within {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity));\\n}\\r\\n  .hover\\\\:cursor-pointer:hover {\\n  cursor: pointer;\\n}\\r\\n  .hover\\\\:border-gray-400:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(156 163 175 / var(--tw-border-opacity));\\n}\\r\\n  .hover\\\\:bg-blue-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-confirm-status\\\\/80:hover {\\n  background-color: rgb(51 202 127 / 0.8);\\n}\\r\\n  .hover\\\\:bg-gray-100:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-gray-200:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-gray-300:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-gray-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-gray-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-red-500:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-red-600:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-skin-primary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgba(var(--color-primary), var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-blue-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-blue-600:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:text-black:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity));\\n}\\r\\n  .hover\\\\:text-gray-900:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity));\\n}\\r\\n  .hover\\\\:text-blue-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity));\\n}\\r\\n  .hover\\\\:underline:hover {\\n  text-decoration-line: underline;\\n}\\r\\n  .hover\\\\:opacity-80:hover {\\n  opacity: 0.8;\\n}\\r\\n  .focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n  .focus\\\\:outline-2:focus {\\n  outline-width: 2px;\\n}\\r\\n  .focus\\\\:\\\\!outline-skin-primary:focus {\\n  outline-color: rgb(var(--color-primary)) !important;\\n}\\r\\n  .focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n  .focus\\\\:ring-4:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n  .focus\\\\:ring-blue-300:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity));\\n}\\r\\n  .focus\\\\:ring-blue-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));\\n}\\r\\n  .focus\\\\:ring-indigo-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity));\\n}\\r\\n  .focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n  .disabled\\\\:bg-\\\\[\\\\#F6F3F3\\\\]:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(246 243 243 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:bg-blue-400:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:bg-gray-100:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:bg-gray-400:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:bg-neutral-100:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(245 245 245 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:bg-slate-600:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:text-gray-400:disabled {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity));\\n}\\r\\n  .disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\r\\n  .group:hover .group-hover\\\\:block {\\n  display: block;\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity));\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:bg-skin-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgba(var(--color-primary), var(--tw-bg-opacity));\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:after\\\\:left-\\\\[3px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 3px;\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:after\\\\:translate-x-full::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:after\\\\:border-white::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity));\\n}\\r\\n  @media (max-width: 1600px) {\\n\\n  .max-\\\\[1600px\\\\]\\\\:hidden {\\n    display: none;\\n  }\\n}\\r\\n  @media not all and (min-width: 1024px) {\\n\\n  .max-lg\\\\:gap-10 {\\n    gap: 2.5rem;\\n  }\\n}\\r\\n  @media (min-width: 765px) {\\n\\n  .md\\\\:mb-3 {\\n    margin-bottom: 0.75rem;\\n  }\\n\\n  .md\\\\:mb-6 {\\n    margin-bottom: 1.5rem;\\n  }\\n\\n  .md\\\\:ml-\\\\[15\\\\%\\\\] {\\n    margin-left: 15%;\\n  }\\n\\n  .md\\\\:ml-\\\\[55px\\\\] {\\n    margin-left: 55px;\\n  }\\n\\n  .md\\\\:mr-12 {\\n    margin-right: 3rem;\\n  }\\n\\n  .md\\\\:w-1\\\\/2 {\\n    width: 50%;\\n  }\\n\\n  .md\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .md\\\\:w-fit {\\n    width: -moz-fit-content;\\n    width: fit-content;\\n  }\\n\\n  .md\\\\:w-full {\\n    width: 100%;\\n  }\\n\\n  .md\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .md\\\\:flex-col {\\n    flex-direction: column;\\n  }\\n\\n  .md\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .md\\\\:py-2 {\\n    padding-top: 0.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .md\\\\:text-base {\\n    font-size: 1rem;\\n    line-height: 1.5rem;\\n  }\\n}\\r\\n  @media (min-width: 1024px) {\\n\\n  .lg\\\\:mb-0 {\\n    margin-bottom: 0px;\\n  }\\n\\n  .lg\\\\:mb-6 {\\n    margin-bottom: 1.5rem;\\n  }\\n\\n  .lg\\\\:ml-\\\\[60px\\\\] {\\n    margin-left: 60px;\\n  }\\n\\n  .lg\\\\:mr-14 {\\n    margin-right: 3.5rem;\\n  }\\n\\n  .lg\\\\:mt-0 {\\n    margin-top: 0px;\\n  }\\n\\n  .lg\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .lg\\\\:w-\\\\[95\\\\%\\\\] {\\n    width: 95%;\\n  }\\n\\n  .lg\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .lg\\\\:w-full {\\n    width: 100%;\\n  }\\n\\n  .lg\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .lg\\\\:py-1 {\\n    padding-top: 0.25rem;\\n    padding-bottom: 0.25rem;\\n  }\\n\\n  .lg\\\\:pl-0 {\\n    padding-left: 0px;\\n  }\\n\\n  .lg\\\\:text-sm {\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n}\\r\\n  @media (min-width: 1280px) {\\n\\n  .xl\\\\:mb-6 {\\n    margin-bottom: 1.5rem;\\n  }\\n\\n  .xl\\\\:mt-20 {\\n    margin-top: 5rem;\\n  }\\n\\n  .xl\\\\:mt-4 {\\n    margin-top: 1rem;\\n  }\\n\\n  .xl\\\\:mt-6 {\\n    margin-top: 1.5rem;\\n  }\\n\\n  .xl\\\\:block {\\n    display: block;\\n  }\\n\\n  .xl\\\\:inline {\\n    display: inline;\\n  }\\n\\n  .xl\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .xl\\\\:w-1\\\\/2 {\\n    width: 50%;\\n  }\\n\\n  .xl\\\\:w-3\\\\/5 {\\n    width: 60%;\\n  }\\n\\n  .xl\\\\:w-\\\\[100\\\\%\\\\] {\\n    width: 100%;\\n  }\\n\\n  .xl\\\\:w-\\\\[20\\\\%\\\\] {\\n    width: 20%;\\n  }\\n\\n  .xl\\\\:w-\\\\[40\\\\%\\\\] {\\n    width: 40%;\\n  }\\n\\n  .xl\\\\:w-\\\\[48\\\\%\\\\] {\\n    width: 48%;\\n  }\\n\\n  .xl\\\\:w-\\\\[65\\\\%\\\\] {\\n    width: 65%;\\n  }\\n\\n  .xl\\\\:w-\\\\[70\\\\%\\\\] {\\n    width: 70%;\\n  }\\n\\n  .xl\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .xl\\\\:gap-6 {\\n    gap: 1.5rem;\\n  }\\n\\n  .xl\\\\:border-e {\\n    border-inline-end-width: 1px;\\n  }\\n\\n  .xl\\\\:border-e-\\\\[1px\\\\] {\\n    border-inline-end-width: 1px;\\n  }\\n\\n  .xl\\\\:pe-8 {\\n    padding-inline-end: 2rem;\\n  }\\n\\n  .xl\\\\:pl-5 {\\n    padding-left: 1.25rem;\\n  }\\n\\n  .xl\\\\:ps-8 {\\n    padding-inline-start: 2rem;\\n  }\\n\\n  .xl\\\\:text-lg {\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .xl\\\\:tracking-normal {\\n    letter-spacing: 0em;\\n  }\\n}\\r\\n  @media (min-width: 1536px) {\\n\\n  .\\\\32xl\\\\:mb-0 {\\n    margin-bottom: 0px;\\n  }\\n\\n  .\\\\32xl\\\\:mb-0\\\\.5 {\\n    margin-bottom: 0.125rem;\\n  }\\n\\n  .\\\\32xl\\\\:block {\\n    display: block;\\n  }\\n\\n  .\\\\32xl\\\\:h-\\\\[calc\\\\(100\\\\%-60px\\\\)\\\\] {\\n    height: calc(100% - 60px);\\n  }\\n\\n  .\\\\32xl\\\\:h-full {\\n    height: 100%;\\n  }\\n\\n  .\\\\32xl\\\\:w-1\\\\/5 {\\n    width: 20%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[50\\\\%\\\\] {\\n    width: 50%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[55\\\\%\\\\] {\\n    width: 55%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[60\\\\%\\\\] {\\n    width: 60%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[calc\\\\(100\\\\%-70px\\\\)\\\\] {\\n    width: calc(100% - 70px);\\n  }\\n\\n  .\\\\32xl\\\\:\\\\!max-w-\\\\[70\\\\%\\\\] {\\n    max-width: 70% !important;\\n  }\\n\\n  .\\\\32xl\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .\\\\32xl\\\\:gap-3 {\\n    gap: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:p-3 {\\n    padding: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-3 {\\n    padding-left: 0.75rem;\\n    padding-right: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-3\\\\.5 {\\n    padding-left: 0.875rem;\\n    padding-right: 0.875rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-4 {\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n\\n  .\\\\32xl\\\\:py-1 {\\n    padding-top: 0.25rem;\\n    padding-bottom: 0.25rem;\\n  }\\n\\n  .\\\\32xl\\\\:py-2 {\\n    padding-top: 0.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .\\\\32xl\\\\:py-3 {\\n    padding-top: 0.75rem;\\n    padding-bottom: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:pt-1 {\\n    padding-top: 0.25rem;\\n  }\\n\\n  .\\\\32xl\\\\:pt-1\\\\.5 {\\n    padding-top: 0.375rem;\\n  }\\n\\n  .\\\\32xl\\\\:text-2xl {\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n\\n  .\\\\32xl\\\\:text-lg {\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n}\\r\\n  @media (min-width: 1600px) {\\n\\n  .min-\\\\[1600px\\\\]\\\\:w-\\\\[23\\\\%\\\\] {\\n    width: 23%;\\n  }\\n\\n  .min-\\\\[1600px\\\\]\\\\:w-\\\\[42\\\\%\\\\] {\\n    width: 42%;\\n  }\\n}\\r\\n  @media (min-width: 1610px) {\\n\\n  .min-\\\\[1610px\\\\]\\\\:inline {\\n    display: inline;\\n  }\\n\\n  .min-\\\\[1610px\\\\]\\\\:w-\\\\[35\\\\%\\\\] {\\n    width: 35%;\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\",\"<no source>\"],\"names\":[],\"mappings\":\"AAAA;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;CAAc;;AAAd;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,4NAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd;AAAc;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AACpB;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sCAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,wBAAmB;KAAnB,qBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kCAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gCAAmB;EAAnB;AAAmB;AAAnB;EAAA,gCAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0FAAmB;EAAnB,8GAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,8FAAmB;EAAnB;AAAmB;AAAnB;EAAA,oCAAmB;EAAnB,mDAAmB;EAAnB;AAAmB;AAAnB;EAAA,oCAAmB;EAAnB,mDAAmB;EAAnB;AAAmB;AAAnB;EAAA,oCAAmB;EAAnB,mDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB,+QAAmB;UAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;UAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AACnB,iCAAiC;;AAEjC;EACE,yBAAyB;EACzB,4CAA6C;AAC/C;AACA;EACE,+BAA+B;EAC/B,4CAAmD;AACrD;AACA;EACE,wBAAwB;EACxB,4CAA4C;AAC9C;AACA;EACE,8BAA8B;EAC9B,4CAAkD;AACpD;AACA;EACE,6BAA6B;EAC7B,4CAAiD;AACnD;AACA;EACE,mCAAmC;EACnC,4CAAuD;AACzD;AACA;EACE,8BAA8B;EAC9B,4CAAkD;AACpD;AACA;EACE,oCAAoC;EACpC,4CAAwD;AAC1D;AACA;EACE,0BAA0B;EAC1B,4CAA8C;AAChD;AACA;EACE,yBAAyB;EACzB,4CAA6C;AAC/C;AACA;EACE,+BAA+B;EAC/B,6CAAmD;AACrD;AACA;EACE,0BAA0B;EAC1B,6CAA8C;AAChD;AACA;EACE,gCAAgC;EAChC,6CAAoD;AACtD;AACA;EACE,2BAA2B;EAC3B,6CAA+C;AACjD;AACA;EACE,4BAA4B;EAC5B,6CAAgD;AAClD;AACA;EACE,kCAAkC;EAClC,6CAAsD;AACxD;AACA;EACE,0BAA0B;EAC1B,4CAA8C;AAChD;AACA;EACE,0BAA0B;EAC1B,4CAA8C;AAChD;;AAEA;;EAEE,YAAY;EACZ,yBAAyB;EACzB,6BAA6B;EAC7B,kBAAkB;EAClB,yBAAyB;EACzB,eAAe;AACjB;;AAEA;;EAEE,YAAY;EACZ,yBAAyB;EACzB,wCAAwC;EACxC,kBAAkB;EAClB,yBAAyB;EACzB,eAAe;AACjB;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;EACE,eAAe;EACf,cAAc;EACd,4BAA4B;AAC9B;;AAEA;EACE,YAAY;EACZ,mBAAmB;AACrB;;AAEA;EACE,eAAe;EACf,WAAW;EACX,4BAA4B;AAC9B;;AAEA;EACE,cAAc;EACd,6BAA6B;EAC7B,eAAe;AACjB;;AAEA;EACE,YAAY;EACZ,6BAA6B;EAC7B,eAAe;EACf,yBAAyB;AAC3B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;AACjB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,6BAA6B;EAC7B,eAAe;AACjB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,6BAA6B;EAC7B,eAAe;AACjB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,wBAAwB;AAC1B;AACA;EACE,wBAAwB;EACxB,YAAY;EACZ,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,yBAAyB;EACzB,6BAA6B;AAC/B;;AAEA;;;;EAIE,yBAAyB;EACzB,6BAA6B;AAC/B;;AAEA;EACE,yBAAyB;EACzB,6BAA6B;AAC/B;;AAEA;EACE,YAAY;EACZ,6BAA6B;AAC/B;AACA;;EAEE,6BAA6B;EAC7B,YAAY;AACd;AACA;;EAEE,6BAA6B;EAC7B,WAAW;AACb;AACA;EACE,6BAA6B;EAC7B,YAAY;AACd;;AAEA;EACE,aAAa;AACf;;;AAGA;EACE,6BAA6B;EAC7B,eAAe;AACjB;;AAEA;;EAEE,WAAW;EACX,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE,mBAAmB;EACnB,eAAe;EACf,kBAAkB;AACpB;;AAEA;;;EAGE,WAAW;EACX,yBAAyB;EACzB,8BAA8B;EAC9B,iBAAiB;EACjB,oBAAoB;EACpB,aAAa;EACb,sBAAsB;EACtB,uBAAuB;AACzB;;AAEA;;EAEE,WAAW;EACX,2BAA2B;EAC3B,iBAAiB;EACjB,oBAAoB;AACtB;;AAEA;EACE,cAAc;EACd,iBAAiB;EACjB,oBAAoB;EACpB,aAAa;EACb,sBAAsB;EACtB,uBAAuB;EACvB,qBAAqB;AACvB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,cAAc;EACd,eAAe;EACf,0BAA0B;EAC1B,iBAAiB;EACjB,wBAAwB;EACxB,SAAS;EACT,UAAU;EACV,gBAAgB;AAClB;;AAEA;EACE,eAAe;EACf,2BAA2B;AAC7B;;AAEA,kEAAkE;;AAElE;EACE,UAAU;AACZ;;AAEA;EACE,eAAe;EACf,sBAAsB;EACtB,WAAW;EACX,WAAW;EACX,+BAA+B;EAC/B,kBAAkB;EAClB,aAAa;EACb,uBAAuB;EACvB,mBAAmB;AACrB;;AAEA;EACE,WAAW;EACX,eAAe;EACf,kBAAkB;EAClB,WAAW;EACX,MAAM;EACN,kBAAkB;AACpB;;AAEA;EACE,eAAe;EACf,WAAW;EACX,gBAAgB;EAChB,aAAa;EACb,gBAAgB;AAClB;;AAEA;;EAEE,6BAA6B;AAC/B;;AAEA;EACE,uBAAuB;EACvB,aAAa;EACb,gBAAgB;EAChB,oCAAoC;AACtC;;AAEA;;;;;;;;GAQG;AACH;;GAEG;AACH;EACE,cAAc;AAChB;AACA;;;GAGG;AACH;EACE,uBAAuB;AACzB;AACA;EACE,sBAAsB;EACtB,YAAY;AACd;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,6BAA6B;KAC1B,0BAA0B;UACrB,qBAAqB;AAC/B;AACA;;EAEE,wBAAwB;AAC1B;;AAEA;;;;;;EAME,gBAAgB;EAChB,mBAAmB;EACnB,qBAAqB;EACrB,0BAA0B;EAC1B,cAAc;EACd,6BAA6B;AAC/B;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,0BAA0B;EAC1B,4BAA4B;AAC9B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,cAAc;AAChB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,cAAc;EACd,yBAAyB;AAC3B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,cAAc;EACd,yBAAyB;EACzB,sBAAsB;AACxB;;AAEA;EACE,yBAAyB;AAC3B;AACA;EACE,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;EACzB,YAAY;AACd;;AAEA;EACE,qBAAqB;AACvB;AACA;EACE,qBAAqB;AACvB;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;;AAEA;EACE,yBAAyB;AAC3B;AACA;EACE,yBAAyB;AAC3B;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;;AAEA;EACE,eAAe;EACf,6BAA6B;AAC/B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;IACI,eAAe;IACf,cAAc;IACd,4BAA4B;AAChC;;AAEA;EACE,8BAA8B;EAC9B,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,8BAA8B;EAC9B,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,eAAe;EACf,6BAA6B;AAC/B;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,uBAAuB;EACvB,8BAA8B;AAChC;;AAEA;EACE,YAAY;EACZ,kBAAkB;EAClB,uBAAuB;AACzB;;AAEA;EACE,+BAA+B;AACjC;;AAEA;EACE,kBAAkB;EAClB,YAAY;EACZ,UAAU;AACZ;;AAEA;EACE,yBAAyB;EACzB,kBAAkB;AACpB;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;IACI,4BAA4B;AAChC;;AAEA;IACI,4BAA4B;AAChC;;AAEA;IACI,4BAA4B;AAChC;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,mBAAmB;AACrB;;AAEA;EACE,aAAa;EACb,mBAAmB;AACrB;;AAEA;;;;GAIG;;AAEH;IACI,mBAAmB;IACnB,aAAa;AACjB;;AAEA;EACE;IACE,kBAAkB;IAClB,wBAAwB;IACxB,iBAAiB;IACjB,YAAY;EACd;EACA;IACE,kBAAkB;EACpB;EACA;;IAEE,gBAAgB;EAClB;EACA;;IAEE,kBAAkB;IAClB,WAAW;IACX,gBAAgB;EAClB;EACA;;KAEG;;EAEH;IACE,eAAe;EACjB;EACA;IACE,gBAAgB;IAChB,WAAW;IACX,WAAW;IACX,aAAa;IACb,6BAA6B;IAC7B,SAAS;IAET,gBAAgB;EAClB;EACA;;IAEE,oBAAoB;EACtB;EACA;IACE,WAAW;EACb;EACA;IACE,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,eAAe;IACf,QAAQ;IACR,aAAa;IACb,uBAAuB;EACzB;EACA;IACE,WAAW;EACb;;EAEA;IACE,UAAU;EACZ;AACF;;;AAGA;;AAEA;;AAEA;;;;;;;;;;GAUG;;AAEH;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,oCAAoC,EAAE,2BAA2B;EACjE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,aAAa,EAAE,wCAAwC;AACzD;AACA;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,wCAAwC,EAAE,2BAA2B;EACrE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,aAAa,EAAE,wCAAwC;AACzD;;AAEA;EACE,kBAAkB;EAClB,YAAY;AACd;;AAEA;EACE,eAAe;EACf,mBAAmB;AACrB;;AAEA;EACE,eAAe;AACjB;AACA,uBAAuB;;CAEtB;EACC,yBAAyB;EACzB,wBAAwB;EACxB,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA,uBAAuB;CACtB;EACC,yBAAyB;EACzB,wBAAwB;EACxB,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;CACC;EACC,yBAAyB;EACzB,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,8BAA8B;EAC9B,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,8BAA8B;EAC9B,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,8BAA8B;EAC9B,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;;AAEA;;GAEG,yBAAyB;GACzB,iBAAiB;GACjB,4BAA4B;AAC/B;AACA;;EAEE,wBAAwB;EACxB,SAAS;AACX;;;AAGA;EACE,YAAY;EACZ,kBAAkB;EAClB,wBAAwB,EAAE,eAAe;EACzC,YAAY;EACZ,UAAU;EACV,eAAe;EACf,sBAAsB;EAEtB,6BAA6B;AAC/B;AACA;;GAEG;AACH;EACE,uBAAuB;AACzB;AACA;;GAEG;AACH;EACE,WAAW;EACX,YAAY;AACd;AACA;EACE,YAAY;EACZ,sBAAsB;EACtB,gBAAgB;EAChB,SAAS;EACT,UAAU;EACV,uBAAuB;EACvB,eAAe;EACf,6BAA6B;AAC/B;AACA;EACE,eAAe;EACf,KAAK;EACL,MAAM;EACN,kBAAkB;EAClB,UAAU;AACZ;;AAEA;EACE,cAAc;EACd,mBAAmB;EACnB,SAAS;EACT,iBAAiB;AACnB;AACA;EACE,kBAAkB;EAClB,QAAQ;AACV;AACA;IACI,YAAY;AAChB;AACA;EACE,4BAA4B;EAC5B,6BAA6B;EAC7B,iCAAiC;AACnC;AACA;EACE,qBAAqB;AACvB;;AAEA;EACE,oCAAoC;EACpC,qBAAqB;AACvB;AACA;EACE,oCAAoC;EACpC,UAAU;EACV,gCAAgC;EAChC,+BAA+B;AACjC;AACA;EACE,oCAAoC;EACpC,UAAU;EACV,gCAAgC;EAChC,+BAA+B;EAC/B,8BAA8B;AAChC;AACA;EACE,oCAAoC;EACpC,UAAU;EACV,gCAAgC;EAChC,8BAA8B;AAChC;AACA;IACI,+BAA+B;IAC/B,gCAAgC;IAChC,mBAAmB;AACvB;;AAEA;EACE,yBAAyB;EACzB,UAAU;EACV,UAAU;EACV,eAAe;EACf,uBAAuB;AACzB;AACA;EACE,sBAAsB;EACtB,UAAU;EACV,gBAAgB;EAChB,UAAU;EACV,uBAAuB;EACvB,0BAA0B;AAC5B;AACA;EACE,yBAAyB;AAC3B;;AAEA;EACE,aAAa;GACZ,wBAAwB;AAC3B;;AAEA;EACE,cAAc;GACb,wBAAwB;AAC3B;;AAEA;EACE,yBAAyB;AAC3B;AACA;EACE,wBAAwB;EACxB;AACF;AACA;EACE,cAAc;EACd,gCAAgC;AAClC;AACA;EACE,4BAA4B;;AAE9B;;AAEA,kBAAkB;;AAElB;GACG,yBAAyB;GACzB,iBAAiB;AACpB;;AAEA;EACE,YAAY;EACZ,gBAAgB;EAChB,wBAAwB,EAAE,eAAe;EACzC,YAAY;EACZ,UAAU;EACV,eAAe;EACf,sBAAsB;EACtB,mBAAmB;EACnB,kBAAkB;EAClB,6BAA6B;AAC/B;;AAEA;;GAEG;AACH;EACE,kBAAkB;EAClB,eAAe;EACf,OAAO;AACT;AACA;EACE,eAAe;EACf,eAAe;EACf,OAAO;AACT;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,sBAAsB;EACtB,WAAW;EACX,UAAU;EACV,kBAAkB;EAClB,6BAA6B;AAC/B;AACA;EACE,YAAY;EACZ,sBAAsB;EACtB,sBAAsB;EACtB,gBAAgB;EAChB,SAAS;EACT,UAAU;EACV,uBAAuB;EACvB,eAAe;EACf,6BAA6B;AAC/B;AACA;EACE,eAAe;EACf,KAAK;EACL,MAAM;EACN,wBAAwB;EACxB,UAAU;AACZ;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,YAAY;EACZ,gBAAgB;EAChB,4BAA4B;AAC9B;AALA;EACE,yBAAyB;EACzB,YAAY;EACZ,gBAAgB;EAChB,4BAA4B;AAC9B;AACA;EACE,oBAAoB;EACpB,4BAA4B;EAC5B,qBAAqB;EACrB,gBAAgB;EAChB,uBAAuB;EACvB;;EAEA;IACE,yBAAyB;IACzB,cAAc;IACd,wBAAwB;IACxB,gBAAgB;EAClB;EACA;IACE,aAAa;EACf;EAz+BF;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,sBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,uBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,mBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,yBCAA;EDAA,yDCAA;EDAA;CCAA;EDAA;EAAA,iBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,uBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,+BCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,4GCAA;EDAA,0GCAA;EDAA;CCAA;EDAA;EAAA,4GCAA;EDAA,0GCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,uBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,uBCAA;EDAA;CCAA;EDAA;;EAAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,wBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;;EDAA;IAAA,gBCAA;IDAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,sBCAA;IDAA;GCAA;;EDAA;IAAA,uBCAA;IDAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,kBCAA;IDAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;CAAA\",\"sourcesContent\":[\"@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n/*@import \\\"bootstrap/bootstrap\\\";*/\\r\\n\\r\\n@font-face {\\r\\n  font-family: poppinsblack;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Black.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsblackitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-BlackItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbold;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Bold.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbolditalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-BoldItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabold;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraBold.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabolditalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraBoldItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralight;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraLight.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralightitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraLightItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Italic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslight;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Light.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslightitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-LightItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmedium;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Medium.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmediumitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-MediumItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsregular;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Regular.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibold;\\r\\n  src: url(\\\"../assets/fonts/Poppins-SemiBold.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibolditalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-SemiBoldItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Italic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Italic.ttf\\\");\\r\\n}\\r\\n\\r\\nbody,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  overflow-x: hidden;\\r\\n  background-color: #f3f8ff;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark body,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\" !important;\\r\\n  overflow-x: hidden;\\r\\n  background-color: #0e0e10;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.ag-header .ag-header-cell.header-with-border  {\\r\\n  border-bottom: 1px solid #ccc;\\r\\n}\\r\\n\\r\\n.ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\nbutton[disabled] {\\r\\n  opacity: 0.5;\\r\\n  cursor: not-allowed;\\r\\n}\\r\\n\\r\\n.dark .ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #fff;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell {\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark .ag-ltr .ag-cell {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-panel {\\r\\n  justify-content: between;\\r\\n}\\r\\n.dark .ag-paging-panel {\\r\\n  justify-content: between;\\r\\n  color: white;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.dark .ag-center-cols-viewport {\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.contentsectionbg {\\r\\n  background-color: white;\\r\\n}\\r\\n\\r\\n.dark .contentsectionbg {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark\\r\\n  .ag-header.ag-header-allow-overflow\\r\\n  .ag-header-row\\r\\n  .ag-root-wrapper.ag-layout-normal {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .ag-header-container {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .pagination-style {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.dark .ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n.ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: #000;\\r\\n}\\r\\n.dark .ag-paging-button.ag-disabled {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-body-horizontal-scroll-viewport {\\r\\n  display: none;\\r\\n} \\r\\n\\r\\n\\r\\n.ag-overlay-no-rows-center {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:hover,\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:focus {\\r\\n  color: #ccc;\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\nnav:hover .forAnimate {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n.mainmenu {\\r\\n  background: #002d73;\\r\\n  border: #002d73;\\r\\n  border-radius: 0px;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > .active > a,\\r\\n.mainmenu .navbar-nav > .active > a:hover,\\r\\n.mainmenu .navbar-nav > .active > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: #2a3344;\\r\\n  border-left: 2px solid #1ca9c0;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > li > a:hover,\\r\\n.mainmenu .navbar-nav > li > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: lightgray;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n}\\r\\n\\r\\n.navbar-default .navbar-nav > li > a {\\r\\n  color: #00b1a3;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n  text-decoration: none;\\r\\n}\\r\\n\\r\\n.navbar {\\r\\n  min-height: 45px;\\r\\n}\\r\\n\\r\\n.page-heading {\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n  margin-top: 0px !important;\\r\\n  margin-left: 25px;\\r\\n  /* position: absolute; */\\r\\n  top: 10px;\\r\\n  left: 90px;\\r\\n  font-weight: 600;\\r\\n}\\r\\n\\r\\n.page-heading h2 {\\r\\n  font-size: 16px;\\r\\n  margin-top: 15px !important;\\r\\n}\\r\\n\\r\\n/* ----------- Notification dropdown end ----------------------- */\\r\\n\\r\\nnav.sidebar .brand a {\\r\\n  padding: 0;\\r\\n}\\r\\n\\r\\n.brand {\\r\\n  font-size: 24px;\\r\\n  /* padding: 0px 5px; */\\r\\n  color: #fff;\\r\\n  width: 69px;\\r\\n  /* background-color: #002d73; */\\r\\n  text-align: center;\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.titlebar {\\r\\n  width: 100%;\\r\\n  position: fixed;\\r\\n  /* height: 55px; */\\r\\n  z-index: 98;\\r\\n  top: 0;\\r\\n  padding-left: 69px;\\r\\n}\\r\\n\\r\\n.accordion {\\r\\n  cursor: pointer;\\r\\n  width: 100%;\\r\\n  text-align: left;\\r\\n  outline: none;\\r\\n  transition: 0.4s;\\r\\n}\\r\\n\\r\\n.active,\\r\\n.accordion:hover {\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\n.panel {\\r\\n  background-color: white;\\r\\n  max-height: 0;\\r\\n  overflow: hidden;\\r\\n  transition: max-height 0.2s ease-out;\\r\\n}\\r\\n\\r\\n/* .reactSelectCustom .css-1fdsijx-ValueContainer , .reactSelectCustom .css-b62m3t-ValueContainer {\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n}\\r\\n\\r\\n.reactSelectCustom .css-1hb7zxy-IndicatorsContainer, .reactSelectCustom .css-1xc3v61-IndicatorsContainer{\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n} */\\r\\n/* .reactSelectCustom .css-1jgx7bw-control{\\r\\n  flex-wrap: nowrap !important;\\r\\n} */\\r\\ninput[type=date]:invalid::-ms-datetime-edit {\\r\\n  color: #808080;\\r\\n}\\r\\n/* label {\\r\\n  font-size: 14px;\\r\\n  color: #505050;\\r\\n} */\\r\\n.top-navbar {\\r\\n  background-color: white;\\r\\n}\\r\\n.dark .top-navbar {\\r\\n  background-color: #000;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.dark .pageName {\\r\\n  color: white;\\r\\n}\\r\\n\\r\\ninput[type=\\\"number\\\"] {\\r\\n  -webkit-appearance: textfield;\\r\\n     -moz-appearance: textfield;\\r\\n          appearance: textfield;\\r\\n}\\r\\ninput[type=number]::-webkit-inner-spin-button, \\r\\ninput[type=number]::-webkit-outer-spin-button { \\r\\n  -webkit-appearance: none;\\r\\n}\\r\\n\\r\\ninput[type=\\\"text\\\"],\\r\\ninput[type=\\\"tel\\\"],\\r\\ninput[type=\\\"date\\\"],\\r\\ninput[type=\\\"email\\\"],\\r\\ninput[type=\\\"number\\\"],\\r\\nselect {\\r\\n  padding-top: 4px;\\r\\n  padding-bottom: 4px;\\r\\n  border-color: #d6d6d6;\\r\\n  outline: 2px solid #ffffff;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\ninput:focus, select:focus , textarea:focus{\\r\\n  outline-color: #0066ff;\\r\\n}\\r\\n\\r\\n.button {\\r\\n  padding: 4px 12px 4px 12px;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #505050;\\r\\n}\\r\\n.dark .labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #AFAFAF;\\r\\n  background-color: #FFFFFF;\\r\\n}\\r\\n\\r\\n.dark .searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #c0c0c0;\\r\\n  background-color: #1d212d;\\r\\n  outline-color: #1d212d;\\r\\n}\\r\\n\\r\\n.checkboxbg {\\r\\n  background-color: #D9D9D9;\\r\\n}\\r\\n.dark .checkboxbg {\\r\\n  background-color: #4d4d4d;\\r\\n}\\r\\n\\r\\n.dark select {\\r\\n  background-color: #1d1d1d;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.borderc-theme-blue2 {\\r\\n  border-color: #0066ff;\\r\\n}\\r\\n.dark .borderc-theme-blue2 {\\r\\n  border-color: #6699ff;\\r\\n}\\r\\n.textc-theme-blue2 {\\r\\n  color: #0066ff;\\r\\n}\\r\\n.dark .textc-theme-blue2 {\\r\\n  color: #6699ff;\\r\\n}\\r\\n\\r\\n.bgc-theme-blue2 {\\r\\n  background-color: #0066ff;\\r\\n}\\r\\n.dark .bgc-theme-blue2 {\\r\\n  background-color: #6699ff;\\r\\n}\\r\\n.textc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .textc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n.borderc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .borderc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n\\r\\n.input {\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .inputs {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.confirmInputs {\\r\\n    font-size: 12px;\\r\\n    color: #333333;\\r\\n    font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.formtitle {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.subtitles {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.text-blackcolor {\\r\\n  color:#505050;\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within {\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-row-odd{\\r\\n  background-color: #f3f3f3 !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value span {\\r\\n  height: auto !important;\\r\\n  line-height: normal !important;\\r\\n}\\r\\n\\r\\n.ag-cell{\\r\\n  display:flex;\\r\\n  align-items:center;\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value, .ag-header-cell-text{\\r\\n  text-overflow: unset !important;\\r\\n}\\r\\n\\r\\n.pagination-style {\\r\\n  position: absolute;\\r\\n  bottom: 10px;\\r\\n  left: 20px;\\r\\n}\\r\\n\\r\\n.pagination-style select {\\r\\n  background-color: #f2f2f2;\\r\\n  border-radius: 3px;\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-header-select-all{\\r\\n  margin-right: 12px !important; \\r\\n}\\r\\n\\r\\n.ag-root-wrapper.ag-layout-normal{\\r\\n  border-radius: 5px !important;\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-row.ag-header-row-column {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-center-cols-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.general_section .ag-header-container, .general_section .ag-center-cols-container, .general_section .ag-header-row  {\\r\\n  width: 100% !important;\\r\\n}\\r\\n\\r\\n.general_section .ag-cell {\\r\\n  /* display: flex; */\\r\\n  margin-right: 10px;\\r\\n}\\r\\n\\r\\n.product_link_def .ag-row .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.product_data_def .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n/* @media (min-width: 640px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 10px;\\r\\n  }\\r\\n} */\\r\\n\\r\\n.viewlog .ag-cell-value {\\r\\n    align-items: center;\\r\\n    display: flex;\\r\\n}\\r\\n\\r\\n@media (min-width: 765px) {\\r\\n  .main {\\r\\n    position: absolute;\\r\\n    width: calc(100% - 40px);\\r\\n    margin-left: 40px;\\r\\n    float: right;\\r\\n  }\\r\\n  nav.sidebar:hover + .main {\\r\\n    margin-left: 200px;\\r\\n  }\\r\\n  nav.sidebar.navbar.sidebar > .container .navbar-brand,\\r\\n  .navbar > .container-fluid .navbar-brand {\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  nav.sidebar .navbar-brand,\\r\\n  nav.sidebar .navbar-header {\\r\\n    text-align: center;\\r\\n    width: 100%;\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  /* nav.sidebar a {\\r\\n    padding-bottom: 34px;\\r\\n  } */\\r\\n\\r\\n  nav.sidebar .navbar-nav > li {\\r\\n    font-size: 13px;\\r\\n  }\\r\\n  nav.sidebar .navbar-nav .open .dropdown-menu {\\r\\n    position: static;\\r\\n    float: none;\\r\\n    width: auto;\\r\\n    margin-top: 0;\\r\\n    background-color: transparent;\\r\\n    border: 0;\\r\\n    -webkit-box-shadow: none;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  nav.sidebar .navbar-collapse,\\r\\n  nav.sidebar .container-fluid {\\r\\n    padding: 0 0px 0 0px;\\r\\n  }\\r\\n  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {\\r\\n    color: #777;\\r\\n  }\\r\\n  nav.sidebar {\\r\\n    width: 69px;\\r\\n    height: 100%;\\r\\n    margin-bottom: 0px;\\r\\n    position: fixed;\\r\\n    top: 0px;\\r\\n    display: flex;\\r\\n    align-items: flex-start;\\r\\n  }\\r\\n  nav.sidebar li {\\r\\n    width: 100%;\\r\\n  }\\r\\n\\r\\n  .forAnimate {\\r\\n    opacity: 0;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n@media (min-width: 1024px) {\\r\\n  \\r\\n}\\r\\n\\r\\n/* @media (min-width: 1280px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 14px;\\r\\n  }}\\r\\n\\r\\n@media (min-width: 1536px) {\\r\\n  \\r\\n}\\r\\n@media (min-width: 1940px) {\\r\\n  \\r\\n} */\\r\\n\\r\\n.desktop-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n.block-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(214, 21, 21, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n\\r\\n.message-content {\\r\\n  text-align: center;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.message-content h2 {\\r\\n  font-size: 24px;\\r\\n  margin-bottom: 10px;\\r\\n}\\r\\n\\r\\n.message-content p {\\r\\n  font-size: 16px;\\r\\n}\\r\\n/* quarter filter css */\\r\\n\\r\\n #q1:checked  + .labelcheck, #q2:checked  + .labelcheck, #q3:checked  + .labelcheck, #q4:checked  + .labelcheck, #all:checked  + .labelcheck, #needsupdate:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* section filter css */\\r\\n #volume:checked  + .labelcheck, #breakeven:checked  + .labelcheck, #unitprice:checked  + .labelcheck, #grossprofit:checked  + .labelcheck, #gppercent:checked  + .labelcheck, #value:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n #bestatus-1:checked  + .labelcheck {\\r\\n  background-color: #FF9A03;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-2:checked  + .labelcheck{\\r\\n  background-color: #33CA7F;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#444;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-3:checked  + .labelcheck{\\r\\n  background-color: #FFDF37;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-4:checked  + .labelcheck{\\r\\n  background-color: #FB4646;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.planningtoolgrid\\r\\n{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n   /* border: 1px solid #ddd; */\\r\\n}\\r\\n.input-number::-webkit-inner-spin-button,\\r\\n.input-number::-webkit-outer-spin-button {\\r\\n  -webkit-appearance: none;\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n\\r\\n.planningtoolgrid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: center;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* .planningtoolgrid thead tr, .planningtoolgrid tbody tr{\\r\\n  border: 1px solid #ddd;\\r\\n} */\\r\\n.planningtoolgrid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n}\\r\\n/* .planningtoolgrid tbody tr{\\r\\n height:70px !important;\\r\\n} */\\r\\n.planningtoolgrid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n}\\r\\n.planningtoolgrid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.planningtoolgrid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  background:#f3f8ff;\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.quartertotals{\\r\\n  /* border:0; */\\r\\n  table-layout: fixed;\\r\\n  width:50%;\\r\\n  text-align:center;\\r\\n}\\r\\n.quartertotals thead th, .quartertotals tbody td{\\r\\n  text-align: center;\\r\\n  border:0;\\r\\n}\\r\\n.quartertotals thead tr, .quartertotals tbody tr {\\r\\n    border: none;\\r\\n}\\r\\n.quartertotals thead th{\\r\\n  border-top-left-radius: 20px;\\r\\n  border-top-right-radius: 20px;\\r\\n  background-color: #fff !important;\\r\\n}\\r\\n.quartertotals thead{\\r\\n  z-index: 5 !important;\\r\\n}\\r\\n\\r\\n.quartertotals thead th:first-child, .quartertotals tbody td:first-child{\\r\\n  background-color: #f3f8ff !important;\\r\\n  width:50px !important;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(2){\\r\\n  background-color: #201E50 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #504F66;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(3){\\r\\n  background-color: #D499B9 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #C0749E;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(4){\\r\\n  background-color: #EE6C4D !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #CE4B2C;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody tr:last-child td{\\r\\n    border-bottom-left-radius: 15px;\\r\\n    border-bottom-right-radius: 15px;\\r\\n    border-bottom: none;\\r\\n}\\r\\n\\r\\n.titlerow{\\r\\n  background-color: #00E0D5;\\r\\n  color:#fff;\\r\\n  z-index: 9;\\r\\n  font-size: 15px;\\r\\n  height: 28px !important;\\r\\n}\\r\\ntd.sectionrow,th.sectionrow{\\r\\n  background-color: #DDD;\\r\\n  color:#444;\\r\\n  font-weight: 600;\\r\\n  z-index: 9;\\r\\n  height: 28px !important;\\r\\n  font-size: 13px !important;\\r\\n}\\r\\ninput[type=\\\"text\\\"]:disabled, input[type=\\\"number\\\"]:disabled, select:disabled{\\r\\n  background-color: #F6F3F3;\\r\\n}\\r\\n\\r\\n#wrapper.closed .list {\\r\\n  display: none;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n#wrapper.open .list {\\r\\n  display: block;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n.bg-currentWeek{\\r\\n  background-color: #fcf6b1;\\r\\n}\\r\\n.nodata{\\r\\n  background-color:#ffecd4;\\r\\n  color:#b31818\\r\\n}\\r\\n.selected{\\r\\n  color: #0066FF;\\r\\n  border-bottom: 3px solid #0066FF;\\r\\n}\\r\\n.buttonText{\\r\\n  font-family: 'poppinsmedium';\\r\\n  \\r\\n}\\r\\n\\r\\n/*--- SLP CSS ---*/\\r\\n\\r\\n.service-level-grid{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n}\\r\\n\\r\\n.service-level-grid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: left;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  white-space: normal;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n/* .service-level-grid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n} */\\r\\n.service-level-grid thead tr th:last-child{\\r\\n  background:#f3f8ff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n.service-level-grid tbody tr td:last-child{\\r\\n  background:#fff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n\\r\\n.service-level-grid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n  border: 1px solid #ddd;\\r\\n  padding:5px;\\r\\n  z-index: 5;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  background-color:white;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  /* background:#f3f8ff; */\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.ag-grid-checkbox-cell .ag-checkbox-input {\\r\\n  cursor: pointer;\\r\\n}\\r\\n\\r\\n.depotdaterange .rdrDateDisplayWrapper{\\r\\n  display:none;\\r\\n}\\r\\n\\r\\nlabel{\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\nselect::placeholder, .css-1jqq78o-placeholder, .placeholdertext {\\r\\n  color: #333333 !important;\\r\\n  opacity: 0.5;\\r\\n  font-weight: 500;\\r\\n  font-family:\\\"poppinsregular\\\";\\r\\n}\\r\\n.text-truncate2L {\\r\\n  display: -webkit-box;\\r\\n  -webkit-box-orient: vertical;\\r\\n  -webkit-line-clamp: 2;\\r\\n  overflow: hidden;\\r\\n  text-overflow: ellipsis;\\r\\n  }\\r\\n\\r\\n  .variety-disabled-block{\\r\\n    background-color: #f6f6f6;\\r\\n    color: #888888;\\r\\n    border:1px solid #E2E2E2;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  .variety-disabled-block h4, .variety-disabled-block label{\\r\\n    color:#797979;\\r\\n  }\",null],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[2]!./styles/globals.css\n"));

/***/ })

});
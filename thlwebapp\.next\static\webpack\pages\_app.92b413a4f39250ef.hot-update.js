"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/getUrl.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/getUrl.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _assets_fonts_Poppins_Black_ttf__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../assets/fonts/Poppins-Black.ttf */ \"./assets/fonts/Poppins-Black.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_BlackItalic_ttf__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../assets/fonts/Poppins-BlackItalic.ttf */ \"./assets/fonts/Poppins-BlackItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Bold_ttf__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../assets/fonts/Poppins-Bold.ttf */ \"./assets/fonts/Poppins-Bold.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_BoldItalic_ttf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../assets/fonts/Poppins-BoldItalic.ttf */ \"./assets/fonts/Poppins-BoldItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraBold_ttf__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraBold.ttf */ \"./assets/fonts/Poppins-ExtraBold.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraBoldItalic.ttf */ \"./assets/fonts/Poppins-ExtraBoldItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraLight_ttf__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraLight.ttf */ \"./assets/fonts/Poppins-ExtraLight.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraLightItalic_ttf__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraLightItalic.ttf */ \"./assets/fonts/Poppins-ExtraLightItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Italic_ttf__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../assets/fonts/Poppins-Italic.ttf */ \"./assets/fonts/Poppins-Italic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Light_ttf__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../assets/fonts/Poppins-Light.ttf */ \"./assets/fonts/Poppins-Light.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_LightItalic_ttf__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../assets/fonts/Poppins-LightItalic.ttf */ \"./assets/fonts/Poppins-LightItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Medium_ttf__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../assets/fonts/Poppins-Medium.ttf */ \"./assets/fonts/Poppins-Medium.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_MediumItalic_ttf__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../assets/fonts/Poppins-MediumItalic.ttf */ \"./assets/fonts/Poppins-MediumItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Regular_ttf__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../assets/fonts/Poppins-Regular.ttf */ \"./assets/fonts/Poppins-Regular.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_SemiBold_ttf__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../assets/fonts/Poppins-SemiBold.ttf */ \"./assets/fonts/Poppins-SemiBold.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_SemiBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../assets/fonts/Poppins-SemiBoldItalic.ttf */ \"./assets/fonts/Poppins-SemiBoldItalic.ttf\");\n// Imports\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Black_ttf__WEBPACK_IMPORTED_MODULE_2__);\nvar ___CSS_LOADER_URL_REPLACEMENT_1___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_BlackItalic_ttf__WEBPACK_IMPORTED_MODULE_3__);\nvar ___CSS_LOADER_URL_REPLACEMENT_2___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Bold_ttf__WEBPACK_IMPORTED_MODULE_4__);\nvar ___CSS_LOADER_URL_REPLACEMENT_3___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_BoldItalic_ttf__WEBPACK_IMPORTED_MODULE_5__);\nvar ___CSS_LOADER_URL_REPLACEMENT_4___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraBold_ttf__WEBPACK_IMPORTED_MODULE_6__);\nvar ___CSS_LOADER_URL_REPLACEMENT_5___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_7__);\nvar ___CSS_LOADER_URL_REPLACEMENT_6___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraLight_ttf__WEBPACK_IMPORTED_MODULE_8__);\nvar ___CSS_LOADER_URL_REPLACEMENT_7___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraLightItalic_ttf__WEBPACK_IMPORTED_MODULE_9__);\nvar ___CSS_LOADER_URL_REPLACEMENT_8___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Italic_ttf__WEBPACK_IMPORTED_MODULE_10__);\nvar ___CSS_LOADER_URL_REPLACEMENT_9___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Light_ttf__WEBPACK_IMPORTED_MODULE_11__);\nvar ___CSS_LOADER_URL_REPLACEMENT_10___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_LightItalic_ttf__WEBPACK_IMPORTED_MODULE_12__);\nvar ___CSS_LOADER_URL_REPLACEMENT_11___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Medium_ttf__WEBPACK_IMPORTED_MODULE_13__);\nvar ___CSS_LOADER_URL_REPLACEMENT_12___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_MediumItalic_ttf__WEBPACK_IMPORTED_MODULE_14__);\nvar ___CSS_LOADER_URL_REPLACEMENT_13___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Regular_ttf__WEBPACK_IMPORTED_MODULE_15__);\nvar ___CSS_LOADER_URL_REPLACEMENT_14___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_SemiBold_ttf__WEBPACK_IMPORTED_MODULE_16__);\nvar ___CSS_LOADER_URL_REPLACEMENT_15___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_SemiBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_17__);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/*\\n! tailwindcss v3.3.3 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n*/\\n\\nhtml {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, \\\"Helvetica Neue\\\", Arial, \\\"Noto Sans\\\", sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font family by default.\\n2. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-size: 1em; /* 2 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\n[type='button'],\\n[type='reset'],\\n[type='submit'] {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden] {\\n  display: none;\\n}\\n\\n*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n}\\r\\n.container {\\n  width: 100%;\\n}\\r\\n@media (min-width: 640px) {\\n\\n  .container {\\n    max-width: 640px;\\n  }\\n}\\r\\n@media (min-width: 765px) {\\n\\n  .container {\\n    max-width: 765px;\\n  }\\n}\\r\\n@media (min-width: 1024px) {\\n\\n  .container {\\n    max-width: 1024px;\\n  }\\n}\\r\\n@media (min-width: 1280px) {\\n\\n  .container {\\n    max-width: 1280px;\\n  }\\n}\\r\\n@media (min-width: 1536px) {\\n\\n  .container {\\n    max-width: 1536px;\\n  }\\n}\\r\\n@media (min-width: 1920px) {\\n\\n  .container {\\n    max-width: 1920px;\\n  }\\n}\\r\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\r\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\r\\n.visible {\\n  visibility: visible;\\n}\\r\\n.fixed {\\n  position: fixed;\\n}\\r\\n.\\\\!absolute {\\n  position: absolute !important;\\n}\\r\\n.absolute {\\n  position: absolute;\\n}\\r\\n.relative {\\n  position: relative;\\n}\\r\\n.sticky {\\n  position: sticky;\\n}\\r\\n.inset-0 {\\n  inset: 0px;\\n}\\r\\n.\\\\!right-1 {\\n  right: 0.25rem !important;\\n}\\r\\n.\\\\!right-2 {\\n  right: 0.5rem !important;\\n}\\r\\n.\\\\!top-1 {\\n  top: 0.25rem !important;\\n}\\r\\n.-top-0 {\\n  top: -0px;\\n}\\r\\n.-top-2 {\\n  top: -0.5rem;\\n}\\r\\n.-top-3 {\\n  top: -0.75rem;\\n}\\r\\n.-top-\\\\[0\\\\.9\\\\] {\\n  top: -0.9;\\n}\\r\\n.-top-\\\\[2px\\\\] {\\n  top: -2px;\\n}\\r\\n.bottom-0 {\\n  bottom: 0px;\\n}\\r\\n.bottom-\\\\[-20px\\\\] {\\n  bottom: -20px;\\n}\\r\\n.left-0 {\\n  left: 0px;\\n}\\r\\n.left-1\\\\/2 {\\n  left: 50%;\\n}\\r\\n.left-5 {\\n  left: 1.25rem;\\n}\\r\\n.right-0 {\\n  right: 0px;\\n}\\r\\n.right-1 {\\n  right: 0.25rem;\\n}\\r\\n.right-10 {\\n  right: 2.5rem;\\n}\\r\\n.right-2 {\\n  right: 0.5rem;\\n}\\r\\n.right-5 {\\n  right: 1.25rem;\\n}\\r\\n.top-0 {\\n  top: 0px;\\n}\\r\\n.top-1 {\\n  top: 0.25rem;\\n}\\r\\n.top-1\\\\/2 {\\n  top: 50%;\\n}\\r\\n.top-\\\\[52px\\\\] {\\n  top: 52px;\\n}\\r\\n.top-full {\\n  top: 100%;\\n}\\r\\n.\\\\!z-\\\\[10\\\\] {\\n  z-index: 10 !important;\\n}\\r\\n.\\\\!z-\\\\[9999999\\\\] {\\n  z-index: 9999999 !important;\\n}\\r\\n.\\\\!z-\\\\[9\\\\] {\\n  z-index: 9 !important;\\n}\\r\\n.z-10 {\\n  z-index: 10;\\n}\\r\\n.z-20 {\\n  z-index: 20;\\n}\\r\\n.z-50 {\\n  z-index: 50;\\n}\\r\\n.z-\\\\[5\\\\] {\\n  z-index: 5;\\n}\\r\\n.col-span-2 {\\n  grid-column: span 2 / span 2;\\n}\\r\\n.col-start-6 {\\n  grid-column-start: 6;\\n}\\r\\n.m-1 {\\n  margin: 0.25rem;\\n}\\r\\n.m-3 {\\n  margin: 0.75rem;\\n}\\r\\n.m-4 {\\n  margin: 1rem;\\n}\\r\\n.m-5 {\\n  margin: 1.25rem;\\n}\\r\\n.\\\\!my-3 {\\n  margin-top: 0.75rem !important;\\n  margin-bottom: 0.75rem !important;\\n}\\r\\n.mx-4 {\\n  margin-left: 1rem;\\n  margin-right: 1rem;\\n}\\r\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\r\\n.my-1 {\\n  margin-top: 0.25rem;\\n  margin-bottom: 0.25rem;\\n}\\r\\n.my-2 {\\n  margin-top: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\r\\n.my-32 {\\n  margin-top: 8rem;\\n  margin-bottom: 8rem;\\n}\\r\\n.my-4 {\\n  margin-top: 1rem;\\n  margin-bottom: 1rem;\\n}\\r\\n.my-5 {\\n  margin-top: 1.25rem;\\n  margin-bottom: 1.25rem;\\n}\\r\\n.my-\\\\[24px\\\\] {\\n  margin-top: 24px;\\n  margin-bottom: 24px;\\n}\\r\\n.\\\\!mt-2 {\\n  margin-top: 0.5rem !important;\\n}\\r\\n.\\\\!mt-3 {\\n  margin-top: 0.75rem !important;\\n}\\r\\n.-mt-1 {\\n  margin-top: -0.25rem;\\n}\\r\\n.-mt-\\\\[1px\\\\] {\\n  margin-top: -1px;\\n}\\r\\n.-mt-\\\\[2px\\\\] {\\n  margin-top: -2px;\\n}\\r\\n.mb-0 {\\n  margin-bottom: 0px;\\n}\\r\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\r\\n.mb-10 {\\n  margin-bottom: 2.5rem;\\n}\\r\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\r\\n.mb-20 {\\n  margin-bottom: 5rem;\\n}\\r\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\r\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\r\\n.mb-5 {\\n  margin-bottom: 1.25rem;\\n}\\r\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\r\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\r\\n.mb-\\\\[18px\\\\] {\\n  margin-bottom: 18px;\\n}\\r\\n.mb-\\\\[2px\\\\] {\\n  margin-bottom: 2px;\\n}\\r\\n.me-10 {\\n  margin-inline-end: 2.5rem;\\n}\\r\\n.me-4 {\\n  margin-inline-end: 1rem;\\n}\\r\\n.me-5 {\\n  margin-inline-end: 1.25rem;\\n}\\r\\n.ml-0 {\\n  margin-left: 0px;\\n}\\r\\n.ml-1 {\\n  margin-left: 0.25rem;\\n}\\r\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\r\\n.ml-3 {\\n  margin-left: 0.75rem;\\n}\\r\\n.ml-4 {\\n  margin-left: 1rem;\\n}\\r\\n.ml-5 {\\n  margin-left: 1.25rem;\\n}\\r\\n.ml-6 {\\n  margin-left: 1.5rem;\\n}\\r\\n.ml-8 {\\n  margin-left: 2rem;\\n}\\r\\n.ml-\\\\[18\\\\%\\\\] {\\n  margin-left: 18%;\\n}\\r\\n.ml-\\\\[28px\\\\] {\\n  margin-left: 28px;\\n}\\r\\n.ml-\\\\[2px\\\\] {\\n  margin-left: 2px;\\n}\\r\\n.ml-\\\\[46px\\\\] {\\n  margin-left: 46px;\\n}\\r\\n.ml-auto {\\n  margin-left: auto;\\n}\\r\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\r\\n.mr-12 {\\n  margin-right: 3rem;\\n}\\r\\n.mr-14 {\\n  margin-right: 3.5rem;\\n}\\r\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\r\\n.mr-20 {\\n  margin-right: 5rem;\\n}\\r\\n.mr-3 {\\n  margin-right: 0.75rem;\\n}\\r\\n.mr-4 {\\n  margin-right: 1rem;\\n}\\r\\n.mr-\\\\[23px\\\\] {\\n  margin-right: 23px;\\n}\\r\\n.mr-\\\\[25px\\\\] {\\n  margin-right: 25px;\\n}\\r\\n.mr-\\\\[55px\\\\] {\\n  margin-right: 55px;\\n}\\r\\n.mr-\\\\[5px\\\\] {\\n  margin-right: 5px;\\n}\\r\\n.mt-0 {\\n  margin-top: 0px;\\n}\\r\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\r\\n.mt-14 {\\n  margin-top: 3.5rem;\\n}\\r\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\r\\n.mt-3 {\\n  margin-top: 0.75rem;\\n}\\r\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\r\\n.mt-5 {\\n  margin-top: 1.25rem;\\n}\\r\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\r\\n.mt-7 {\\n  margin-top: 1.75rem;\\n}\\r\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\r\\n.mt-9 {\\n  margin-top: 2.25rem;\\n}\\r\\n.mt-\\\\[17px\\\\] {\\n  margin-top: 17px;\\n}\\r\\n.mt-\\\\[2px\\\\] {\\n  margin-top: 2px;\\n}\\r\\n.mt-\\\\[45px\\\\] {\\n  margin-top: 45px;\\n}\\r\\n.mt-\\\\[60px\\\\] {\\n  margin-top: 60px;\\n}\\r\\n.block {\\n  display: block;\\n}\\r\\n.inline-block {\\n  display: inline-block;\\n}\\r\\n.inline {\\n  display: inline;\\n}\\r\\n.\\\\!flex {\\n  display: flex !important;\\n}\\r\\n.flex {\\n  display: flex;\\n}\\r\\n.inline-flex {\\n  display: inline-flex;\\n}\\r\\n.table {\\n  display: table;\\n}\\r\\n.grid {\\n  display: grid;\\n}\\r\\n.hidden {\\n  display: none;\\n}\\r\\n.\\\\!h-0 {\\n  height: 0px !important;\\n}\\r\\n.\\\\!h-16 {\\n  height: 4rem !important;\\n}\\r\\n.h-10 {\\n  height: 2.5rem;\\n}\\r\\n.h-14 {\\n  height: 3.5rem;\\n}\\r\\n.h-24 {\\n  height: 6rem;\\n}\\r\\n.h-4 {\\n  height: 1rem;\\n}\\r\\n.h-5 {\\n  height: 1.25rem;\\n}\\r\\n.h-52 {\\n  height: 13rem;\\n}\\r\\n.h-6 {\\n  height: 1.5rem;\\n}\\r\\n.h-8 {\\n  height: 2rem;\\n}\\r\\n.h-9 {\\n  height: 2.25rem;\\n}\\r\\n.h-\\\\[100vh\\\\] {\\n  height: 100vh;\\n}\\r\\n.h-\\\\[20px\\\\] {\\n  height: 20px;\\n}\\r\\n.h-\\\\[23px\\\\] {\\n  height: 23px;\\n}\\r\\n.h-\\\\[25px\\\\] {\\n  height: 25px;\\n}\\r\\n.h-\\\\[28px\\\\] {\\n  height: 28px;\\n}\\r\\n.h-\\\\[30px\\\\] {\\n  height: 30px;\\n}\\r\\n.h-\\\\[31px\\\\] {\\n  height: 31px;\\n}\\r\\n.h-\\\\[36px\\\\] {\\n  height: 36px;\\n}\\r\\n.h-\\\\[38px\\\\] {\\n  height: 38px;\\n}\\r\\n.h-\\\\[40px\\\\] {\\n  height: 40px;\\n}\\r\\n.h-\\\\[85vh\\\\] {\\n  height: 85vh;\\n}\\r\\n.h-\\\\[calc\\\\(100vh-100px\\\\)\\\\] {\\n  height: calc(100vh - 100px);\\n}\\r\\n.h-full {\\n  height: 100%;\\n}\\r\\n.h-12 {\\n  height: 3rem;\\n}\\r\\n.h-2 {\\n  height: 0.5rem;\\n}\\r\\n.\\\\!max-h-\\\\[28rem\\\\] {\\n  max-height: 28rem !important;\\n}\\r\\n.\\\\!max-h-\\\\[70vh\\\\] {\\n  max-height: 70vh !important;\\n}\\r\\n.\\\\!max-h-full {\\n  max-height: 100% !important;\\n}\\r\\n.max-h-\\\\[500px\\\\] {\\n  max-height: 500px;\\n}\\r\\n.min-h-full {\\n  min-height: 100%;\\n}\\r\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\r\\n.\\\\!w-10 {\\n  width: 2.5rem !important;\\n}\\r\\n.\\\\!w-2\\\\/5 {\\n  width: 40% !important;\\n}\\r\\n.\\\\!w-28 {\\n  width: 7rem !important;\\n}\\r\\n.\\\\!w-32 {\\n  width: 8rem !important;\\n}\\r\\n.\\\\!w-40 {\\n  width: 10rem !important;\\n}\\r\\n.\\\\!w-44 {\\n  width: 11rem !important;\\n}\\r\\n.\\\\!w-48 {\\n  width: 12rem !important;\\n}\\r\\n.\\\\!w-52 {\\n  width: 13rem !important;\\n}\\r\\n.\\\\!w-60 {\\n  width: 15rem !important;\\n}\\r\\n.\\\\!w-72 {\\n  width: 18rem !important;\\n}\\r\\n.\\\\!w-80 {\\n  width: 20rem !important;\\n}\\r\\n.\\\\!w-\\\\[20\\\\%\\\\] {\\n  width: 20% !important;\\n}\\r\\n.\\\\!w-\\\\[450px\\\\] {\\n  width: 450px !important;\\n}\\r\\n.\\\\!w-auto {\\n  width: auto !important;\\n}\\r\\n.\\\\!w-full {\\n  width: 100% !important;\\n}\\r\\n.w-1\\\\/2 {\\n  width: 50%;\\n}\\r\\n.w-1\\\\/3 {\\n  width: 33.333333%;\\n}\\r\\n.w-1\\\\/4 {\\n  width: 25%;\\n}\\r\\n.w-1\\\\/5 {\\n  width: 20%;\\n}\\r\\n.w-1\\\\/6 {\\n  width: 16.666667%;\\n}\\r\\n.w-11 {\\n  width: 2.75rem;\\n}\\r\\n.w-2\\\\/3 {\\n  width: 66.666667%;\\n}\\r\\n.w-2\\\\/5 {\\n  width: 40%;\\n}\\r\\n.w-2\\\\/6 {\\n  width: 33.333333%;\\n}\\r\\n.w-28 {\\n  width: 7rem;\\n}\\r\\n.w-3\\\\/4 {\\n  width: 75%;\\n}\\r\\n.w-4 {\\n  width: 1rem;\\n}\\r\\n.w-5 {\\n  width: 1.25rem;\\n}\\r\\n.w-6 {\\n  width: 1.5rem;\\n}\\r\\n.w-8 {\\n  width: 2rem;\\n}\\r\\n.w-80 {\\n  width: 20rem;\\n}\\r\\n.w-96 {\\n  width: 24rem;\\n}\\r\\n.w-\\\\[10\\\\%\\\\] {\\n  width: 10%;\\n}\\r\\n.w-\\\\[100\\\\%-70px\\\\] {\\n  width: 100%-70px;\\n}\\r\\n.w-\\\\[100px\\\\] {\\n  width: 100px;\\n}\\r\\n.w-\\\\[115px\\\\] {\\n  width: 115px;\\n}\\r\\n.w-\\\\[130px\\\\] {\\n  width: 130px;\\n}\\r\\n.w-\\\\[15\\\\%\\\\] {\\n  width: 15%;\\n}\\r\\n.w-\\\\[150px\\\\] {\\n  width: 150px;\\n}\\r\\n.w-\\\\[160px\\\\] {\\n  width: 160px;\\n}\\r\\n.w-\\\\[20\\\\%\\\\] {\\n  width: 20%;\\n}\\r\\n.w-\\\\[25\\\\%\\\\] {\\n  width: 25%;\\n}\\r\\n.w-\\\\[25px\\\\] {\\n  width: 25px;\\n}\\r\\n.w-\\\\[30\\\\%\\\\] {\\n  width: 30%;\\n}\\r\\n.w-\\\\[30px\\\\] {\\n  width: 30px;\\n}\\r\\n.w-\\\\[40\\\\%\\\\] {\\n  width: 40%;\\n}\\r\\n.w-\\\\[44\\\\%\\\\] {\\n  width: 44%;\\n}\\r\\n.w-\\\\[45\\\\%\\\\] {\\n  width: 45%;\\n}\\r\\n.w-\\\\[47vh\\\\] {\\n  width: 47vh;\\n}\\r\\n.w-\\\\[48\\\\%\\\\] {\\n  width: 48%;\\n}\\r\\n.w-\\\\[5\\\\%\\\\] {\\n  width: 5%;\\n}\\r\\n.w-\\\\[50\\\\%\\\\] {\\n  width: 50%;\\n}\\r\\n.w-\\\\[500px\\\\] {\\n  width: 500px;\\n}\\r\\n.w-\\\\[60\\\\%\\\\] {\\n  width: 60%;\\n}\\r\\n.w-\\\\[75\\\\%\\\\] {\\n  width: 75%;\\n}\\r\\n.w-\\\\[8\\\\%\\\\] {\\n  width: 8%;\\n}\\r\\n.w-\\\\[93\\\\%\\\\] {\\n  width: 93%;\\n}\\r\\n.w-\\\\[94\\\\%\\\\] {\\n  width: 94%;\\n}\\r\\n.w-\\\\[95\\\\%\\\\] {\\n  width: 95%;\\n}\\r\\n.w-\\\\[96\\\\%\\\\] {\\n  width: 96%;\\n}\\r\\n.w-\\\\[calc\\\\(100\\\\%-20px\\\\)\\\\] {\\n  width: calc(100% - 20px);\\n}\\r\\n.w-auto {\\n  width: auto;\\n}\\r\\n.w-full {\\n  width: 100%;\\n}\\r\\n.w-12 {\\n  width: 3rem;\\n}\\r\\n.w-32 {\\n  width: 8rem;\\n}\\r\\n.w-\\\\[15vh\\\\] {\\n  width: 15vh;\\n}\\r\\n.\\\\!min-w-0 {\\n  min-width: 0px !important;\\n}\\r\\n.\\\\!min-w-fit {\\n  min-width: -moz-fit-content !important;\\n  min-width: fit-content !important;\\n}\\r\\n.min-w-\\\\[200px\\\\] {\\n  min-width: 200px;\\n}\\r\\n.min-w-\\\\[260px\\\\] {\\n  min-width: 260px;\\n}\\r\\n.min-w-full {\\n  min-width: 100%;\\n}\\r\\n.\\\\!max-w-\\\\[60\\\\%\\\\] {\\n  max-width: 60% !important;\\n}\\r\\n.\\\\!max-w-\\\\[600px\\\\] {\\n  max-width: 600px !important;\\n}\\r\\n.\\\\!max-w-\\\\[90\\\\%\\\\] {\\n  max-width: 90% !important;\\n}\\r\\n.max-w-full {\\n  max-width: 100%;\\n}\\r\\n.max-w-md {\\n  max-width: 28rem;\\n}\\r\\n.max-w-7xl {\\n  max-width: 80rem;\\n}\\r\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\r\\n.table-fixed {\\n  table-layout: fixed;\\n}\\r\\n.border-collapse {\\n  border-collapse: collapse;\\n}\\r\\n.-translate-x-1\\\\/2 {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-y-1\\\\/2 {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-rotate-90 {\\n  --tw-rotate: -90deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-180 {\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-45 {\\n  --tw-rotate: 45deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-100 {\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-95 {\\n  --tw-scale-x: .95;\\n  --tw-scale-y: .95;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-x-100 {\\n  --tw-scale-x: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\r\\n.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\r\\n.cursor-default {\\n  cursor: default;\\n}\\r\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\r\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\r\\n.resize-none {\\n  resize: none;\\n}\\r\\n.resize {\\n  resize: both;\\n}\\r\\n.list-inside {\\n  list-style-position: inside;\\n}\\r\\n.list-disc {\\n  list-style-type: disc;\\n}\\r\\n.appearance-none {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n}\\r\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\r\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\r\\n.grid-cols-3 {\\n  grid-template-columns: repeat(3, minmax(0, 1fr));\\n}\\r\\n.grid-cols-4 {\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n}\\r\\n.grid-cols-8 {\\n  grid-template-columns: repeat(8, minmax(0, 1fr));\\n}\\r\\n.\\\\!flex-row {\\n  flex-direction: row !important;\\n}\\r\\n.flex-row {\\n  flex-direction: row;\\n}\\r\\n.flex-col {\\n  flex-direction: column;\\n}\\r\\n.flex-wrap {\\n  flex-wrap: wrap;\\n}\\r\\n.items-start {\\n  align-items: flex-start;\\n}\\r\\n.items-end {\\n  align-items: flex-end;\\n}\\r\\n.\\\\!items-center {\\n  align-items: center !important;\\n}\\r\\n.items-center {\\n  align-items: center;\\n}\\r\\n.items-baseline {\\n  align-items: baseline;\\n}\\r\\n.items-stretch {\\n  align-items: stretch;\\n}\\r\\n.justify-start {\\n  justify-content: flex-start;\\n}\\r\\n.justify-end {\\n  justify-content: flex-end;\\n}\\r\\n.justify-center {\\n  justify-content: center;\\n}\\r\\n.\\\\!justify-between {\\n  justify-content: space-between !important;\\n}\\r\\n.justify-between {\\n  justify-content: space-between;\\n}\\r\\n.justify-around {\\n  justify-content: space-around;\\n}\\r\\n.\\\\!gap-0 {\\n  gap: 0px !important;\\n}\\r\\n.gap-1 {\\n  gap: 0.25rem;\\n}\\r\\n.gap-10 {\\n  gap: 2.5rem;\\n}\\r\\n.gap-12 {\\n  gap: 3rem;\\n}\\r\\n.gap-2 {\\n  gap: 0.5rem;\\n}\\r\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\r\\n.gap-4 {\\n  gap: 1rem;\\n}\\r\\n.gap-5 {\\n  gap: 1.25rem;\\n}\\r\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\r\\n.gap-8 {\\n  gap: 2rem;\\n}\\r\\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\r\\n.overflow-auto {\\n  overflow: auto;\\n}\\r\\n.\\\\!overflow-hidden {\\n  overflow: hidden !important;\\n}\\r\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\r\\n.overflow-x-auto {\\n  overflow-x: auto;\\n}\\r\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\r\\n.whitespace-nowrap {\\n  white-space: nowrap;\\n}\\r\\n.\\\\!rounded-md {\\n  border-radius: 0.375rem !important;\\n}\\r\\n.\\\\!rounded-xl {\\n  border-radius: 0.75rem !important;\\n}\\r\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\r\\n.rounded-\\\\[4px\\\\] {\\n  border-radius: 4px;\\n}\\r\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\r\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\r\\n.rounded-md {\\n  border-radius: 0.375rem;\\n}\\r\\n.rounded-sm {\\n  border-radius: 0.125rem;\\n}\\r\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\r\\n.rounded-2xl {\\n  border-radius: 1rem;\\n}\\r\\n.rounded-b-lg {\\n  border-bottom-right-radius: 0.5rem;\\n  border-bottom-left-radius: 0.5rem;\\n}\\r\\n.rounded-t {\\n  border-top-left-radius: 0.25rem;\\n  border-top-right-radius: 0.25rem;\\n}\\r\\n.rounded-t-lg {\\n  border-top-left-radius: 0.5rem;\\n  border-top-right-radius: 0.5rem;\\n}\\r\\n.rounded-bl-lg {\\n  border-bottom-left-radius: 0.5rem;\\n}\\r\\n.rounded-bl-md {\\n  border-bottom-left-radius: 0.375rem;\\n}\\r\\n.rounded-br-lg {\\n  border-bottom-right-radius: 0.5rem;\\n}\\r\\n.rounded-br-md {\\n  border-bottom-right-radius: 0.375rem;\\n}\\r\\n.rounded-tl-lg {\\n  border-top-left-radius: 0.5rem;\\n}\\r\\n.rounded-tl-md {\\n  border-top-left-radius: 0.375rem;\\n}\\r\\n.rounded-tl-xl {\\n  border-top-left-radius: 0.75rem;\\n}\\r\\n.rounded-tr-lg {\\n  border-top-right-radius: 0.5rem;\\n}\\r\\n.rounded-tr-md {\\n  border-top-right-radius: 0.375rem;\\n}\\r\\n.rounded-tr-xl {\\n  border-top-right-radius: 0.75rem;\\n}\\r\\n.\\\\!border {\\n  border-width: 1px !important;\\n}\\r\\n.\\\\!border-0 {\\n  border-width: 0px !important;\\n}\\r\\n.border {\\n  border-width: 1px;\\n}\\r\\n.border-0 {\\n  border-width: 0px;\\n}\\r\\n.border-2 {\\n  border-width: 2px;\\n}\\r\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\r\\n.border-b-2 {\\n  border-bottom-width: 2px;\\n}\\r\\n.border-e-\\\\[1px\\\\] {\\n  border-inline-end-width: 1px;\\n}\\r\\n.border-r {\\n  border-right-width: 1px;\\n}\\r\\n.border-t {\\n  border-top-width: 1px;\\n}\\r\\n.border-dashed {\\n  border-style: dashed;\\n}\\r\\n.\\\\!border-bright-red {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgb(249 54 71 / var(--tw-border-opacity)) !important;\\n}\\r\\n.\\\\!border-red-500 {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity)) !important;\\n}\\r\\n.\\\\!border-seablue {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgb(0 224 213 / var(--tw-border-opacity)) !important;\\n}\\r\\n.\\\\!border-skin-primary {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgba(var(--color-primary), var(--tw-border-opacity)) !important;\\n}\\r\\n.border-\\\\[\\\\#0066ff\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 102 255 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#FBB522\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(251 181 34 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#cccccc\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(204 204 204 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#d3d3d3\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(211 211 211 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#ddd\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(221 221 221 / var(--tw-border-opacity));\\n}\\r\\n.border-blue-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(191 219 254 / var(--tw-border-opacity));\\n}\\r\\n.border-blue-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(147 197 253 / var(--tw-border-opacity));\\n}\\r\\n.border-blue-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity));\\n}\\r\\n.border-bright-green {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(62 171 88 / var(--tw-border-opacity));\\n}\\r\\n.border-bright-red {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(249 54 71 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-100 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(243 244 246 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(156 163 175 / var(--tw-border-opacity));\\n}\\r\\n.border-green-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity));\\n}\\r\\n.border-light-gray {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(214 214 214 / var(--tw-border-opacity));\\n}\\r\\n.border-light-gray2 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(176 176 176 / var(--tw-border-opacity));\\n}\\r\\n.border-light-gray3 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(211 211 211 / var(--tw-border-opacity));\\n}\\r\\n.border-red-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity));\\n}\\r\\n.border-save-green {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(37 174 101 / var(--tw-border-opacity));\\n}\\r\\n.border-skin-primary {\\n  --tw-border-opacity: 1;\\n  border-color: rgba(var(--color-primary), var(--tw-border-opacity));\\n}\\r\\n.border-theme-blue2 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 102 255 / var(--tw-border-opacity));\\n}\\r\\n.border-transparent {\\n  border-color: transparent;\\n}\\r\\n.border-white {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity));\\n}\\r\\n.border-blue-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(96 165 250 / var(--tw-border-opacity));\\n}\\r\\n.border-red-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity));\\n}\\r\\n.\\\\!bg-\\\\[\\\\#f3f8ff\\\\] {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(243 248 255 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-be-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 154 3 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-confirm-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(51 202 127 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-gray-100 {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-issue-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(251 70 70 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-locked-products {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(211 234 255 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-needsupdate-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 245 208 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-pricechange-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(44 176 250 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-seablue {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(0 224 213 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-transparent {\\n  background-color: transparent !important;\\n}\\r\\n.\\\\!bg-volumechange-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(179 187 221 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-white {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-wip-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 223 55 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.bg-\\\\[\\\\#00E0D5\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 224 213 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#3EAB58\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(62 171 88 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#54C5ED\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(84 197 237 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#F3F8FF\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 248 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#FF6C09\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 108 9 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#FFAE00\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 174 0 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#f3f8ff\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 248 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#ff2929\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 41 41 / var(--tw-bg-opacity));\\n}\\r\\n.bg-be-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 154 3 / var(--tw-bg-opacity));\\n}\\r\\n.bg-black {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity));\\n}\\r\\n.bg-black\\\\/25 {\\n  background-color: rgb(0 0 0 / 0.25);\\n}\\r\\n.bg-blue-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity));\\n}\\r\\n.bg-blue-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity));\\n}\\r\\n.bg-cancelled-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 108 9 / var(--tw-bg-opacity));\\n}\\r\\n.bg-complete-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(62 171 88 / var(--tw-bg-opacity));\\n}\\r\\n.bg-confirm-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 202 127 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity));\\n}\\r\\n.bg-green-300 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(134 239 172 / var(--tw-bg-opacity));\\n}\\r\\n.bg-green-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity));\\n}\\r\\n.bg-green-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity));\\n}\\r\\n.bg-green-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity));\\n}\\r\\n.bg-issue-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(251 70 70 / var(--tw-bg-opacity));\\n}\\r\\n.bg-locked-products {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(211 234 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-needsupdate-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 245 208 / var(--tw-bg-opacity));\\n}\\r\\n.bg-pricechange-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(44 176 250 / var(--tw-bg-opacity));\\n}\\r\\n.bg-qtydiff-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 203 71 / var(--tw-bg-opacity));\\n}\\r\\n.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity));\\n}\\r\\n.bg-red-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity));\\n}\\r\\n.bg-save-green {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 174 101 / var(--tw-bg-opacity));\\n}\\r\\n.bg-skin-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgba(var(--color-primary), var(--tw-bg-opacity));\\n}\\r\\n.bg-skin-primary\\\\/10 {\\n  background-color: rgba(var(--color-primary), 0.1);\\n}\\r\\n.bg-theme-blue {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 45 115 / var(--tw-bg-opacity));\\n}\\r\\n.bg-theme-blue2 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 102 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-transparent {\\n  background-color: transparent;\\n}\\r\\n.bg-volumechange-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(179 187 221 / var(--tw-bg-opacity));\\n}\\r\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-wip-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 223 55 / var(--tw-bg-opacity));\\n}\\r\\n.bg-blue-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity));\\n}\\r\\n.bg-red-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity));\\n}\\r\\n.bg-green-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity));\\n}\\r\\n.bg-red-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity));\\n}\\r\\n.bg-opacity-25 {\\n  --tw-bg-opacity: 0.25;\\n}\\r\\n.bg-cover {\\n  background-size: cover;\\n}\\r\\n.fill-current {\\n  fill: currentColor;\\n}\\r\\n.fill-gray-300 {\\n  fill: #d1d5db;\\n}\\r\\n.fill-skin-primary {\\n  fill: rgb(var(--color-primary));\\n}\\r\\n.fill-white {\\n  fill: #FFFFFF;\\n}\\r\\n.\\\\!p-0 {\\n  padding: 0px !important;\\n}\\r\\n.\\\\!p-1 {\\n  padding: 0.25rem !important;\\n}\\r\\n.\\\\!p-3 {\\n  padding: 0.75rem !important;\\n}\\r\\n.\\\\!p-5 {\\n  padding: 1.25rem !important;\\n}\\r\\n.p-0 {\\n  padding: 0px;\\n}\\r\\n.p-1 {\\n  padding: 0.25rem;\\n}\\r\\n.p-2 {\\n  padding: 0.5rem;\\n}\\r\\n.p-3 {\\n  padding: 0.75rem;\\n}\\r\\n.p-4 {\\n  padding: 1rem;\\n}\\r\\n.p-6 {\\n  padding: 1.5rem;\\n}\\r\\n.p-8 {\\n  padding: 2rem;\\n}\\r\\n.p-\\\\[6px\\\\] {\\n  padding: 6px;\\n}\\r\\n.p-10 {\\n  padding: 2.5rem;\\n}\\r\\n.\\\\!px-1 {\\n  padding-left: 0.25rem !important;\\n  padding-right: 0.25rem !important;\\n}\\r\\n.\\\\!px-3 {\\n  padding-left: 0.75rem !important;\\n  padding-right: 0.75rem !important;\\n}\\r\\n.\\\\!px-4 {\\n  padding-left: 1rem !important;\\n  padding-right: 1rem !important;\\n}\\r\\n.\\\\!py-1 {\\n  padding-top: 0.25rem !important;\\n  padding-bottom: 0.25rem !important;\\n}\\r\\n.\\\\!py-3 {\\n  padding-top: 0.75rem !important;\\n  padding-bottom: 0.75rem !important;\\n}\\r\\n.px-0 {\\n  padding-left: 0px;\\n  padding-right: 0px;\\n}\\r\\n.px-1 {\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\r\\n.px-14 {\\n  padding-left: 3.5rem;\\n  padding-right: 3.5rem;\\n}\\r\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n.px-2\\\\.5 {\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n}\\r\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\r\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\r\\n.px-5 {\\n  padding-left: 1.25rem;\\n  padding-right: 1.25rem;\\n}\\r\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\r\\n.px-8 {\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\r\\n.px-9 {\\n  padding-left: 2.25rem;\\n  padding-right: 2.25rem;\\n}\\r\\n.px-\\\\[9px\\\\] {\\n  padding-left: 9px;\\n  padding-right: 9px;\\n}\\r\\n.py-0 {\\n  padding-top: 0px;\\n  padding-bottom: 0px;\\n}\\r\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\r\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\r\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\r\\n.py-5 {\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\r\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\r\\n.py-\\\\[5px\\\\] {\\n  padding-top: 5px;\\n  padding-bottom: 5px;\\n}\\r\\n.\\\\!pb-3 {\\n  padding-bottom: 0.75rem !important;\\n}\\r\\n.pb-0 {\\n  padding-bottom: 0px;\\n}\\r\\n.pb-1 {\\n  padding-bottom: 0.25rem;\\n}\\r\\n.pb-2 {\\n  padding-bottom: 0.5rem;\\n}\\r\\n.pb-20 {\\n  padding-bottom: 5rem;\\n}\\r\\n.pb-3 {\\n  padding-bottom: 0.75rem;\\n}\\r\\n.pb-4 {\\n  padding-bottom: 1rem;\\n}\\r\\n.pe-1 {\\n  padding-inline-end: 0.25rem;\\n}\\r\\n.pe-8 {\\n  padding-inline-end: 2rem;\\n}\\r\\n.pl-0 {\\n  padding-left: 0px;\\n}\\r\\n.pl-1 {\\n  padding-left: 0.25rem;\\n}\\r\\n.pl-10 {\\n  padding-left: 2.5rem;\\n}\\r\\n.pl-12 {\\n  padding-left: 3rem;\\n}\\r\\n.pl-2 {\\n  padding-left: 0.5rem;\\n}\\r\\n.pl-3 {\\n  padding-left: 0.75rem;\\n}\\r\\n.pl-4 {\\n  padding-left: 1rem;\\n}\\r\\n.pl-\\\\[2px\\\\] {\\n  padding-left: 2px;\\n}\\r\\n.pl-\\\\[88px\\\\] {\\n  padding-left: 88px;\\n}\\r\\n.pr-12 {\\n  padding-right: 3rem;\\n}\\r\\n.pr-3 {\\n  padding-right: 0.75rem;\\n}\\r\\n.pr-4 {\\n  padding-right: 1rem;\\n}\\r\\n.pr-\\\\[10px\\\\] {\\n  padding-right: 10px;\\n}\\r\\n.pr-\\\\[12px\\\\] {\\n  padding-right: 12px;\\n}\\r\\n.pr-\\\\[18px\\\\] {\\n  padding-right: 18px;\\n}\\r\\n.pr-\\\\[5px\\\\] {\\n  padding-right: 5px;\\n}\\r\\n.pr-\\\\[70px\\\\] {\\n  padding-right: 70px;\\n}\\r\\n.ps-8 {\\n  padding-inline-start: 2rem;\\n}\\r\\n.pt-0 {\\n  padding-top: 0px;\\n}\\r\\n.pt-1 {\\n  padding-top: 0.25rem;\\n}\\r\\n.pt-10 {\\n  padding-top: 2.5rem;\\n}\\r\\n.pt-2 {\\n  padding-top: 0.5rem;\\n}\\r\\n.pt-3 {\\n  padding-top: 0.75rem;\\n}\\r\\n.pt-4 {\\n  padding-top: 1rem;\\n}\\r\\n.pt-5 {\\n  padding-top: 1.25rem;\\n}\\r\\n.pt-6 {\\n  padding-top: 1.5rem;\\n}\\r\\n.pt-\\\\[0\\\\.4rem\\\\] {\\n  padding-top: 0.4rem;\\n}\\r\\n.pt-\\\\[80px\\\\] {\\n  padding-top: 80px;\\n}\\r\\n.\\\\!text-left {\\n  text-align: left !important;\\n}\\r\\n.text-left {\\n  text-align: left;\\n}\\r\\n.\\\\!text-center {\\n  text-align: center !important;\\n}\\r\\n.text-center {\\n  text-align: center;\\n}\\r\\n.align-top {\\n  vertical-align: top;\\n}\\r\\n.align-middle {\\n  vertical-align: middle;\\n}\\r\\n.font-poppinsregular {\\n  font-family: poppinsregular;\\n}\\r\\n.font-poppinssemibold {\\n  font-family: poppinssemibold;\\n}\\r\\n.\\\\!text-base {\\n  font-size: 1rem !important;\\n  line-height: 1.5rem !important;\\n}\\r\\n.\\\\!text-xs {\\n  font-size: 0.75rem !important;\\n  line-height: 1rem !important;\\n}\\r\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\r\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\r\\n.text-\\\\[10px\\\\] {\\n  font-size: 10px;\\n}\\r\\n.text-\\\\[16px\\\\] {\\n  font-size: 16px;\\n}\\r\\n.text-\\\\[20px\\\\] {\\n  font-size: 20px;\\n}\\r\\n.text-base {\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\r\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n.\\\\!font-medium {\\n  font-weight: 500 !important;\\n}\\r\\n.\\\\!font-normal {\\n  font-weight: 400 !important;\\n}\\r\\n.font-bold {\\n  font-weight: 700;\\n}\\r\\n.font-extrabold {\\n  font-weight: 800;\\n}\\r\\n.font-medium {\\n  font-weight: 500;\\n}\\r\\n.font-normal {\\n  font-weight: 400;\\n}\\r\\n.font-semibold {\\n  font-weight: 600;\\n}\\r\\n.uppercase {\\n  text-transform: uppercase;\\n}\\r\\n.capitalize {\\n  text-transform: capitalize;\\n}\\r\\n.italic {\\n  font-style: italic;\\n}\\r\\n.leading-5 {\\n  line-height: 1.25rem;\\n}\\r\\n.leading-8 {\\n  line-height: 2rem;\\n}\\r\\n.leading-\\\\[30px\\\\] {\\n  line-height: 30px;\\n}\\r\\n.leading-relaxed {\\n  line-height: 1.625;\\n}\\r\\n.tracking-\\\\[0\\\\.05em\\\\] {\\n  letter-spacing: 0.05em;\\n}\\r\\n.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\r\\n.tracking-wide {\\n  letter-spacing: 0.025em;\\n}\\r\\n.tracking-wider {\\n  letter-spacing: 0.05em;\\n}\\r\\n.\\\\!text-\\\\[\\\\#333333\\\\] {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(51 51 51 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-gray-300 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(209 213 219 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-gray-500 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(107 114 128 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-gray-700 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(55 65 81 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-red-500 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(239 68 68 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-skin-primary {\\n  --tw-text-opacity: 1 !important;\\n  color: rgba(var(--color-primary), var(--tw-text-opacity)) !important;\\n}\\r\\n.text-\\\\[\\\\#B31312\\\\] {\\n  --tw-text-opacity: 1;\\n  color: rgb(179 19 18 / var(--tw-text-opacity));\\n}\\r\\n.text-\\\\[\\\\#FBB522\\\\] {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 181 34 / var(--tw-text-opacity));\\n}\\r\\n.text-\\\\[\\\\#ffffff\\\\] {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity));\\n}\\r\\n.text-black {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity));\\n}\\r\\n.text-blue-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(59 130 246 / var(--tw-text-opacity));\\n}\\r\\n.text-blue-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity));\\n}\\r\\n.text-blue-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity));\\n}\\r\\n.text-blue-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity));\\n}\\r\\n.text-bright-green {\\n  --tw-text-opacity: 1;\\n  color: rgb(62 171 88 / var(--tw-text-opacity));\\n}\\r\\n.text-bright-red {\\n  --tw-text-opacity: 1;\\n  color: rgb(249 54 71 / var(--tw-text-opacity));\\n}\\r\\n.text-bright-yellow {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 181 34 / var(--tw-text-opacity));\\n}\\r\\n.text-dark-gray {\\n  --tw-text-opacity: 1;\\n  color: rgb(114 114 114 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity));\\n}\\r\\n.text-green-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 197 94 / var(--tw-text-opacity));\\n}\\r\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity));\\n}\\r\\n.text-green-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity));\\n}\\r\\n.text-issue-status {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 70 70 / var(--tw-text-opacity));\\n}\\r\\n.text-light-gray2 {\\n  --tw-text-opacity: 1;\\n  color: rgb(176 176 176 / var(--tw-text-opacity));\\n}\\r\\n.text-orange-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(249 115 22 / var(--tw-text-opacity));\\n}\\r\\n.text-red-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity));\\n}\\r\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity));\\n}\\r\\n.text-skin-a11y {\\n  --tw-text-opacity: 1;\\n  color: rgba(var(--color-a11y), var(--tw-text-opacity));\\n}\\r\\n.text-skin-primary {\\n  --tw-text-opacity: 1;\\n  color: rgba(var(--color-primary), var(--tw-text-opacity));\\n}\\r\\n.text-theme-blue2 {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 102 255 / var(--tw-text-opacity));\\n}\\r\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity));\\n}\\r\\n.text-green-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(21 128 61 / var(--tw-text-opacity));\\n}\\r\\n.text-red-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(185 28 28 / var(--tw-text-opacity));\\n}\\r\\n.underline {\\n  text-decoration-line: underline;\\n}\\r\\n.placeholder-gray-400::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity));\\n}\\r\\n.placeholder-gray-400::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity));\\n}\\r\\n.accent-skin-primary {\\n  accent-color: rgb(var(--color-primary));\\n}\\r\\n.opacity-0 {\\n  opacity: 0;\\n}\\r\\n.opacity-100 {\\n  opacity: 1;\\n}\\r\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\r\\n.opacity-70 {\\n  opacity: 0.7;\\n}\\r\\n.\\\\!shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color) !important;\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;\\n}\\r\\n.shadow {\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-\\\\[0_0_1px_rgba\\\\(0\\\\2c 0\\\\2c 0\\\\2c 0\\\\.1\\\\)\\\\] {\\n  --tw-shadow: 0 0 1px rgba(0,0,0,0.1);\\n  --tw-shadow-colored: 0 0 1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-\\\\[0_0_2px_rgba\\\\(0\\\\2c 0\\\\2c 0\\\\2c 0\\\\.2\\\\)\\\\] {\\n  --tw-shadow: 0 0 2px rgba(0,0,0,0.2);\\n  --tw-shadow-colored: 0 0 2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-\\\\[0_0_5px_rgba\\\\(0\\\\2c 0\\\\2c 0\\\\2c 0\\\\.1\\\\)\\\\] {\\n  --tw-shadow: 0 0 5px rgba(0,0,0,0.1);\\n  --tw-shadow-colored: 0 0 5px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-md {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-xl {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.outline {\\n  outline-style: solid;\\n}\\r\\n.ring-2 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.backdrop-blur-none {\\n  --tw-backdrop-blur: blur(0);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-shadow {\\n  transition-property: box-shadow;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\r\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\r\\n.ease-in {\\n  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);\\n}\\r\\n.ease-in-out {\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\r\\n.ease-out {\\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n}\\r\\n/*@import \\\"bootstrap/bootstrap\\\";*/\\r\\n\\r\\n@font-face {\\r\\n  font-family: poppinsblack;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsblackitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_1___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbold;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_2___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbolditalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_3___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabold;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_4___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabolditalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_5___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralight;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_6___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralightitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_7___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_8___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslight;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_9___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslightitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_10___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmedium;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_11___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmediumitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_12___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsregular;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_13___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibold;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_14___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibolditalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_15___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_8___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_8___ + \");\\r\\n}\\r\\n\\r\\nbody,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  overflow-x: hidden;\\r\\n  background-color: #f3f8ff;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark body,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\" !important;\\r\\n  overflow-x: hidden;\\r\\n  background-color: #0e0e10;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.ag-header .ag-header-cell.header-with-border  {\\r\\n  border-bottom: 1px solid #ccc;\\r\\n}\\r\\n\\r\\n.ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\nbutton[disabled] {\\r\\n  opacity: 0.5;\\r\\n  cursor: not-allowed;\\r\\n}\\r\\n\\r\\n.dark .ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #fff;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell {\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark .ag-ltr .ag-cell {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-panel {\\r\\n  justify-content: between;\\r\\n}\\r\\n.dark .ag-paging-panel {\\r\\n  justify-content: between;\\r\\n  color: white;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.dark .ag-center-cols-viewport {\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.contentsectionbg {\\r\\n  background-color: white;\\r\\n}\\r\\n\\r\\n.dark .contentsectionbg {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark\\r\\n  .ag-header.ag-header-allow-overflow\\r\\n  .ag-header-row\\r\\n  .ag-root-wrapper.ag-layout-normal {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .ag-header-container {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .pagination-style {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.dark .ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n.ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: #000;\\r\\n}\\r\\n.dark .ag-paging-button.ag-disabled {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-body-horizontal-scroll-viewport {\\r\\n  display: none;\\r\\n} \\r\\n\\r\\n\\r\\n.ag-overlay-no-rows-center {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:hover,\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:focus {\\r\\n  color: #ccc;\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\nnav:hover .forAnimate {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n.mainmenu {\\r\\n  background: #002d73;\\r\\n  border: #002d73;\\r\\n  border-radius: 0px;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > .active > a,\\r\\n.mainmenu .navbar-nav > .active > a:hover,\\r\\n.mainmenu .navbar-nav > .active > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: #2a3344;\\r\\n  border-left: 2px solid #1ca9c0;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > li > a:hover,\\r\\n.mainmenu .navbar-nav > li > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: lightgray;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n}\\r\\n\\r\\n.navbar-default .navbar-nav > li > a {\\r\\n  color: #00b1a3;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n  text-decoration: none;\\r\\n}\\r\\n\\r\\n.navbar {\\r\\n  min-height: 45px;\\r\\n}\\r\\n\\r\\n.page-heading {\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n  margin-top: 0px !important;\\r\\n  margin-left: 25px;\\r\\n  /* position: absolute; */\\r\\n  top: 10px;\\r\\n  left: 90px;\\r\\n  font-weight: 600;\\r\\n}\\r\\n\\r\\n.page-heading h2 {\\r\\n  font-size: 16px;\\r\\n  margin-top: 15px !important;\\r\\n}\\r\\n\\r\\n/* ----------- Notification dropdown end ----------------------- */\\r\\n\\r\\nnav.sidebar .brand a {\\r\\n  padding: 0;\\r\\n}\\r\\n\\r\\n.brand {\\r\\n  font-size: 24px;\\r\\n  /* padding: 0px 5px; */\\r\\n  color: #fff;\\r\\n  width: 69px;\\r\\n  /* background-color: #002d73; */\\r\\n  text-align: center;\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.titlebar {\\r\\n  width: 100%;\\r\\n  position: fixed;\\r\\n  /* height: 55px; */\\r\\n  z-index: 98;\\r\\n  top: 0;\\r\\n  padding-left: 69px;\\r\\n}\\r\\n\\r\\n.accordion {\\r\\n  cursor: pointer;\\r\\n  width: 100%;\\r\\n  text-align: left;\\r\\n  outline: none;\\r\\n  transition: 0.4s;\\r\\n}\\r\\n\\r\\n.active,\\r\\n.accordion:hover {\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\n.panel {\\r\\n  background-color: white;\\r\\n  max-height: 0;\\r\\n  overflow: hidden;\\r\\n  transition: max-height 0.2s ease-out;\\r\\n}\\r\\n\\r\\n/* .reactSelectCustom .css-1fdsijx-ValueContainer , .reactSelectCustom .css-b62m3t-ValueContainer {\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n}\\r\\n\\r\\n.reactSelectCustom .css-1hb7zxy-IndicatorsContainer, .reactSelectCustom .css-1xc3v61-IndicatorsContainer{\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n} */\\r\\n/* .reactSelectCustom .css-1jgx7bw-control{\\r\\n  flex-wrap: nowrap !important;\\r\\n} */\\r\\ninput[type=date]:invalid::-ms-datetime-edit {\\r\\n  color: #808080;\\r\\n}\\r\\n/* label {\\r\\n  font-size: 14px;\\r\\n  color: #505050;\\r\\n} */\\r\\n.top-navbar {\\r\\n  background-color: white;\\r\\n}\\r\\n.dark .top-navbar {\\r\\n  background-color: #000;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.dark .pageName {\\r\\n  color: white;\\r\\n}\\r\\n\\r\\ninput[type=\\\"number\\\"] {\\r\\n  -webkit-appearance: textfield;\\r\\n     -moz-appearance: textfield;\\r\\n          appearance: textfield;\\r\\n}\\r\\ninput[type=number]::-webkit-inner-spin-button, \\r\\ninput[type=number]::-webkit-outer-spin-button { \\r\\n  -webkit-appearance: none;\\r\\n}\\r\\n\\r\\ninput[type=\\\"text\\\"],\\r\\ninput[type=\\\"tel\\\"],\\r\\ninput[type=\\\"date\\\"],\\r\\ninput[type=\\\"email\\\"],\\r\\ninput[type=\\\"number\\\"],\\r\\nselect {\\r\\n  padding-top: 4px;\\r\\n  padding-bottom: 4px;\\r\\n  border-color: #d6d6d6;\\r\\n  outline: 2px solid #ffffff;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\ninput:focus, select:focus , textarea:focus{\\r\\n  outline-color: #0066ff;\\r\\n}\\r\\n\\r\\n.button {\\r\\n  padding: 4px 12px 4px 12px;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #505050;\\r\\n}\\r\\n.dark .labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #AFAFAF;\\r\\n  background-color: #FFFFFF;\\r\\n}\\r\\n\\r\\n.dark .searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #c0c0c0;\\r\\n  background-color: #1d212d;\\r\\n  outline-color: #1d212d;\\r\\n}\\r\\n\\r\\n.checkboxbg {\\r\\n  background-color: #D9D9D9;\\r\\n}\\r\\n.dark .checkboxbg {\\r\\n  background-color: #4d4d4d;\\r\\n}\\r\\n\\r\\n.dark select {\\r\\n  background-color: #1d1d1d;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.borderc-theme-blue2 {\\r\\n  border-color: #0066ff;\\r\\n}\\r\\n.dark .borderc-theme-blue2 {\\r\\n  border-color: #6699ff;\\r\\n}\\r\\n.textc-theme-blue2 {\\r\\n  color: #0066ff;\\r\\n}\\r\\n.dark .textc-theme-blue2 {\\r\\n  color: #6699ff;\\r\\n}\\r\\n\\r\\n.bgc-theme-blue2 {\\r\\n  background-color: #0066ff;\\r\\n}\\r\\n.dark .bgc-theme-blue2 {\\r\\n  background-color: #6699ff;\\r\\n}\\r\\n.textc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .textc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n.borderc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .borderc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n\\r\\n.input {\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .inputs {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.confirmInputs {\\r\\n    font-size: 12px;\\r\\n    color: #333333;\\r\\n    font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.formtitle {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.subtitles {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.text-blackcolor {\\r\\n  color:#505050;\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within {\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-row-odd{\\r\\n  background-color: #f3f3f3 !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value span {\\r\\n  height: auto !important;\\r\\n  line-height: normal !important;\\r\\n}\\r\\n\\r\\n.ag-cell{\\r\\n  display:flex;\\r\\n  align-items:center;\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value, .ag-header-cell-text{\\r\\n  text-overflow: unset !important;\\r\\n}\\r\\n\\r\\n.pagination-style {\\r\\n  position: absolute;\\r\\n  bottom: 10px;\\r\\n  left: 20px;\\r\\n}\\r\\n\\r\\n.pagination-style select {\\r\\n  background-color: #f2f2f2;\\r\\n  border-radius: 3px;\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-header-select-all{\\r\\n  margin-right: 12px !important; \\r\\n}\\r\\n\\r\\n.ag-root-wrapper.ag-layout-normal{\\r\\n  border-radius: 5px !important;\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-row.ag-header-row-column {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-center-cols-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.general_section .ag-header-container, .general_section .ag-center-cols-container, .general_section .ag-header-row  {\\r\\n  width: 100% !important;\\r\\n}\\r\\n\\r\\n.general_section .ag-cell {\\r\\n  /* display: flex; */\\r\\n  margin-right: 10px;\\r\\n}\\r\\n\\r\\n.product_link_def .ag-row .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.product_data_def .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n/* @media (min-width: 640px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 10px;\\r\\n  }\\r\\n} */\\r\\n\\r\\n.viewlog .ag-cell-value {\\r\\n    align-items: center;\\r\\n    display: flex;\\r\\n}\\r\\n\\r\\n@media (min-width: 765px) {\\r\\n  .main {\\r\\n    position: absolute;\\r\\n    width: calc(100% - 40px);\\r\\n    margin-left: 40px;\\r\\n    float: right;\\r\\n  }\\r\\n  nav.sidebar:hover + .main {\\r\\n    margin-left: 200px;\\r\\n  }\\r\\n  nav.sidebar.navbar.sidebar > .container .navbar-brand,\\r\\n  .navbar > .container-fluid .navbar-brand {\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  nav.sidebar .navbar-brand,\\r\\n  nav.sidebar .navbar-header {\\r\\n    text-align: center;\\r\\n    width: 100%;\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  /* nav.sidebar a {\\r\\n    padding-bottom: 34px;\\r\\n  } */\\r\\n\\r\\n  nav.sidebar .navbar-nav > li {\\r\\n    font-size: 13px;\\r\\n  }\\r\\n  nav.sidebar .navbar-nav .open .dropdown-menu {\\r\\n    position: static;\\r\\n    float: none;\\r\\n    width: auto;\\r\\n    margin-top: 0;\\r\\n    background-color: transparent;\\r\\n    border: 0;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  nav.sidebar .navbar-collapse,\\r\\n  nav.sidebar .container-fluid {\\r\\n    padding: 0 0px 0 0px;\\r\\n  }\\r\\n  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {\\r\\n    color: #777;\\r\\n  }\\r\\n  nav.sidebar {\\r\\n    width: 69px;\\r\\n    height: 100%;\\r\\n    margin-bottom: 0px;\\r\\n    position: fixed;\\r\\n    top: 0px;\\r\\n    display: flex;\\r\\n    align-items: flex-start;\\r\\n  }\\r\\n  nav.sidebar li {\\r\\n    width: 100%;\\r\\n  }\\r\\n\\r\\n  .forAnimate {\\r\\n    opacity: 0;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n@media (min-width: 1024px) {\\r\\n  \\r\\n}\\r\\n\\r\\n/* @media (min-width: 1280px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 14px;\\r\\n  }}\\r\\n\\r\\n@media (min-width: 1536px) {\\r\\n  \\r\\n}\\r\\n@media (min-width: 1940px) {\\r\\n  \\r\\n} */\\r\\n\\r\\n.desktop-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n.block-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(214, 21, 21, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n\\r\\n.message-content {\\r\\n  text-align: center;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.message-content h2 {\\r\\n  font-size: 24px;\\r\\n  margin-bottom: 10px;\\r\\n}\\r\\n\\r\\n.message-content p {\\r\\n  font-size: 16px;\\r\\n}\\r\\n/* quarter filter css */\\r\\n\\r\\n #q1:checked  + .labelcheck, #q2:checked  + .labelcheck, #q3:checked  + .labelcheck, #q4:checked  + .labelcheck, #all:checked  + .labelcheck, #needsupdate:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* section filter css */\\r\\n #volume:checked  + .labelcheck, #breakeven:checked  + .labelcheck, #unitprice:checked  + .labelcheck, #grossprofit:checked  + .labelcheck, #gppercent:checked  + .labelcheck, #value:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n #bestatus-1:checked  + .labelcheck {\\r\\n  background-color: #FF9A03;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-2:checked  + .labelcheck{\\r\\n  background-color: #33CA7F;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#444;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-3:checked  + .labelcheck{\\r\\n  background-color: #FFDF37;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-4:checked  + .labelcheck{\\r\\n  background-color: #FB4646;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.planningtoolgrid\\r\\n{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n   /* border: 1px solid #ddd; */\\r\\n}\\r\\n.input-number::-webkit-inner-spin-button,\\r\\n.input-number::-webkit-outer-spin-button {\\r\\n  -webkit-appearance: none;\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n\\r\\n.planningtoolgrid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: center;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* .planningtoolgrid thead tr, .planningtoolgrid tbody tr{\\r\\n  border: 1px solid #ddd;\\r\\n} */\\r\\n.planningtoolgrid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n}\\r\\n/* .planningtoolgrid tbody tr{\\r\\n height:70px !important;\\r\\n} */\\r\\n.planningtoolgrid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n}\\r\\n.planningtoolgrid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.planningtoolgrid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  background:#f3f8ff;\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.quartertotals{\\r\\n  /* border:0; */\\r\\n  table-layout: fixed;\\r\\n  width:50%;\\r\\n  text-align:center;\\r\\n}\\r\\n.quartertotals thead th, .quartertotals tbody td{\\r\\n  text-align: center;\\r\\n  border:0;\\r\\n}\\r\\n.quartertotals thead tr, .quartertotals tbody tr {\\r\\n    border: none;\\r\\n}\\r\\n.quartertotals thead th{\\r\\n  border-top-left-radius: 20px;\\r\\n  border-top-right-radius: 20px;\\r\\n  background-color: #fff !important;\\r\\n}\\r\\n.quartertotals thead{\\r\\n  z-index: 5 !important;\\r\\n}\\r\\n\\r\\n.quartertotals thead th:first-child, .quartertotals tbody td:first-child{\\r\\n  background-color: #f3f8ff !important;\\r\\n  width:50px !important;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(2){\\r\\n  background-color: #201E50 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #504F66;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(3){\\r\\n  background-color: #D499B9 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #C0749E;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(4){\\r\\n  background-color: #EE6C4D !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #CE4B2C;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody tr:last-child td{\\r\\n    border-bottom-left-radius: 15px;\\r\\n    border-bottom-right-radius: 15px;\\r\\n    border-bottom: none;\\r\\n}\\r\\n\\r\\n.titlerow{\\r\\n  background-color: #00E0D5;\\r\\n  color:#fff;\\r\\n  z-index: 9;\\r\\n  font-size: 15px;\\r\\n  height: 28px !important;\\r\\n}\\r\\ntd.sectionrow,th.sectionrow{\\r\\n  background-color: #DDD;\\r\\n  color:#444;\\r\\n  font-weight: 600;\\r\\n  z-index: 9;\\r\\n  height: 28px !important;\\r\\n  font-size: 13px !important;\\r\\n}\\r\\ninput[type=\\\"text\\\"]:disabled, input[type=\\\"number\\\"]:disabled, select:disabled{\\r\\n  background-color: #F6F3F3;\\r\\n}\\r\\n\\r\\n#wrapper.closed .list {\\r\\n  display: none;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n#wrapper.open .list {\\r\\n  display: block;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n.bg-currentWeek{\\r\\n  background-color: #fcf6b1;\\r\\n}\\r\\n.nodata{\\r\\n  background-color:#ffecd4;\\r\\n  color:#b31818\\r\\n}\\r\\n.selected{\\r\\n  color: #0066FF;\\r\\n  border-bottom: 3px solid #0066FF;\\r\\n}\\r\\n.buttonText{\\r\\n  font-family: 'poppinsmedium';\\r\\n  \\r\\n}\\r\\n\\r\\n/*--- SLP CSS ---*/\\r\\n\\r\\n.service-level-grid{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n}\\r\\n\\r\\n.service-level-grid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: left;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  white-space: normal;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n/* .service-level-grid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n} */\\r\\n.service-level-grid thead tr th:last-child{\\r\\n  background:#f3f8ff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n.service-level-grid tbody tr td:last-child{\\r\\n  background:#fff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n\\r\\n.service-level-grid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n  border: 1px solid #ddd;\\r\\n  padding:5px;\\r\\n  z-index: 5;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  background-color:white;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  /* background:#f3f8ff; */\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.ag-grid-checkbox-cell .ag-checkbox-input {\\r\\n  cursor: pointer;\\r\\n}\\r\\n\\r\\n.depotdaterange .rdrDateDisplayWrapper{\\r\\n  display:none;\\r\\n}\\r\\n\\r\\nlabel{\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\nselect::-moz-placeholder {\\r\\n  color: #333333 !important;\\r\\n  opacity: 0.5;\\r\\n  font-weight: 500;\\r\\n  font-family:\\\"poppinsregular\\\";\\r\\n}\\r\\nselect::placeholder, .css-1jqq78o-placeholder, .placeholdertext {\\r\\n  color: #333333 !important;\\r\\n  opacity: 0.5;\\r\\n  font-weight: 500;\\r\\n  font-family:\\\"poppinsregular\\\";\\r\\n}\\r\\n.text-truncate2L {\\r\\n  display: -webkit-box;\\r\\n  -webkit-box-orient: vertical;\\r\\n  -webkit-line-clamp: 2;\\r\\n  overflow: hidden;\\r\\n  text-overflow: ellipsis;\\r\\n  }\\r\\n\\r\\n  .variety-disabled-block{\\r\\n    background-color: #f6f6f6;\\r\\n    color: #888888;\\r\\n    border:1px solid #E2E2E2;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  .variety-disabled-block h4, .variety-disabled-block label{\\r\\n    color:#797979;\\r\\n  }\\r\\n  .after\\\\:absolute::after {\\n  content: var(--tw-content);\\n  position: absolute;\\n}\\r\\n  .after\\\\:left-\\\\[1px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 1px;\\n}\\r\\n  .after\\\\:top-2::after {\\n  content: var(--tw-content);\\n  top: 0.5rem;\\n}\\r\\n  .after\\\\:h-5::after {\\n  content: var(--tw-content);\\n  height: 1.25rem;\\n}\\r\\n  .after\\\\:w-5::after {\\n  content: var(--tw-content);\\n  width: 1.25rem;\\n}\\r\\n  .after\\\\:translate-x-0::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n  .after\\\\:rounded-full::after {\\n  content: var(--tw-content);\\n  border-radius: 9999px;\\n}\\r\\n  .after\\\\:border::after {\\n  content: var(--tw-content);\\n  border-width: 1px;\\n}\\r\\n  .after\\\\:border-light-gray::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(214 214 214 / var(--tw-border-opacity));\\n}\\r\\n  .after\\\\:bg-white::after {\\n  content: var(--tw-content);\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity));\\n}\\r\\n  .after\\\\:transition-all::after {\\n  content: var(--tw-content);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n  .after\\\\:content-\\\\[\\\\'\\\\'\\\\]::after {\\n  --tw-content: '';\\n  content: var(--tw-content);\\n}\\r\\n  .focus-within\\\\:text-gray-600:focus-within {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity));\\n}\\r\\n  .hover\\\\:cursor-pointer:hover {\\n  cursor: pointer;\\n}\\r\\n  .hover\\\\:border-gray-400:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(156 163 175 / var(--tw-border-opacity));\\n}\\r\\n  .hover\\\\:bg-blue-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-confirm-status\\\\/80:hover {\\n  background-color: rgb(51 202 127 / 0.8);\\n}\\r\\n  .hover\\\\:bg-gray-100:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-gray-200:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-gray-300:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-gray-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-gray-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-red-500:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-red-600:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-skin-primary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgba(var(--color-primary), var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-blue-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-blue-600:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-gray-600:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-green-600:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:text-black:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity));\\n}\\r\\n  .hover\\\\:text-gray-900:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity));\\n}\\r\\n  .hover\\\\:text-blue-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity));\\n}\\r\\n  .hover\\\\:underline:hover {\\n  text-decoration-line: underline;\\n}\\r\\n  .hover\\\\:opacity-80:hover {\\n  opacity: 0.8;\\n}\\r\\n  .focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n  .focus\\\\:outline-2:focus {\\n  outline-width: 2px;\\n}\\r\\n  .focus\\\\:\\\\!outline-skin-primary:focus {\\n  outline-color: rgb(var(--color-primary)) !important;\\n}\\r\\n  .focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n  .focus\\\\:ring-4:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n  .focus\\\\:ring-blue-300:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity));\\n}\\r\\n  .focus\\\\:ring-blue-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));\\n}\\r\\n  .focus\\\\:ring-indigo-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity));\\n}\\r\\n  .focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n  .disabled\\\\:bg-\\\\[\\\\#F6F3F3\\\\]:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(246 243 243 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:bg-blue-400:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:bg-gray-100:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:bg-gray-400:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:bg-neutral-100:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(245 245 245 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:bg-slate-600:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:text-gray-400:disabled {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity));\\n}\\r\\n  .disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\r\\n  .group:hover .group-hover\\\\:block {\\n  display: block;\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity));\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:bg-skin-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgba(var(--color-primary), var(--tw-bg-opacity));\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:after\\\\:left-\\\\[3px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 3px;\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:after\\\\:translate-x-full::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:after\\\\:border-white::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity));\\n}\\r\\n  @media (max-width: 1600px) {\\n\\n  .max-\\\\[1600px\\\\]\\\\:hidden {\\n    display: none;\\n  }\\n}\\r\\n  @media not all and (min-width: 1024px) {\\n\\n  .max-lg\\\\:gap-10 {\\n    gap: 2.5rem;\\n  }\\n}\\r\\n  @media (min-width: 765px) {\\n\\n  .md\\\\:mb-3 {\\n    margin-bottom: 0.75rem;\\n  }\\n\\n  .md\\\\:mb-6 {\\n    margin-bottom: 1.5rem;\\n  }\\n\\n  .md\\\\:ml-\\\\[15\\\\%\\\\] {\\n    margin-left: 15%;\\n  }\\n\\n  .md\\\\:ml-\\\\[55px\\\\] {\\n    margin-left: 55px;\\n  }\\n\\n  .md\\\\:mr-12 {\\n    margin-right: 3rem;\\n  }\\n\\n  .md\\\\:w-1\\\\/2 {\\n    width: 50%;\\n  }\\n\\n  .md\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .md\\\\:w-fit {\\n    width: -moz-fit-content;\\n    width: fit-content;\\n  }\\n\\n  .md\\\\:w-full {\\n    width: 100%;\\n  }\\n\\n  .md\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .md\\\\:flex-col {\\n    flex-direction: column;\\n  }\\n\\n  .md\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .md\\\\:py-2 {\\n    padding-top: 0.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .md\\\\:text-base {\\n    font-size: 1rem;\\n    line-height: 1.5rem;\\n  }\\n}\\r\\n  @media (min-width: 1024px) {\\n\\n  .lg\\\\:mb-0 {\\n    margin-bottom: 0px;\\n  }\\n\\n  .lg\\\\:mb-6 {\\n    margin-bottom: 1.5rem;\\n  }\\n\\n  .lg\\\\:ml-\\\\[60px\\\\] {\\n    margin-left: 60px;\\n  }\\n\\n  .lg\\\\:mr-14 {\\n    margin-right: 3.5rem;\\n  }\\n\\n  .lg\\\\:mt-0 {\\n    margin-top: 0px;\\n  }\\n\\n  .lg\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .lg\\\\:w-\\\\[95\\\\%\\\\] {\\n    width: 95%;\\n  }\\n\\n  .lg\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .lg\\\\:w-full {\\n    width: 100%;\\n  }\\n\\n  .lg\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .lg\\\\:py-1 {\\n    padding-top: 0.25rem;\\n    padding-bottom: 0.25rem;\\n  }\\n\\n  .lg\\\\:pl-0 {\\n    padding-left: 0px;\\n  }\\n\\n  .lg\\\\:text-sm {\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n}\\r\\n  @media (min-width: 1280px) {\\n\\n  .xl\\\\:mb-6 {\\n    margin-bottom: 1.5rem;\\n  }\\n\\n  .xl\\\\:mt-20 {\\n    margin-top: 5rem;\\n  }\\n\\n  .xl\\\\:mt-4 {\\n    margin-top: 1rem;\\n  }\\n\\n  .xl\\\\:mt-6 {\\n    margin-top: 1.5rem;\\n  }\\n\\n  .xl\\\\:block {\\n    display: block;\\n  }\\n\\n  .xl\\\\:inline {\\n    display: inline;\\n  }\\n\\n  .xl\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .xl\\\\:w-1\\\\/2 {\\n    width: 50%;\\n  }\\n\\n  .xl\\\\:w-3\\\\/5 {\\n    width: 60%;\\n  }\\n\\n  .xl\\\\:w-\\\\[100\\\\%\\\\] {\\n    width: 100%;\\n  }\\n\\n  .xl\\\\:w-\\\\[20\\\\%\\\\] {\\n    width: 20%;\\n  }\\n\\n  .xl\\\\:w-\\\\[40\\\\%\\\\] {\\n    width: 40%;\\n  }\\n\\n  .xl\\\\:w-\\\\[48\\\\%\\\\] {\\n    width: 48%;\\n  }\\n\\n  .xl\\\\:w-\\\\[65\\\\%\\\\] {\\n    width: 65%;\\n  }\\n\\n  .xl\\\\:w-\\\\[70\\\\%\\\\] {\\n    width: 70%;\\n  }\\n\\n  .xl\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .xl\\\\:gap-6 {\\n    gap: 1.5rem;\\n  }\\n\\n  .xl\\\\:border-e {\\n    border-inline-end-width: 1px;\\n  }\\n\\n  .xl\\\\:border-e-\\\\[1px\\\\] {\\n    border-inline-end-width: 1px;\\n  }\\n\\n  .xl\\\\:pe-8 {\\n    padding-inline-end: 2rem;\\n  }\\n\\n  .xl\\\\:pl-5 {\\n    padding-left: 1.25rem;\\n  }\\n\\n  .xl\\\\:ps-8 {\\n    padding-inline-start: 2rem;\\n  }\\n\\n  .xl\\\\:text-lg {\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .xl\\\\:tracking-normal {\\n    letter-spacing: 0em;\\n  }\\n}\\r\\n  @media (min-width: 1536px) {\\n\\n  .\\\\32xl\\\\:mb-0 {\\n    margin-bottom: 0px;\\n  }\\n\\n  .\\\\32xl\\\\:mb-0\\\\.5 {\\n    margin-bottom: 0.125rem;\\n  }\\n\\n  .\\\\32xl\\\\:block {\\n    display: block;\\n  }\\n\\n  .\\\\32xl\\\\:h-\\\\[calc\\\\(100\\\\%-60px\\\\)\\\\] {\\n    height: calc(100% - 60px);\\n  }\\n\\n  .\\\\32xl\\\\:h-full {\\n    height: 100%;\\n  }\\n\\n  .\\\\32xl\\\\:w-1\\\\/5 {\\n    width: 20%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[50\\\\%\\\\] {\\n    width: 50%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[55\\\\%\\\\] {\\n    width: 55%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[60\\\\%\\\\] {\\n    width: 60%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[calc\\\\(100\\\\%-70px\\\\)\\\\] {\\n    width: calc(100% - 70px);\\n  }\\n\\n  .\\\\32xl\\\\:\\\\!max-w-\\\\[70\\\\%\\\\] {\\n    max-width: 70% !important;\\n  }\\n\\n  .\\\\32xl\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .\\\\32xl\\\\:gap-3 {\\n    gap: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:p-3 {\\n    padding: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-3 {\\n    padding-left: 0.75rem;\\n    padding-right: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-3\\\\.5 {\\n    padding-left: 0.875rem;\\n    padding-right: 0.875rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-4 {\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n\\n  .\\\\32xl\\\\:py-1 {\\n    padding-top: 0.25rem;\\n    padding-bottom: 0.25rem;\\n  }\\n\\n  .\\\\32xl\\\\:py-2 {\\n    padding-top: 0.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .\\\\32xl\\\\:py-3 {\\n    padding-top: 0.75rem;\\n    padding-bottom: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:pt-1 {\\n    padding-top: 0.25rem;\\n  }\\n\\n  .\\\\32xl\\\\:pt-1\\\\.5 {\\n    padding-top: 0.375rem;\\n  }\\n\\n  .\\\\32xl\\\\:text-2xl {\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n\\n  .\\\\32xl\\\\:text-lg {\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n}\\r\\n  @media (min-width: 1600px) {\\n\\n  .min-\\\\[1600px\\\\]\\\\:w-\\\\[23\\\\%\\\\] {\\n    width: 23%;\\n  }\\n\\n  .min-\\\\[1600px\\\\]\\\\:w-\\\\[42\\\\%\\\\] {\\n    width: 42%;\\n  }\\n}\\r\\n  @media (min-width: 1610px) {\\n\\n  .min-\\\\[1610px\\\\]\\\\:inline {\\n    display: inline;\\n  }\\n\\n  .min-\\\\[1610px\\\\]\\\\:w-\\\\[35\\\\%\\\\] {\\n    width: 35%;\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\",\"<no source>\"],\"names\":[],\"mappings\":\"AAAA;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;CAAc;;AAAd;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,4NAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd;AAAc;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AACpB;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sCAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,wBAAmB;KAAnB,qBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kCAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gCAAmB;EAAnB;AAAmB;AAAnB;EAAA,gCAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0FAAmB;EAAnB,8GAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,8FAAmB;EAAnB;AAAmB;AAAnB;EAAA,oCAAmB;EAAnB,mDAAmB;EAAnB;AAAmB;AAAnB;EAAA,oCAAmB;EAAnB,mDAAmB;EAAnB;AAAmB;AAAnB;EAAA,oCAAmB;EAAnB,mDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB,+QAAmB;UAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;UAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AACnB,iCAAiC;;AAEjC;EACE,yBAAyB;EACzB,4CAA6C;AAC/C;AACA;EACE,+BAA+B;EAC/B,4CAAmD;AACrD;AACA;EACE,wBAAwB;EACxB,4CAA4C;AAC9C;AACA;EACE,8BAA8B;EAC9B,4CAAkD;AACpD;AACA;EACE,6BAA6B;EAC7B,4CAAiD;AACnD;AACA;EACE,mCAAmC;EACnC,4CAAuD;AACzD;AACA;EACE,8BAA8B;EAC9B,4CAAkD;AACpD;AACA;EACE,oCAAoC;EACpC,4CAAwD;AAC1D;AACA;EACE,0BAA0B;EAC1B,4CAA8C;AAChD;AACA;EACE,yBAAyB;EACzB,4CAA6C;AAC/C;AACA;EACE,+BAA+B;EAC/B,6CAAmD;AACrD;AACA;EACE,0BAA0B;EAC1B,6CAA8C;AAChD;AACA;EACE,gCAAgC;EAChC,6CAAoD;AACtD;AACA;EACE,2BAA2B;EAC3B,6CAA+C;AACjD;AACA;EACE,4BAA4B;EAC5B,6CAAgD;AAClD;AACA;EACE,kCAAkC;EAClC,6CAAsD;AACxD;AACA;EACE,0BAA0B;EAC1B,4CAA8C;AAChD;AACA;EACE,0BAA0B;EAC1B,4CAA8C;AAChD;;AAEA;;EAEE,YAAY;EACZ,yBAAyB;EACzB,6BAA6B;EAC7B,kBAAkB;EAClB,yBAAyB;EACzB,eAAe;AACjB;;AAEA;;EAEE,YAAY;EACZ,yBAAyB;EACzB,wCAAwC;EACxC,kBAAkB;EAClB,yBAAyB;EACzB,eAAe;AACjB;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;EACE,eAAe;EACf,cAAc;EACd,4BAA4B;AAC9B;;AAEA;EACE,YAAY;EACZ,mBAAmB;AACrB;;AAEA;EACE,eAAe;EACf,WAAW;EACX,4BAA4B;AAC9B;;AAEA;EACE,cAAc;EACd,6BAA6B;EAC7B,eAAe;AACjB;;AAEA;EACE,YAAY;EACZ,6BAA6B;EAC7B,eAAe;EACf,yBAAyB;AAC3B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;AACjB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,6BAA6B;EAC7B,eAAe;AACjB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,6BAA6B;EAC7B,eAAe;AACjB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,wBAAwB;AAC1B;AACA;EACE,wBAAwB;EACxB,YAAY;EACZ,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,yBAAyB;EACzB,6BAA6B;AAC/B;;AAEA;;;;EAIE,yBAAyB;EACzB,6BAA6B;AAC/B;;AAEA;EACE,yBAAyB;EACzB,6BAA6B;AAC/B;;AAEA;EACE,YAAY;EACZ,6BAA6B;AAC/B;AACA;;EAEE,6BAA6B;EAC7B,YAAY;AACd;AACA;;EAEE,6BAA6B;EAC7B,WAAW;AACb;AACA;EACE,6BAA6B;EAC7B,YAAY;AACd;;AAEA;EACE,aAAa;AACf;;;AAGA;EACE,6BAA6B;EAC7B,eAAe;AACjB;;AAEA;;EAEE,WAAW;EACX,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE,mBAAmB;EACnB,eAAe;EACf,kBAAkB;AACpB;;AAEA;;;EAGE,WAAW;EACX,yBAAyB;EACzB,8BAA8B;EAC9B,iBAAiB;EACjB,oBAAoB;EACpB,aAAa;EACb,sBAAsB;EACtB,uBAAuB;AACzB;;AAEA;;EAEE,WAAW;EACX,2BAA2B;EAC3B,iBAAiB;EACjB,oBAAoB;AACtB;;AAEA;EACE,cAAc;EACd,iBAAiB;EACjB,oBAAoB;EACpB,aAAa;EACb,sBAAsB;EACtB,uBAAuB;EACvB,qBAAqB;AACvB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,cAAc;EACd,eAAe;EACf,0BAA0B;EAC1B,iBAAiB;EACjB,wBAAwB;EACxB,SAAS;EACT,UAAU;EACV,gBAAgB;AAClB;;AAEA;EACE,eAAe;EACf,2BAA2B;AAC7B;;AAEA,kEAAkE;;AAElE;EACE,UAAU;AACZ;;AAEA;EACE,eAAe;EACf,sBAAsB;EACtB,WAAW;EACX,WAAW;EACX,+BAA+B;EAC/B,kBAAkB;EAClB,aAAa;EACb,uBAAuB;EACvB,mBAAmB;AACrB;;AAEA;EACE,WAAW;EACX,eAAe;EACf,kBAAkB;EAClB,WAAW;EACX,MAAM;EACN,kBAAkB;AACpB;;AAEA;EACE,eAAe;EACf,WAAW;EACX,gBAAgB;EAChB,aAAa;EACb,gBAAgB;AAClB;;AAEA;;EAEE,6BAA6B;AAC/B;;AAEA;EACE,uBAAuB;EACvB,aAAa;EACb,gBAAgB;EAChB,oCAAoC;AACtC;;AAEA;;;;;;;;GAQG;AACH;;GAEG;AACH;EACE,cAAc;AAChB;AACA;;;GAGG;AACH;EACE,uBAAuB;AACzB;AACA;EACE,sBAAsB;EACtB,YAAY;AACd;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,6BAA6B;KAC1B,0BAA0B;UACrB,qBAAqB;AAC/B;AACA;;EAEE,wBAAwB;AAC1B;;AAEA;;;;;;EAME,gBAAgB;EAChB,mBAAmB;EACnB,qBAAqB;EACrB,0BAA0B;EAC1B,cAAc;EACd,6BAA6B;AAC/B;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,0BAA0B;EAC1B,4BAA4B;AAC9B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,cAAc;AAChB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,cAAc;EACd,yBAAyB;AAC3B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,cAAc;EACd,yBAAyB;EACzB,sBAAsB;AACxB;;AAEA;EACE,yBAAyB;AAC3B;AACA;EACE,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;EACzB,YAAY;AACd;;AAEA;EACE,qBAAqB;AACvB;AACA;EACE,qBAAqB;AACvB;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;;AAEA;EACE,yBAAyB;AAC3B;AACA;EACE,yBAAyB;AAC3B;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;;AAEA;EACE,eAAe;EACf,6BAA6B;AAC/B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;IACI,eAAe;IACf,cAAc;IACd,4BAA4B;AAChC;;AAEA;EACE,8BAA8B;EAC9B,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,8BAA8B;EAC9B,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,eAAe;EACf,6BAA6B;AAC/B;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,uBAAuB;EACvB,8BAA8B;AAChC;;AAEA;EACE,YAAY;EACZ,kBAAkB;EAClB,uBAAuB;AACzB;;AAEA;EACE,+BAA+B;AACjC;;AAEA;EACE,kBAAkB;EAClB,YAAY;EACZ,UAAU;AACZ;;AAEA;EACE,yBAAyB;EACzB,kBAAkB;AACpB;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;IACI,4BAA4B;AAChC;;AAEA;IACI,4BAA4B;AAChC;;AAEA;IACI,4BAA4B;AAChC;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,mBAAmB;AACrB;;AAEA;EACE,aAAa;EACb,mBAAmB;AACrB;;AAEA;;;;GAIG;;AAEH;IACI,mBAAmB;IACnB,aAAa;AACjB;;AAEA;EACE;IACE,kBAAkB;IAClB,wBAAwB;IACxB,iBAAiB;IACjB,YAAY;EACd;EACA;IACE,kBAAkB;EACpB;EACA;;IAEE,gBAAgB;EAClB;EACA;;IAEE,kBAAkB;IAClB,WAAW;IACX,gBAAgB;EAClB;EACA;;KAEG;;EAEH;IACE,eAAe;EACjB;EACA;IACE,gBAAgB;IAChB,WAAW;IACX,WAAW;IACX,aAAa;IACb,6BAA6B;IAC7B,SAAS;IAET,gBAAgB;EAClB;EACA;;IAEE,oBAAoB;EACtB;EACA;IACE,WAAW;EACb;EACA;IACE,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,eAAe;IACf,QAAQ;IACR,aAAa;IACb,uBAAuB;EACzB;EACA;IACE,WAAW;EACb;;EAEA;IACE,UAAU;EACZ;AACF;;;AAGA;;AAEA;;AAEA;;;;;;;;;;GAUG;;AAEH;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,oCAAoC,EAAE,2BAA2B;EACjE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,aAAa,EAAE,wCAAwC;AACzD;AACA;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,wCAAwC,EAAE,2BAA2B;EACrE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,aAAa,EAAE,wCAAwC;AACzD;;AAEA;EACE,kBAAkB;EAClB,YAAY;AACd;;AAEA;EACE,eAAe;EACf,mBAAmB;AACrB;;AAEA;EACE,eAAe;AACjB;AACA,uBAAuB;;CAEtB;EACC,yBAAyB;EACzB,wBAAwB;EACxB,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA,uBAAuB;CACtB;EACC,yBAAyB;EACzB,wBAAwB;EACxB,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;CACC;EACC,yBAAyB;EACzB,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,8BAA8B;EAC9B,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,8BAA8B;EAC9B,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,8BAA8B;EAC9B,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;;AAEA;;GAEG,yBAAyB;GACzB,iBAAiB;GACjB,4BAA4B;AAC/B;AACA;;EAEE,wBAAwB;EACxB,SAAS;AACX;;;AAGA;EACE,YAAY;EACZ,kBAAkB;EAClB,wBAAwB,EAAE,eAAe;EACzC,YAAY;EACZ,UAAU;EACV,eAAe;EACf,sBAAsB;EAEtB,6BAA6B;AAC/B;AACA;;GAEG;AACH;EACE,uBAAuB;AACzB;AACA;;GAEG;AACH;EACE,WAAW;EACX,YAAY;AACd;AACA;EACE,YAAY;EACZ,sBAAsB;EACtB,gBAAgB;EAChB,SAAS;EACT,UAAU;EACV,uBAAuB;EACvB,eAAe;EACf,6BAA6B;AAC/B;AACA;EACE,eAAe;EACf,KAAK;EACL,MAAM;EACN,kBAAkB;EAClB,UAAU;AACZ;;AAEA;EACE,cAAc;EACd,mBAAmB;EACnB,SAAS;EACT,iBAAiB;AACnB;AACA;EACE,kBAAkB;EAClB,QAAQ;AACV;AACA;IACI,YAAY;AAChB;AACA;EACE,4BAA4B;EAC5B,6BAA6B;EAC7B,iCAAiC;AACnC;AACA;EACE,qBAAqB;AACvB;;AAEA;EACE,oCAAoC;EACpC,qBAAqB;AACvB;AACA;EACE,oCAAoC;EACpC,UAAU;EACV,gCAAgC;EAChC,+BAA+B;AACjC;AACA;EACE,oCAAoC;EACpC,UAAU;EACV,gCAAgC;EAChC,+BAA+B;EAC/B,8BAA8B;AAChC;AACA;EACE,oCAAoC;EACpC,UAAU;EACV,gCAAgC;EAChC,8BAA8B;AAChC;AACA;IACI,+BAA+B;IAC/B,gCAAgC;IAChC,mBAAmB;AACvB;;AAEA;EACE,yBAAyB;EACzB,UAAU;EACV,UAAU;EACV,eAAe;EACf,uBAAuB;AACzB;AACA;EACE,sBAAsB;EACtB,UAAU;EACV,gBAAgB;EAChB,UAAU;EACV,uBAAuB;EACvB,0BAA0B;AAC5B;AACA;EACE,yBAAyB;AAC3B;;AAEA;EACE,aAAa;GACZ,wBAAwB;AAC3B;;AAEA;EACE,cAAc;GACb,wBAAwB;AAC3B;;AAEA;EACE,yBAAyB;AAC3B;AACA;EACE,wBAAwB;EACxB;AACF;AACA;EACE,cAAc;EACd,gCAAgC;AAClC;AACA;EACE,4BAA4B;;AAE9B;;AAEA,kBAAkB;;AAElB;GACG,yBAAyB;GACzB,iBAAiB;AACpB;;AAEA;EACE,YAAY;EACZ,gBAAgB;EAChB,wBAAwB,EAAE,eAAe;EACzC,YAAY;EACZ,UAAU;EACV,eAAe;EACf,sBAAsB;EACtB,mBAAmB;EACnB,kBAAkB;EAClB,6BAA6B;AAC/B;;AAEA;;GAEG;AACH;EACE,kBAAkB;EAClB,eAAe;EACf,OAAO;AACT;AACA;EACE,eAAe;EACf,eAAe;EACf,OAAO;AACT;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,sBAAsB;EACtB,WAAW;EACX,UAAU;EACV,kBAAkB;EAClB,6BAA6B;AAC/B;AACA;EACE,YAAY;EACZ,sBAAsB;EACtB,sBAAsB;EACtB,gBAAgB;EAChB,SAAS;EACT,UAAU;EACV,uBAAuB;EACvB,eAAe;EACf,6BAA6B;AAC/B;AACA;EACE,eAAe;EACf,KAAK;EACL,MAAM;EACN,wBAAwB;EACxB,UAAU;AACZ;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,YAAY;EACZ,gBAAgB;EAChB,4BAA4B;AAC9B;AALA;EACE,yBAAyB;EACzB,YAAY;EACZ,gBAAgB;EAChB,4BAA4B;AAC9B;AACA;EACE,oBAAoB;EACpB,4BAA4B;EAC5B,qBAAqB;EACrB,gBAAgB;EAChB,uBAAuB;EACvB;;EAEA;IACE,yBAAyB;IACzB,cAAc;IACd,wBAAwB;IACxB,gBAAgB;EAClB;EACA;IACE,aAAa;EACf;EAz+BF;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,sBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,uBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,mBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,yBCAA;EDAA,yDCAA;EDAA;CCAA;EDAA;EAAA,iBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,uBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,+BCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,4GCAA;EDAA,0GCAA;EDAA;CCAA;EDAA;EAAA,4GCAA;EDAA,0GCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,uBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,uBCAA;EDAA;CCAA;EDAA;;EAAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,wBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;;EDAA;IAAA,gBCAA;IDAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,sBCAA;IDAA;GCAA;;EDAA;IAAA,uBCAA;IDAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,kBCAA;IDAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;CAAA\",\"sourcesContent\":[\"@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n/*@import \\\"bootstrap/bootstrap\\\";*/\\r\\n\\r\\n@font-face {\\r\\n  font-family: poppinsblack;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Black.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsblackitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-BlackItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbold;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Bold.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbolditalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-BoldItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabold;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraBold.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabolditalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraBoldItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralight;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraLight.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralightitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraLightItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Italic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslight;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Light.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslightitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-LightItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmedium;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Medium.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmediumitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-MediumItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsregular;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Regular.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibold;\\r\\n  src: url(\\\"../assets/fonts/Poppins-SemiBold.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibolditalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-SemiBoldItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Italic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Italic.ttf\\\");\\r\\n}\\r\\n\\r\\nbody,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  overflow-x: hidden;\\r\\n  background-color: #f3f8ff;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark body,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\" !important;\\r\\n  overflow-x: hidden;\\r\\n  background-color: #0e0e10;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.ag-header .ag-header-cell.header-with-border  {\\r\\n  border-bottom: 1px solid #ccc;\\r\\n}\\r\\n\\r\\n.ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\nbutton[disabled] {\\r\\n  opacity: 0.5;\\r\\n  cursor: not-allowed;\\r\\n}\\r\\n\\r\\n.dark .ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #fff;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell {\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark .ag-ltr .ag-cell {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-panel {\\r\\n  justify-content: between;\\r\\n}\\r\\n.dark .ag-paging-panel {\\r\\n  justify-content: between;\\r\\n  color: white;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.dark .ag-center-cols-viewport {\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.contentsectionbg {\\r\\n  background-color: white;\\r\\n}\\r\\n\\r\\n.dark .contentsectionbg {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark\\r\\n  .ag-header.ag-header-allow-overflow\\r\\n  .ag-header-row\\r\\n  .ag-root-wrapper.ag-layout-normal {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .ag-header-container {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .pagination-style {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.dark .ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n.ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: #000;\\r\\n}\\r\\n.dark .ag-paging-button.ag-disabled {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-body-horizontal-scroll-viewport {\\r\\n  display: none;\\r\\n} \\r\\n\\r\\n\\r\\n.ag-overlay-no-rows-center {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:hover,\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:focus {\\r\\n  color: #ccc;\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\nnav:hover .forAnimate {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n.mainmenu {\\r\\n  background: #002d73;\\r\\n  border: #002d73;\\r\\n  border-radius: 0px;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > .active > a,\\r\\n.mainmenu .navbar-nav > .active > a:hover,\\r\\n.mainmenu .navbar-nav > .active > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: #2a3344;\\r\\n  border-left: 2px solid #1ca9c0;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > li > a:hover,\\r\\n.mainmenu .navbar-nav > li > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: lightgray;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n}\\r\\n\\r\\n.navbar-default .navbar-nav > li > a {\\r\\n  color: #00b1a3;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n  text-decoration: none;\\r\\n}\\r\\n\\r\\n.navbar {\\r\\n  min-height: 45px;\\r\\n}\\r\\n\\r\\n.page-heading {\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n  margin-top: 0px !important;\\r\\n  margin-left: 25px;\\r\\n  /* position: absolute; */\\r\\n  top: 10px;\\r\\n  left: 90px;\\r\\n  font-weight: 600;\\r\\n}\\r\\n\\r\\n.page-heading h2 {\\r\\n  font-size: 16px;\\r\\n  margin-top: 15px !important;\\r\\n}\\r\\n\\r\\n/* ----------- Notification dropdown end ----------------------- */\\r\\n\\r\\nnav.sidebar .brand a {\\r\\n  padding: 0;\\r\\n}\\r\\n\\r\\n.brand {\\r\\n  font-size: 24px;\\r\\n  /* padding: 0px 5px; */\\r\\n  color: #fff;\\r\\n  width: 69px;\\r\\n  /* background-color: #002d73; */\\r\\n  text-align: center;\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.titlebar {\\r\\n  width: 100%;\\r\\n  position: fixed;\\r\\n  /* height: 55px; */\\r\\n  z-index: 98;\\r\\n  top: 0;\\r\\n  padding-left: 69px;\\r\\n}\\r\\n\\r\\n.accordion {\\r\\n  cursor: pointer;\\r\\n  width: 100%;\\r\\n  text-align: left;\\r\\n  outline: none;\\r\\n  transition: 0.4s;\\r\\n}\\r\\n\\r\\n.active,\\r\\n.accordion:hover {\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\n.panel {\\r\\n  background-color: white;\\r\\n  max-height: 0;\\r\\n  overflow: hidden;\\r\\n  transition: max-height 0.2s ease-out;\\r\\n}\\r\\n\\r\\n/* .reactSelectCustom .css-1fdsijx-ValueContainer , .reactSelectCustom .css-b62m3t-ValueContainer {\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n}\\r\\n\\r\\n.reactSelectCustom .css-1hb7zxy-IndicatorsContainer, .reactSelectCustom .css-1xc3v61-IndicatorsContainer{\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n} */\\r\\n/* .reactSelectCustom .css-1jgx7bw-control{\\r\\n  flex-wrap: nowrap !important;\\r\\n} */\\r\\ninput[type=date]:invalid::-ms-datetime-edit {\\r\\n  color: #808080;\\r\\n}\\r\\n/* label {\\r\\n  font-size: 14px;\\r\\n  color: #505050;\\r\\n} */\\r\\n.top-navbar {\\r\\n  background-color: white;\\r\\n}\\r\\n.dark .top-navbar {\\r\\n  background-color: #000;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.dark .pageName {\\r\\n  color: white;\\r\\n}\\r\\n\\r\\ninput[type=\\\"number\\\"] {\\r\\n  -webkit-appearance: textfield;\\r\\n     -moz-appearance: textfield;\\r\\n          appearance: textfield;\\r\\n}\\r\\ninput[type=number]::-webkit-inner-spin-button, \\r\\ninput[type=number]::-webkit-outer-spin-button { \\r\\n  -webkit-appearance: none;\\r\\n}\\r\\n\\r\\ninput[type=\\\"text\\\"],\\r\\ninput[type=\\\"tel\\\"],\\r\\ninput[type=\\\"date\\\"],\\r\\ninput[type=\\\"email\\\"],\\r\\ninput[type=\\\"number\\\"],\\r\\nselect {\\r\\n  padding-top: 4px;\\r\\n  padding-bottom: 4px;\\r\\n  border-color: #d6d6d6;\\r\\n  outline: 2px solid #ffffff;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\ninput:focus, select:focus , textarea:focus{\\r\\n  outline-color: #0066ff;\\r\\n}\\r\\n\\r\\n.button {\\r\\n  padding: 4px 12px 4px 12px;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #505050;\\r\\n}\\r\\n.dark .labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #AFAFAF;\\r\\n  background-color: #FFFFFF;\\r\\n}\\r\\n\\r\\n.dark .searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #c0c0c0;\\r\\n  background-color: #1d212d;\\r\\n  outline-color: #1d212d;\\r\\n}\\r\\n\\r\\n.checkboxbg {\\r\\n  background-color: #D9D9D9;\\r\\n}\\r\\n.dark .checkboxbg {\\r\\n  background-color: #4d4d4d;\\r\\n}\\r\\n\\r\\n.dark select {\\r\\n  background-color: #1d1d1d;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.borderc-theme-blue2 {\\r\\n  border-color: #0066ff;\\r\\n}\\r\\n.dark .borderc-theme-blue2 {\\r\\n  border-color: #6699ff;\\r\\n}\\r\\n.textc-theme-blue2 {\\r\\n  color: #0066ff;\\r\\n}\\r\\n.dark .textc-theme-blue2 {\\r\\n  color: #6699ff;\\r\\n}\\r\\n\\r\\n.bgc-theme-blue2 {\\r\\n  background-color: #0066ff;\\r\\n}\\r\\n.dark .bgc-theme-blue2 {\\r\\n  background-color: #6699ff;\\r\\n}\\r\\n.textc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .textc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n.borderc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .borderc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n\\r\\n.input {\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .inputs {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.confirmInputs {\\r\\n    font-size: 12px;\\r\\n    color: #333333;\\r\\n    font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.formtitle {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.subtitles {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.text-blackcolor {\\r\\n  color:#505050;\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within {\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-row-odd{\\r\\n  background-color: #f3f3f3 !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value span {\\r\\n  height: auto !important;\\r\\n  line-height: normal !important;\\r\\n}\\r\\n\\r\\n.ag-cell{\\r\\n  display:flex;\\r\\n  align-items:center;\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value, .ag-header-cell-text{\\r\\n  text-overflow: unset !important;\\r\\n}\\r\\n\\r\\n.pagination-style {\\r\\n  position: absolute;\\r\\n  bottom: 10px;\\r\\n  left: 20px;\\r\\n}\\r\\n\\r\\n.pagination-style select {\\r\\n  background-color: #f2f2f2;\\r\\n  border-radius: 3px;\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-header-select-all{\\r\\n  margin-right: 12px !important; \\r\\n}\\r\\n\\r\\n.ag-root-wrapper.ag-layout-normal{\\r\\n  border-radius: 5px !important;\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-row.ag-header-row-column {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-center-cols-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.general_section .ag-header-container, .general_section .ag-center-cols-container, .general_section .ag-header-row  {\\r\\n  width: 100% !important;\\r\\n}\\r\\n\\r\\n.general_section .ag-cell {\\r\\n  /* display: flex; */\\r\\n  margin-right: 10px;\\r\\n}\\r\\n\\r\\n.product_link_def .ag-row .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.product_data_def .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n/* @media (min-width: 640px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 10px;\\r\\n  }\\r\\n} */\\r\\n\\r\\n.viewlog .ag-cell-value {\\r\\n    align-items: center;\\r\\n    display: flex;\\r\\n}\\r\\n\\r\\n@media (min-width: 765px) {\\r\\n  .main {\\r\\n    position: absolute;\\r\\n    width: calc(100% - 40px);\\r\\n    margin-left: 40px;\\r\\n    float: right;\\r\\n  }\\r\\n  nav.sidebar:hover + .main {\\r\\n    margin-left: 200px;\\r\\n  }\\r\\n  nav.sidebar.navbar.sidebar > .container .navbar-brand,\\r\\n  .navbar > .container-fluid .navbar-brand {\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  nav.sidebar .navbar-brand,\\r\\n  nav.sidebar .navbar-header {\\r\\n    text-align: center;\\r\\n    width: 100%;\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  /* nav.sidebar a {\\r\\n    padding-bottom: 34px;\\r\\n  } */\\r\\n\\r\\n  nav.sidebar .navbar-nav > li {\\r\\n    font-size: 13px;\\r\\n  }\\r\\n  nav.sidebar .navbar-nav .open .dropdown-menu {\\r\\n    position: static;\\r\\n    float: none;\\r\\n    width: auto;\\r\\n    margin-top: 0;\\r\\n    background-color: transparent;\\r\\n    border: 0;\\r\\n    -webkit-box-shadow: none;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  nav.sidebar .navbar-collapse,\\r\\n  nav.sidebar .container-fluid {\\r\\n    padding: 0 0px 0 0px;\\r\\n  }\\r\\n  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {\\r\\n    color: #777;\\r\\n  }\\r\\n  nav.sidebar {\\r\\n    width: 69px;\\r\\n    height: 100%;\\r\\n    margin-bottom: 0px;\\r\\n    position: fixed;\\r\\n    top: 0px;\\r\\n    display: flex;\\r\\n    align-items: flex-start;\\r\\n  }\\r\\n  nav.sidebar li {\\r\\n    width: 100%;\\r\\n  }\\r\\n\\r\\n  .forAnimate {\\r\\n    opacity: 0;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n@media (min-width: 1024px) {\\r\\n  \\r\\n}\\r\\n\\r\\n/* @media (min-width: 1280px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 14px;\\r\\n  }}\\r\\n\\r\\n@media (min-width: 1536px) {\\r\\n  \\r\\n}\\r\\n@media (min-width: 1940px) {\\r\\n  \\r\\n} */\\r\\n\\r\\n.desktop-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n.block-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(214, 21, 21, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n\\r\\n.message-content {\\r\\n  text-align: center;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.message-content h2 {\\r\\n  font-size: 24px;\\r\\n  margin-bottom: 10px;\\r\\n}\\r\\n\\r\\n.message-content p {\\r\\n  font-size: 16px;\\r\\n}\\r\\n/* quarter filter css */\\r\\n\\r\\n #q1:checked  + .labelcheck, #q2:checked  + .labelcheck, #q3:checked  + .labelcheck, #q4:checked  + .labelcheck, #all:checked  + .labelcheck, #needsupdate:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* section filter css */\\r\\n #volume:checked  + .labelcheck, #breakeven:checked  + .labelcheck, #unitprice:checked  + .labelcheck, #grossprofit:checked  + .labelcheck, #gppercent:checked  + .labelcheck, #value:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n #bestatus-1:checked  + .labelcheck {\\r\\n  background-color: #FF9A03;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-2:checked  + .labelcheck{\\r\\n  background-color: #33CA7F;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#444;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-3:checked  + .labelcheck{\\r\\n  background-color: #FFDF37;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-4:checked  + .labelcheck{\\r\\n  background-color: #FB4646;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.planningtoolgrid\\r\\n{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n   /* border: 1px solid #ddd; */\\r\\n}\\r\\n.input-number::-webkit-inner-spin-button,\\r\\n.input-number::-webkit-outer-spin-button {\\r\\n  -webkit-appearance: none;\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n\\r\\n.planningtoolgrid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: center;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* .planningtoolgrid thead tr, .planningtoolgrid tbody tr{\\r\\n  border: 1px solid #ddd;\\r\\n} */\\r\\n.planningtoolgrid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n}\\r\\n/* .planningtoolgrid tbody tr{\\r\\n height:70px !important;\\r\\n} */\\r\\n.planningtoolgrid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n}\\r\\n.planningtoolgrid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.planningtoolgrid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  background:#f3f8ff;\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.quartertotals{\\r\\n  /* border:0; */\\r\\n  table-layout: fixed;\\r\\n  width:50%;\\r\\n  text-align:center;\\r\\n}\\r\\n.quartertotals thead th, .quartertotals tbody td{\\r\\n  text-align: center;\\r\\n  border:0;\\r\\n}\\r\\n.quartertotals thead tr, .quartertotals tbody tr {\\r\\n    border: none;\\r\\n}\\r\\n.quartertotals thead th{\\r\\n  border-top-left-radius: 20px;\\r\\n  border-top-right-radius: 20px;\\r\\n  background-color: #fff !important;\\r\\n}\\r\\n.quartertotals thead{\\r\\n  z-index: 5 !important;\\r\\n}\\r\\n\\r\\n.quartertotals thead th:first-child, .quartertotals tbody td:first-child{\\r\\n  background-color: #f3f8ff !important;\\r\\n  width:50px !important;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(2){\\r\\n  background-color: #201E50 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #504F66;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(3){\\r\\n  background-color: #D499B9 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #C0749E;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(4){\\r\\n  background-color: #EE6C4D !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #CE4B2C;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody tr:last-child td{\\r\\n    border-bottom-left-radius: 15px;\\r\\n    border-bottom-right-radius: 15px;\\r\\n    border-bottom: none;\\r\\n}\\r\\n\\r\\n.titlerow{\\r\\n  background-color: #00E0D5;\\r\\n  color:#fff;\\r\\n  z-index: 9;\\r\\n  font-size: 15px;\\r\\n  height: 28px !important;\\r\\n}\\r\\ntd.sectionrow,th.sectionrow{\\r\\n  background-color: #DDD;\\r\\n  color:#444;\\r\\n  font-weight: 600;\\r\\n  z-index: 9;\\r\\n  height: 28px !important;\\r\\n  font-size: 13px !important;\\r\\n}\\r\\ninput[type=\\\"text\\\"]:disabled, input[type=\\\"number\\\"]:disabled, select:disabled{\\r\\n  background-color: #F6F3F3;\\r\\n}\\r\\n\\r\\n#wrapper.closed .list {\\r\\n  display: none;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n#wrapper.open .list {\\r\\n  display: block;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n.bg-currentWeek{\\r\\n  background-color: #fcf6b1;\\r\\n}\\r\\n.nodata{\\r\\n  background-color:#ffecd4;\\r\\n  color:#b31818\\r\\n}\\r\\n.selected{\\r\\n  color: #0066FF;\\r\\n  border-bottom: 3px solid #0066FF;\\r\\n}\\r\\n.buttonText{\\r\\n  font-family: 'poppinsmedium';\\r\\n  \\r\\n}\\r\\n\\r\\n/*--- SLP CSS ---*/\\r\\n\\r\\n.service-level-grid{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n}\\r\\n\\r\\n.service-level-grid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: left;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  white-space: normal;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n/* .service-level-grid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n} */\\r\\n.service-level-grid thead tr th:last-child{\\r\\n  background:#f3f8ff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n.service-level-grid tbody tr td:last-child{\\r\\n  background:#fff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n\\r\\n.service-level-grid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n  border: 1px solid #ddd;\\r\\n  padding:5px;\\r\\n  z-index: 5;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  background-color:white;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  /* background:#f3f8ff; */\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.ag-grid-checkbox-cell .ag-checkbox-input {\\r\\n  cursor: pointer;\\r\\n}\\r\\n\\r\\n.depotdaterange .rdrDateDisplayWrapper{\\r\\n  display:none;\\r\\n}\\r\\n\\r\\nlabel{\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\nselect::placeholder, .css-1jqq78o-placeholder, .placeholdertext {\\r\\n  color: #333333 !important;\\r\\n  opacity: 0.5;\\r\\n  font-weight: 500;\\r\\n  font-family:\\\"poppinsregular\\\";\\r\\n}\\r\\n.text-truncate2L {\\r\\n  display: -webkit-box;\\r\\n  -webkit-box-orient: vertical;\\r\\n  -webkit-line-clamp: 2;\\r\\n  overflow: hidden;\\r\\n  text-overflow: ellipsis;\\r\\n  }\\r\\n\\r\\n  .variety-disabled-block{\\r\\n    background-color: #f6f6f6;\\r\\n    color: #888888;\\r\\n    border:1px solid #E2E2E2;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  .variety-disabled-block h4, .variety-disabled-block label{\\r\\n    color:#797979;\\r\\n  }\",null],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[2]!./styles/globals.css\n"));

/***/ })

});
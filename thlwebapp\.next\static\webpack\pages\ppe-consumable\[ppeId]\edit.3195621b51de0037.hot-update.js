"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable/[ppeId]/edit",{

/***/ "./components/PpeConsumable.js":
/*!*************************************!*\
  !*** ./components/PpeConsumable.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fortawesome/free-regular-svg-icons */ \"./node_modules/@fortawesome/free-regular-svg-icons/index.mjs\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lodash/forEach */ \"./node_modules/lodash/forEach.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(lodash_forEach__WEBPACK_IMPORTED_MODULE_12__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    valueContainer: (provided)=>({\n            ...provided,\n            height: \"28px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: ()=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst emptyItem = {\n    product: null,\n    size: null,\n    quantity: \"\",\n    nameForPrinting: \"\",\n    comments: \"\"\n};\nconst PpeConsumable = (param)=>{\n    let { userData, pageType, sites, OrderRequestData, ppeId = null } = param;\n    var _currentItem_product, _currentItem_product_sizes, _currentItem_product1, _currentItem_product2, _currentItem_product3, _currentItem_product_sizes1, _currentItem_product4, _currentItem_product5;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [nameOfOriginator, setNameOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [emailOfOriginator, setEmailOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [siteData, setSiteData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            ...emptyItem\n        }\n    ]);\n    const [requiredDate, setRequiredDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [createdDate, setCreatedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [site, setSite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((sites === null || sites === void 0 ? void 0 : sites[0]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [productsData, setProductsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [currentItem, setCurrentItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        ...emptyItem\n    });\n    const [savedItems, setSavedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // console.log(\"dsksldibiasdbilsdbv\", siteData);\n    // Add validation states for current item\n    // const [productValid, setProductValid] = useState(\"\");\n    // const [quantityValid, setQuantityValid] = useState(\"\");\n    const [selectedRowData, setSelectedRowData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dateWarning, setDateWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // const [isEditMode, setIsEditMode] = useState();\n    const [requestStatus, setRequestStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const [requestStatusList, setRequestStatusList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [submitted, setSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popupType, setPopupType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [editingRowId, setEditingRowId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gridApi, setGridApi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const closeModal = (e)=>{\n        if (e) {\n            e.preventDefault();\n        }\n        setIsValidCancelReason(true);\n        setCancelledReasonapi(\"\");\n        setIsOpen(false);\n    };\n    //#region Load data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n        if (OrderRequestData || (OrderRequestData === null || OrderRequestData === void 0 ? void 0 : OrderRequestData.length) > 0) {\n            var _OrderRequestData_, _OrderRequestData_1, _OrderRequestData_2, _OrderRequestData_3;\n            // console.log(\"OrderRequestData\", OrderRequestData);\n            if ((_OrderRequestData_ = OrderRequestData[0]) === null || _OrderRequestData_ === void 0 ? void 0 : _OrderRequestData_.action_id) {\n                var _OrderRequestData_4, _OrderRequestData_5;\n                setRequestStatus((_OrderRequestData_4 = OrderRequestData[0]) === null || _OrderRequestData_4 === void 0 ? void 0 : _OrderRequestData_4.action_id);\n                if (((_OrderRequestData_5 = OrderRequestData[0]) === null || _OrderRequestData_5 === void 0 ? void 0 : _OrderRequestData_5.action_id) !== 5) {\n                    setSubmitted(true);\n                }\n            }\n            if ((_OrderRequestData_1 = OrderRequestData[0]) === null || _OrderRequestData_1 === void 0 ? void 0 : _OrderRequestData_1.site_id) {\n                var _OrderRequestData_6, _OrderRequestData_7;\n                setSite({\n                    label: (_OrderRequestData_6 = OrderRequestData[0]) === null || _OrderRequestData_6 === void 0 ? void 0 : _OrderRequestData_6.site_name,\n                    value: (_OrderRequestData_7 = OrderRequestData[0]) === null || _OrderRequestData_7 === void 0 ? void 0 : _OrderRequestData_7.site_id\n                });\n            }\n            if ((_OrderRequestData_2 = OrderRequestData[0]) === null || _OrderRequestData_2 === void 0 ? void 0 : _OrderRequestData_2.required_date) {\n                var _OrderRequestData_8, _OrderRequestData_9;\n                setRequiredDate(((_OrderRequestData_8 = OrderRequestData[0]) === null || _OrderRequestData_8 === void 0 ? void 0 : _OrderRequestData_8.required_date) ? new Date((_OrderRequestData_9 = OrderRequestData[0]) === null || _OrderRequestData_9 === void 0 ? void 0 : _OrderRequestData_9.required_date).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if ((_OrderRequestData_3 = OrderRequestData[0]) === null || _OrderRequestData_3 === void 0 ? void 0 : _OrderRequestData_3.created_at) {\n                var _OrderRequestData_10, _OrderRequestData_11;\n                setCreatedDate(((_OrderRequestData_10 = OrderRequestData[0]) === null || _OrderRequestData_10 === void 0 ? void 0 : _OrderRequestData_10.created_at) ? new Date((_OrderRequestData_11 = OrderRequestData[0]) === null || _OrderRequestData_11 === void 0 ? void 0 : _OrderRequestData_11.created_at).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if (OrderRequestData[0].user_name) {\n                // setNameOfOriginator();\n                setNameOfOriginator(OrderRequestData[0].user_name);\n            }\n            if (OrderRequestData[0].actioned_by_email) {\n                // setNameOfOriginator();\n                setEmailOfOriginator(OrderRequestData[0].actioned_by_email);\n            }\n            // setrowdata\n            if (OrderRequestData && Array.isArray(OrderRequestData)) {\n                console.log(\"load data\", OrderRequestData, requestStatus);\n                const STATUS_MAP = {\n                    1: \"Pending Review\",\n                    2: \"Approved\",\n                    3: \"Rejected\",\n                    4: \"Ordered\",\n                    5: \"Draft\"\n                };\n                const mappedItems = OrderRequestData.map((item)=>({\n                        id: item.order_request_items,\n                        product: {\n                            value: item.product_id,\n                            label: item.product_name\n                        },\n                        size: {\n                            value: item.size,\n                            label: item.size || \"N/A\"\n                        },\n                        quantity: item.quantity,\n                        nameForPrinting: item.name_for_printing,\n                        comments: (item.action_id === 3 ? \"Rejected Reason: \".concat(item.latest_comment) : item.comments) || \"\",\n                        updateFlag: 0,\n                        status: STATUS_MAP[item.action_id]\n                    }));\n                const uniqueValues = [\n                    ...new Set(OrderRequestData.map((item)=>item.action_id))\n                ];\n                setRequestStatusList(uniqueValues);\n                setSavedItems(mappedItems);\n                console.log(\"uniqueValues\", uniqueValues);\n            }\n        }\n    }, [\n        OrderRequestData\n    ]);\n    //#endregion Load data\n    //#region ag-grid\n    const onRowSelected = (event)=>{\n        if (event.node.isSelected()) {\n            setEditingRowId(event.data.id);\n            setCurrentItem(event.data);\n        }\n    };\n    const onGridReady = (params)=>{\n        setGridApi(params.api);\n    };\n    // inside your component\n    const getRowClass = (params)=>{\n        return params.data.id === editingRowId ? \"bg-blue-100\" : \"\";\n    };\n    const IconsRenderer = (props)=>{\n        const handleDelete = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n        };\n        const handleEdit = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            setHasUnsavedChanges(true);\n            setEditingRowId(selectedRow.id);\n            // setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n            const handleRowUpdate = (actionId)=>()=>{\n                    setSelectedRowData(selectedRow);\n                    setStatusAction(actionId);\n                    setShowStatusDialog(true);\n                };\n            // event.preventDefault();\n            // const selectedRow = props.data;\n            // setEditingRowId(selectedRow.id || props.node.id);\n            // // const updatedData = savedItems.filter((item) => item !== selectedRow);\n            // // setSavedItems(updatedData);\n            //   setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            // Populate the form with the selected row data\n            setCurrentItem({\n                id: selectedRow.id,\n                product: selectedRow.product,\n                size: selectedRow.size,\n                quantity: selectedRow.quantity,\n                nameForPrinting: selectedRow.nameForPrinting,\n                nameForPrintingFlag: selectedRow.nameForPrinting,\n                comments: selectedRow.comments,\n                updateFlag: 1\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-row gap-4 justify-center text-skin-primary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleEdit,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faPenToSquare\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDelete,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\", \" text-red-500\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faTrash\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 277,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    onClick: ()=>{\n                        setSelectedRowData(props.data);\n                        setPopupType(\"approveSingleItem\");\n                        setIsOpen(true);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faCircleCheck,\n                        className: \"\".concat(requestStatus !== 1 || !requestStatus ? \"hidden\" : \"\", \" text-green-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 279,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    onClick: ()=>{\n                        setSelectedRowData(props.data);\n                        setPopupType(\"rejectSingleItem\");\n                        setIsOpen(true);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmarkCircle,\n                        className: \"\".concat(requestStatus !== 1 || !requestStatus ? \"hidden\" : \"\", \" text-red-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    onClick: ()=>{\n                        setSelectedRowData(props.data);\n                        setPopupType(\"markSingleItemOrdered\");\n                        setIsOpen(true);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faSquareCheck,\n                        className: \"\".concat(requestStatus !== 2 || !requestStatus ? \"hidden\" : \"\", \" text-green-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Column definitions for the grid\n    const columnDefs = [\n        {\n            headerName: \"Product\",\n            field: \"product.label\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Size\",\n            field: \"size.label\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Quantity\",\n            field: \"quantity\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Name for Printing\",\n            field: \"nameForPrinting\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Comments\",\n            field: \"comments\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            hide: requestStatus === 5 || !requestStatus,\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: 1.25,\n            filterParams: {\n                values: [\n                    \"Pending Review\",\n                    \"Approved\",\n                    \"Rejected\",\n                    \"Ordered\",\n                    \"Draft\"\n                ]\n            }\n        },\n        {\n            headerName: \"Actions\",\n            cellRenderer: IconsRenderer,\n            headerClass: \"header-with-border\",\n            flex: 1\n        }\n    ];\n    //#endregion ag-grid\n    //#region api's\n    function getAllProducts() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-products\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to fetch products: \".concat(error.message));\n            throw error;\n        });\n    }\n    function getAllSites() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/sites\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        });\n    }\n    //#endregion api's\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const siteData = await getAllSites();\n                const formattedSites = siteData === null || siteData === void 0 ? void 0 : siteData.map((site)=>({\n                        value: site.id,\n                        label: site.name\n                    }));\n                setSiteData(formattedSites);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error fetching Sites\", error);\n            }\n        };\n        const fetchProducts = async ()=>{\n            try {\n                const productsJson = await getAllProducts();\n                const filterOutHiddenProducts = productsJson.filter((product)=>!product.IsProductHidden);\n                const formattedProducts = filterOutHiddenProducts === null || filterOutHiddenProducts === void 0 ? void 0 : filterOutHiddenProducts.map((product)=>({\n                        value: product.ProductId,\n                        label: product.ProductName,\n                        size_required: product.IsSizeRequired || product.size_required,\n                        sizes: product.AvailableSizes ? product.AvailableSizes.split(\", \").map((size)=>({\n                                value: size.toLowerCase().replace(/\\s+/g, \"\"),\n                                label: size\n                            })) : [],\n                        productCode: product.ProductCode,\n                        packageQuantity: product.PackageQuantity,\n                        productType: product.ProductType,\n                        name_printable: product.name_printable\n                    }));\n                setProductsData(formattedProducts);\n            } catch (error) {\n                console.error(\"Error fetching products:\", error);\n            }\n        };\n        fetchProducts();\n        fetchSites();\n        setLoading(false);\n    }, []);\n    const updateSelectedRowStatusForSingleItem = (updatedStatusID)=>{\n        var _selectedRowData_product, _selectedRowData_size;\n        // Check if selectedRowData exists\n        if (!selectedRowData) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"No item selected\");\n            return;\n        }\n        if (updatedStatusID === 3) {\n            if (cancelledReasonapi) {\n                setIsValidCancelReason(true);\n            } else {\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        console.log(\"sssssssssssssssssssssssssssssssssssss\", selectedRowData);\n        setLoading(true);\n        const apiPayload = {\n            request_no: requestId,\n            item_number: selectedRowData.item_number || \"\",\n            ProductName: ((_selectedRowData_product = selectedRowData.product) === null || _selectedRowData_product === void 0 ? void 0 : _selectedRowData_product.label) || selectedRowData.product_name,\n            Size: ((_selectedRowData_size = selectedRowData.size) === null || _selectedRowData_size === void 0 ? void 0 : _selectedRowData_size.label) || selectedRowData.size,\n            Quantity: selectedRowData.quantity,\n            NameForPrinting: selectedRowData.nameForPrinting || selectedRowData.NameForPrinting,\n            Comments: updatedStatusID === 3 ? cancelledReasonapi : selectedRowData.comments,\n            RequestItemID: selectedRowData.id,\n            commentOnUpdatingRequest: cancelledReasonapi,\n            action_id: updatedStatusID,\n            SubmitterUserID: userData.user_id,\n            SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n            orignatorEmail: (selectedRowData === null || selectedRowData === void 0 ? void 0 : selectedRowData.orignatorEmail) || (userData === null || userData === void 0 ? void 0 : userData.email),\n            cancelledReasonapi: cancelledReasonapi\n        };\n        try {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-product-request-items/\").concat(selectedRowData.id), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setCancelledReasonapi(\"\");\n                    setIsOpen(false);\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            }).catch((error)=>{\n                console.error(\"Error in submitting the order\", error);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to update status. Please try again.\");\n            }).finally(()=>{\n                setLoading(false);\n            });\n        } catch (error) {\n            console.error(\"Error in submitting the order\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to update status. Please try again.\");\n            setLoading(false);\n        }\n    };\n    //#region Validate\n    // Update the validate function to use savedItems instead of items\n    const validate = ()=>{\n        const newErrors = {};\n        if (!site) newErrors.site = \"Site is required.\";\n        if (!requiredDate) newErrors.requiredDate = \"Required date is missing.\";\n        if (!savedItems || savedItems.length === 0) {\n            newErrors.savedItems = \"At least one item must be added.\";\n        }\n        // Update state for field-level messages\n        setErrors(newErrors);\n        const hasErrors = Object.keys(newErrors).length > 0;\n        if (hasErrors) {\n            const errorMessages = Object.values(newErrors);\n            // setPopupConfig({\n            //   title: \"Validation Error\",\n            //   message: errorMessages.join(\"\\n\"),\n            //   type: \"error\",\n            // });\n            // setShowPopup(true); // trigger popup\n            const errorsLength = Object.keys(newErrors).length;\n            if (errorsLength > 1) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill all required fields.\");\n            } else if (errorsLength == 1 && newErrors.savedItems) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please add at least one item.\");\n            }\n            return false;\n        }\n        return true;\n    };\n    const handleSiteAdd = (value)=>{\n        setSite(value);\n        // Clear the error for site if value is valid\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (value) {\n                delete updated.site;\n            }\n            return updated;\n        });\n    };\n    // Add date validation function\n    const validateRequiredDate = (selectedDate)=>{\n        // setRequiredDate(selectedDate);\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (selectedDate) {\n                delete updated.requiredDate;\n            }\n            return updated;\n        });\n        if (!selectedDate) {\n            setDateWarning(\"\");\n            return;\n        }\n        const today = new Date();\n        const selected = new Date(selectedDate);\n        const twoWeeksFromNow = new Date();\n        twoWeeksFromNow.setDate(today.getDate() + 14);\n        if (selected < twoWeeksFromNow) {\n            setDateWarning(\"Notice: Less than 2 weeks lead time may affect availability.\");\n        } else {\n            setDateWarning(\"\");\n        }\n    };\n    // #region handlers\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    const handleCancelReason = (data)=>{\n        if (data) {\n            setIsValidCancelReason(true);\n        } else {\n            setIsValidCancelReason(false);\n        }\n    };\n    const handleSaveClick = (e)=>{\n        // e.preventDefault();\n        if (hasUnsavedChanges) {\n            setPopupType(\"unsavedWarning\");\n            setIsOpen(true); // your existing modal state\n        } else {\n            saveCurrentItem(e);\n        }\n    };\n    const saveCurrentItem = (e)=>{\n        var _currentItem_product, _currentItem_product_sizes, _currentItem_product1;\n        console.log(\"save order request\");\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (e) {\n                delete updated.savedItems;\n            }\n            return updated;\n        });\n        if (!e) {\n            console.log(\"returning here\");\n            setDateWarning(\"\");\n            return;\n        }\n        e.preventDefault();\n        let count = 0;\n        if (!currentItem.product) {\n            console.log(\"++ product\");\n            count++;\n        // setProductValid(\"Please select a product\");\n        } else {\n        // setProductValid(\"\");\n        }\n        if (!currentItem.quantity || currentItem.quantity < 1) {\n            console.log(\"++ quantity\");\n            count++;\n        // setQuantityValid(\"Please enter a valid quantity\");\n        } else {\n            if (currentItem.quantity > 1 && currentItem.nameForPrinting) {\n                // currentItem.nameForPrinting=\"\"\n                setCurrentItem((prev)=>({\n                        ...prev,\n                        nameForPrinting: \"\"\n                    }));\n            }\n        // setQuantityValid(\"\");\n        }\n        if (((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) && ((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) && !currentItem.size) {\n            console.log(\"++ product size\");\n            count++;\n        // Add size validation if needed\n        }\n        if (count > 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill in all mandatory fields to add items with valid quantity\");\n            return;\n        }\n        //     if (editingRowId) {\n        //         // Update existing item\n        //   const updatedItems = savedItems.map(item =>\n        //     item.id === editingRowId ? { ...currentItem, id: editingRowId } : item\n        //   );\n        //   setSavedItems(updatedItems);\n        //   setEditingRowId(null);\n        // } else {\n        //   // Add new item\n        //   setSavedItems([...savedItems, { ...currentItem, id: Date.now() }]);\n        // }\n        if (editingRowId) {\n            // Add the edited item back to savedItems\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: editingRowId,\n                    product: currentItem.product ? {\n                        ...currentItem.product\n                    } : null,\n                    size: currentItem.size ? {\n                        ...currentItem.size\n                    } : null\n                }\n            ]);\n            setEditingRowId(null);\n        } else {\n            // Add new item\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: \"row-\".concat(Date.now())\n                }\n            ]);\n        }\n        // Reset current item\n        setCurrentItem({\n            ...emptyItem\n        });\n        setEditingRowId(null);\n        setHasUnsavedChanges(false);\n    };\n    // Handle current item changes\n    const handleCurrentItemChange = (field, value)=>{\n        if (field === \"product\") {\n            // console.log(\"field-------------\",value)\n            // Reset other fields when product changes\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    size: null,\n                    quantity: \"\",\n                    nameForPrinting: \"\",\n                    nameForPrintingFlag: \"\",\n                    comments: \"\"\n                }));\n        } else if (field === \"quantity\" && value > 1) {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    nameForPrinting: \"\"\n                }));\n        } else {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value\n                }));\n        }\n    };\n    //#region Save\n    const handleSubmit = (status_ID)=>{\n        if (status_ID != 1 && !validate()) {\n            return;\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssssssssssssssssssss\",savedItems);\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        // Transform savedItems to API format\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size;\n            return {\n                requestItemId: item.requestItemId,\n                // RequestItemID: typeof item.id === 'string' && item.id.startsWith('row-') ? null : item.id,\n                ProductID: (_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value,\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            Status_id: status_ID,\n            SubmitterUserID: userData === null || userData === void 0 ? void 0 : userData.user_id,\n            TargetSiteID: (site === null || site === void 0 ? void 0 : site.value) || 3,\n            RequiredDate: requiredDate,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            items: items,\n            orignatorEmail: emailOfOriginator\n        };\n        // console.log(\"items--------------------------------------------------------\",items)\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/create-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    //#region updated conditional\n    const updateSelectedRowStatus = (updatedStatusID, existingStatusId)=>{\n        const STATUS_MAP = {\n            1: \"Pending Review\",\n            2: \"Approved\",\n            3: \"Rejected\",\n            4: \"Ordered\",\n            5: \"Draft\"\n        };\n        const toBeUpdated = savedItems.filter((item)=>item.status == STATUS_MAP[existingStatusId]);\n        if (toBeUpdated.length === 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"No items found with the specified status\");\n            return;\n        }\n        setLoading(true);\n        if (updatedStatusID == 3) {\n            if (cancelledReasonapi) {\n                setIsValidCancelReason(true);\n                console.log(\"triggerrerererered\", cancelledReasonapi);\n            } else {\n                console.log(\"triggerrerererered\");\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssss\", toBeUpdated);\n        const updatePromises = toBeUpdated.map(async (item)=>{\n            const apiPayload = {\n                request_no: item.id,\n                item_number: item.item_number,\n                ProductName: item.product.label,\n                Size: item.size.label,\n                Quantity: item.quantity,\n                NameForPrinting: item.nameForPrinting,\n                Comments: updatedStatusID == 3 ? cancelledReasonapi : item.comments,\n                RequestItemID: item.id,\n                commentOnUpdatingRequest: cancelledReasonapi,\n                action_id: updatedStatusID,\n                SubmitterUserID: userData.user_id,\n                SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n                orignatorEmail: emailOfOriginator,\n                cancelledReasonapi: cancelledReasonapi\n            };\n            console.log(\"apiPayload\", apiPayload); // Log for debugging purposes.\n            const res = await fetch(\"\".concat(_services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress, \"ppe-consumables/update-product-request-items/\").concat(item.id), {\n                method: \"POST\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            });\n            if (res.status === 200) {\n                return res.json();\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    router.push(redirectUrl);\n                }, 3000);\n                throw new Error(\"Unauthorized\");\n            } else {\n                const errorText = await res.text();\n                console.error(\"Update failed:\", errorText);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to update item status.\");\n                throw new Error(\"Update failed\");\n            }\n        });\n        Promise.all(updatePromises).then((results)=>{\n            const successCount = results.filter((result)=>result === null || result === void 0 ? void 0 : result.msg).length;\n            if (successCount > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Successfully updated \".concat(successCount, \" item(s)\"), {\n                    position: \"top-right\"\n                });\n                setTimeout(()=>{\n                    router.replace(\"/ppe-consumable\");\n                }, 2000);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"No items were successfully updated\");\n                router.push(\"/ppe-consumable\");\n            }\n        }).catch((error)=>{\n            console.error(\"Error updating items:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Some items failed to update\");\n        }).finally(()=>{\n            setLoading(false);\n        });\n    };\n    //#endregion updated conditional\n    // #region update\n    const handleUpdate = (action_id)=>{\n        if (!validate()) {\n            return;\n        }\n        // handleSaveClick();\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        console.log(\"savedItems\", savedItems);\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size, _item_size1;\n            return {\n                // RequestItemID: item.id,\n                RequestItemID: typeof item.id === \"string\" && item.id.startsWith(\"row-\") ? null : item.id,\n                ProductID: item.product_id || ((_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value),\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                // SizeID: item.size?.value || null, //TODO no size handel\n                SizeID: ((_item_size1 = item.size) === null || _item_size1 === void 0 ? void 0 : _item_size1.value) && !isNaN(Number(item.size.value)) ? Number(item.size.value) : null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            requestId: ppeId,\n            action_id: action_id,\n            submitterUserId: userData.user_id,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            targetSiteId: site.value,\n            requiredDate: requiredDate,\n            items: items,\n            comment: cancelledReasonapi,\n            orignatorEmail: emailOfOriginator\n        };\n        console.log(\"apiPayload--------------------------------------------------------\", apiPayload);\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n        setLoading(false);\n        setTimeout(()=>{\n            setLoading(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Request submitted!\");\n            router.push(\"/ppe-consumable\");\n        }, 1000);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"update\") {\n            setIsEdit(true);\n        }\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n    }, [\n        0\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"flex flex-col justify-start max-w-full  mx-auto mr-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row gap-8 bg-white p-6 rounded-lg shadow-[0_0_1px_rgba(0,0,0,0.1)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1108,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: nameOfOriginator || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1109,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1107,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1117,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: emailOfOriginator || (userData === null || userData === void 0 ? void 0 : userData.email) || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1118,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1116,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Created Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1126,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: new Date().toLocaleDateString(\"en-CA\"),\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1127,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1125,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Site\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500 ml-[2px]\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1136,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1135,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            value: site,\n                                            onChange: (e)=>{\n                                                setSite();\n                                                handleSiteAdd(e);\n                                            },\n                                            options: siteData,\n                                            isDisabled: (sites === null || sites === void 0 ? void 0 : sites.length) === 1 || submitted,\n                                            styles: customSelectStyles,\n                                            isClearable: true,\n                                            className: \" w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1138,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        errors.site && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.site\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1150,\n                                            columnNumber: 31\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1134,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Required Date \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1154,\n                                                    columnNumber: 31\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1153,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            min: new Date().toISOString().split(\"T\")[0],\n                                            value: requiredDate,\n                                            onChange: (e)=>{\n                                                setRequiredDate(e.target.value);\n                                                validateRequiredDate(e.target.value);\n                                            },\n                                            className: \"block w-full px-4 border rounded-lg form-input\",\n                                            disabled: submitted\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1156,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        dateWarning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-orange-500 text-sm\",\n                                            children: dateWarning\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1168,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.requiredDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.requiredDate\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1171,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1152,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1106,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1100,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row justify-between items-end my-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"flex mb-1 font-semibold tracking-wider text-base pl-2\",\n                                    children: \"Items\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1191,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1190,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col mb-28px bg-white p-6 rounded-lg py-8 shadow-[0_0_2px_rgba(0,0,0,0.2)]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Product \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1200,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1199,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: currentItem.product,\n                                                        onChange: (val)=>handleCurrentItemChange(\"product\", val),\n                                                        options: productsData,\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1202,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1198,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Size\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 \".concat(!((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) || !((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) || submitted ? \"hidden\" : \"\"),\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1216,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1214,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: currentItem.size,\n                                                        onChange: (val)=>handleCurrentItemChange(\"size\", val),\n                                                        options: ((_currentItem_product2 = currentItem.product) === null || _currentItem_product2 === void 0 ? void 0 : _currentItem_product2.sizes) || [],\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: !((_currentItem_product3 = currentItem.product) === null || _currentItem_product3 === void 0 ? void 0 : _currentItem_product3.size_required) || !((_currentItem_product4 = currentItem.product) === null || _currentItem_product4 === void 0 ? void 0 : (_currentItem_product_sizes1 = _currentItem_product4.sizes) === null || _currentItem_product_sizes1 === void 0 ? void 0 : _currentItem_product_sizes1.length) || submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1228,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1213,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Quantity \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1245,\n                                                                columnNumber: 28\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1244,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        // min={0}\n                                                        max: 999,\n                                                        value: currentItem.quantity,\n                                                        onChange: (e)=>handleCurrentItemChange(\"quantity\", e.target.value > 999 ? 999 : parseInt(e.target.value)),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1247,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1243,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Name for Printing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1263,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        maxLength: \"50\" //TODO change in db also\n                                                        ,\n                                                        value: currentItem.nameForPrinting,\n                                                        onChange: (e)=>handleCurrentItemChange(\"nameForPrinting\", e.target.value),\n                                                        disabled: currentItem.quantity !== 1 || submitted || !((_currentItem_product5 = currentItem.product) === null || _currentItem_product5 === void 0 ? void 0 : _currentItem_product5.name_printable),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        placeholder: \"Only if quantity = 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1264,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1262,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Comments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1281,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        value: currentItem.comments,\n                                                        maxLength: \"500\",\n                                                        onChange: (e)=>handleCurrentItemChange(\"comments\", e.target.value),\n                                                        className: \"disabled:text-gray-400 disabled:bg-[#F6F3F3] block w-full h-8 px-4 border rounded-lg form-input resize-none\",\n                                                        rows: 1,\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1282,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1280,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    onClick: saveCurrentItem,\n                                                    // onClick={handleSaveClick}\n                                                    className: \"px-2 py-1 2xl:px-3.5 border border-skin-primary rounded-md text-skin-primary cursor-pointer \".concat(submitted ? \"hidden\" : \"\"),\n                                                    disabled: submitted,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faFloppyDisk\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1303,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1295,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1293,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1197,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ag-theme-alpine my-[24px]\",\n                                        style: {\n                                            height: 250,\n                                            width: \"100%\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__.AgGridReact, {\n                                            ref: gridRef,\n                                            columnDefs: columnDefs,\n                                            rowData: savedItems,\n                                            rowHeight: 35,\n                                            getRowClass: getRowClass,\n                                            onGridReady: onGridReady\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1314,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1310,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    popupType === \"unsavedWarning\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"One item is being edited. If you continue, changes will be lost.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1332,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: closeModal,\n                                                        className: \"border px-4 py-2 rounded\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1337,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            saveCurrentItem(); // discard editing row\n                                                            closeModal();\n                                                        },\n                                                        className: \"bg-blue-600 text-white px-4 py-2 rounded\",\n                                                        children: \"Save Anyway\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1343,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1336,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1331,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1196,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1189,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end gap-4 rounded-lg  p-4 mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    router.push(\"/ppe-consumable\");\n                                },\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6\",\n                                disabled: loading,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1359,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (pageType === \"edit\") {\n                                        console.log(\"hasUnsavedChanges\", hasUnsavedChanges);\n                                        if (hasUnsavedChanges) {\n                                            setPopupType(\"Save\");\n                                            setIsOpen(true);\n                                        } else {\n                                            handleUpdate(5);\n                                        }\n                                    } else {\n                                        handleSubmit(5);\n                                    }\n                                },\n                                disabled: loading || requestStatus === 1,\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6 \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1369,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (hasUnsavedChanges) {\n                                        setPopupType(\"submitWhileEditing\");\n                                        setIsOpen(true);\n                                    } else {\n                                        if (!validate()) return;\n                                        setPopupType(\"submit\");\n                                        setIsOpen(true);\n                                    }\n                                },\n                                className: \"border border-skin-primary bg-skin-primary text-white rounded-md px-6 font-medium \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                disabled: loading || requestStatus === 1,\n                                children: \"Submit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1391,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"reject\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-red-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Reject All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1410,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"approve\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Approve All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1429,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                // onClick={() => {\n                                //   updateSelectedRowStatus(4, 2);\n                                // }}\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"markOrdered\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(requestStatusList.includes(2) ? \"\" : \"hidden\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Mark All Approved Requests to Ordered\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1448,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1358,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1096,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition, {\n                appear: true,\n                show: isOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: (value)=>{\n                        if (popupType !== \"reject\" || isValidCancelReason) {\n                            closeModal();\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1496,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1487,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                            lineNumber: 1517,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1516,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Confirm \".concat(popupType == \"submit\" ? \"Submission\" : popupType == \"reject\" ? \"Rejection\" : popupType == \"approve\" ? \"Approval\" : popupType == \"markOrdered\" ? \"markOrdered\" : popupType == \"Save\" ? \"Save\" : popupType == \"submitWhileEditing\" ? \"Submission\" : \" \")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1515,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeModal,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1541,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1535,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1514,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                popupType == \"submit\" ? \"submit\" : popupType == \"rejectSingleItem\" ? \"reject\" : popupType == \"approveSingleItem\" ? \"approve\" : popupType == \"markSingleItemOrdered\" ? \"mark approved items as ordered\" : popupType == \"reject\" ? \"reject\" : popupType == \"approve\" ? \"approve\" : popupType == \"markOrdered\" ? \"mark approved items as ordered\" : popupType == \"Save\" ? \"save? there is a item being edited,\\n it will be lost if you Save \" : popupType == \"submitWhileEditing\" ? \"Submit? there is a item being edited,\\n it will be lost if you Submit \" : \" \",\n                                                                \" \",\n                                                                \"this request?\",\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1549,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        popupType == \"reject\" || popupType == \"rejectSingleItem\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                    className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                                    rows: \"8\",\n                                                                    value: cancelledReasonapi,\n                                                                    onChange: (e)=>{\n                                                                        setCancelledReasonapi(e.target.value);\n                                                                        if (e.target.value) {\n                                                                            setIsValidCancelReason(true);\n                                                                        }\n                                                                    },\n                                                                    onBlur: (e)=>{\n                                                                        const trimmedValue = trimInputText(e.target.value);\n                                                                        setCancelledReasonapi(trimmedValue);\n                                                                    },\n                                                                    placeholder: \"Provide reason for rejection...\",\n                                                                    maxLength: \"500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1575,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                (popupType == \"reject\" || popupType == \"rejectSingleItem\") && !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"Please Provide reason for cancellation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1595,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1573,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1548,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                closeModal(), setCancelledReasonapi(\"\");\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1604,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                if (popupType === \"reject\" && !(cancelledReasonapi === null || cancelledReasonapi === void 0 ? void 0 : cancelledReasonapi.trim())) {\n                                                                    setIsValidCancelReason(false);\n                                                                    return;\n                                                                }\n                                                                if (pageType == \"add\") {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleSubmit(1);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleSubmit(1);\n                                                                    }\n                                                                } else {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleUpdate(1);\n                                                                    } else if (popupType === \"reject\") {\n                                                                        updateSelectedRowStatus(3, 1);\n                                                                    } else if (popupType === \"approve\") {\n                                                                        updateSelectedRowStatus(2, 1);\n                                                                    } else if (popupType === \"rejectSingleItem\") {\n                                                                        updateSelectedRowStatusForSingleItem(3);\n                                                                    } else if (popupType === \"approveSingleItem\") {\n                                                                        updateSelectedRowStatusForSingleItem(2);\n                                                                    } else if (popupType === \"Save\") {\n                                                                        handleUpdate(5);\n                                                                    } else if (popupType === \"markOrdered\") {\n                                                                        updateSelectedRowStatus(4, 2);\n                                                                    } else if (popupType === \"markSingleItemOrdered\") {\n                                                                        updateSelectedRowStatusForSingleItem(4);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleUpdate(1);\n                                                                    }\n                                                                }\n                                                                setCancelledReasonapi(\"\");\n                                                                closeModal();\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center h-full border border-skin-primary\",\n                                                            children: \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1614,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1603,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1512,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1510,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1501,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1500,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1499,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 1478,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1477,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(PpeConsumable, \"3uVmOuiN21oz/jDcvtM1KLDGaQI=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = PpeConsumable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PpeConsumable);\nvar _c;\n$RefreshReg$(_c, \"PpeConsumable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/PpeConsumable.js\n"));

/***/ })

});
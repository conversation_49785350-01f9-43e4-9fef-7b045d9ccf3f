"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable/[ppeId]/edit",{

/***/ "./components/PpeConsumable.js":
/*!*************************************!*\
  !*** ./components/PpeConsumable.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lodash/forEach */ \"./node_modules/lodash/forEach.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(lodash_forEach__WEBPACK_IMPORTED_MODULE_12__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    valueContainer: (provided)=>({\n            ...provided,\n            height: \"28px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: ()=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst emptyItem = {\n    product: null,\n    size: null,\n    quantity: \"\",\n    nameForPrinting: \"\",\n    comments: \"\"\n};\nconst PpeConsumable = (param)=>{\n    let { userData, pageType, sites, OrderRequestData, ppeId = null } = param;\n    var _currentItem_product, _currentItem_product_sizes, _currentItem_product1, _currentItem_product2, _currentItem_product3, _currentItem_product_sizes1, _currentItem_product4, _currentItem_product5;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [nameOfOriginator, setNameOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [emailOfOriginator, setEmailOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [siteData, setSiteData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            ...emptyItem\n        }\n    ]);\n    const [requiredDate, setRequiredDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [createdDate, setCreatedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [site, setSite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((sites === null || sites === void 0 ? void 0 : sites[0]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [productsData, setProductsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [currentItem, setCurrentItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        ...emptyItem\n    });\n    const [savedItems, setSavedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // console.log(\"dsksldibiasdbilsdbv\", siteData);\n    // Add validation states for current item\n    // const [productValid, setProductValid] = useState(\"\");\n    // const [quantityValid, setQuantityValid] = useState(\"\");\n    const [dateWarning, setDateWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // const [isEditMode, setIsEditMode] = useState();\n    const [requestStatus, setRequestStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const [requestStatusList, setRequestStatusList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [submitted, setSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popupType, setPopupType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [editingRowId, setEditingRowId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gridApi, setGridApi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const closeModal = (e)=>{\n        if (e) {\n            e.preventDefault();\n        }\n        setIsValidCancelReason(true);\n        setCancelledReasonapi(\"\");\n        setIsOpen(false);\n    };\n    //#region Load data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n        if (OrderRequestData || (OrderRequestData === null || OrderRequestData === void 0 ? void 0 : OrderRequestData.length) > 0) {\n            var _OrderRequestData_, _OrderRequestData_1, _OrderRequestData_2, _OrderRequestData_3;\n            // console.log(\"OrderRequestData\", OrderRequestData);\n            if ((_OrderRequestData_ = OrderRequestData[0]) === null || _OrderRequestData_ === void 0 ? void 0 : _OrderRequestData_.action_id) {\n                var _OrderRequestData_4, _OrderRequestData_5;\n                setRequestStatus((_OrderRequestData_4 = OrderRequestData[0]) === null || _OrderRequestData_4 === void 0 ? void 0 : _OrderRequestData_4.action_id);\n                if (((_OrderRequestData_5 = OrderRequestData[0]) === null || _OrderRequestData_5 === void 0 ? void 0 : _OrderRequestData_5.action_id) !== 5) {\n                    setSubmitted(true);\n                }\n            }\n            if ((_OrderRequestData_1 = OrderRequestData[0]) === null || _OrderRequestData_1 === void 0 ? void 0 : _OrderRequestData_1.site_id) {\n                var _OrderRequestData_6, _OrderRequestData_7;\n                setSite({\n                    label: (_OrderRequestData_6 = OrderRequestData[0]) === null || _OrderRequestData_6 === void 0 ? void 0 : _OrderRequestData_6.site_name,\n                    value: (_OrderRequestData_7 = OrderRequestData[0]) === null || _OrderRequestData_7 === void 0 ? void 0 : _OrderRequestData_7.site_id\n                });\n            }\n            if ((_OrderRequestData_2 = OrderRequestData[0]) === null || _OrderRequestData_2 === void 0 ? void 0 : _OrderRequestData_2.required_date) {\n                var _OrderRequestData_8, _OrderRequestData_9;\n                setRequiredDate(((_OrderRequestData_8 = OrderRequestData[0]) === null || _OrderRequestData_8 === void 0 ? void 0 : _OrderRequestData_8.required_date) ? new Date((_OrderRequestData_9 = OrderRequestData[0]) === null || _OrderRequestData_9 === void 0 ? void 0 : _OrderRequestData_9.required_date).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if ((_OrderRequestData_3 = OrderRequestData[0]) === null || _OrderRequestData_3 === void 0 ? void 0 : _OrderRequestData_3.created_at) {\n                var _OrderRequestData_10, _OrderRequestData_11;\n                setCreatedDate(((_OrderRequestData_10 = OrderRequestData[0]) === null || _OrderRequestData_10 === void 0 ? void 0 : _OrderRequestData_10.created_at) ? new Date((_OrderRequestData_11 = OrderRequestData[0]) === null || _OrderRequestData_11 === void 0 ? void 0 : _OrderRequestData_11.created_at).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if (OrderRequestData[0].user_name) {\n                // setNameOfOriginator();\n                setNameOfOriginator(OrderRequestData[0].user_name);\n            }\n            if (OrderRequestData[0].actioned_by_email) {\n                // setNameOfOriginator();\n                setEmailOfOriginator(OrderRequestData[0].actioned_by_email);\n            }\n            // setrowdata\n            if (OrderRequestData && Array.isArray(OrderRequestData)) {\n                console.log(\"load data\", OrderRequestData, requestStatus);\n                const STATUS_MAP = {\n                    1: \"Pending Review\",\n                    2: \"Approved\",\n                    3: \"Rejected\",\n                    4: \"Ordered\",\n                    5: \"Draft\"\n                };\n                const mappedItems = OrderRequestData.map((item)=>({\n                        id: item.order_request_items,\n                        product: {\n                            value: item.product_id,\n                            label: item.product_name\n                        },\n                        size: {\n                            value: item.size,\n                            label: item.size || \"N/A\"\n                        },\n                        quantity: item.quantity,\n                        nameForPrinting: item.name_for_printing,\n                        comments: (item.action_id === 3 ? \"Rejected Reason: \".concat(item.latest_comment) : item.comments) || \"\",\n                        updateFlag: 0,\n                        status: STATUS_MAP[item.action_id]\n                    }));\n                const uniqueValues = [\n                    ...new Set(OrderRequestData.map((item)=>item.action_id))\n                ];\n                setRequestStatusList(uniqueValues);\n                setSavedItems(mappedItems);\n                console.log(\"uniqueValues\", uniqueValues);\n            }\n        }\n    }, [\n        OrderRequestData\n    ]);\n    //#endregion Load data\n    //#region ag-grid\n    const onRowSelected = (event)=>{\n        if (event.node.isSelected()) {\n            setEditingRowId(event.data.id);\n            setCurrentItem(event.data);\n        }\n    };\n    const onGridReady = (params1)=>{\n        setGridApi(params1.api);\n    };\n    // inside your component\n    const getRowClass = (params1)=>{\n        return params1.data.id === editingRowId ? \"bg-blue-100\" : \"\";\n    };\n    const IconsRenderer = (props)=>{\n        const handleDelete = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n        };\n        const handleEdit = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            setHasUnsavedChanges(true);\n            setEditingRowId(selectedRow.id);\n            // setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n            const handleRowUpdate = (actionId)=>()=>{\n                    setSelectedRowData(params.data);\n                    setStatusAction(actionId);\n                    setShowStatusDialog(true);\n                };\n            // event.preventDefault();\n            // const selectedRow = props.data;\n            // setEditingRowId(selectedRow.id || props.node.id);\n            // // const updatedData = savedItems.filter((item) => item !== selectedRow);\n            // // setSavedItems(updatedData);\n            //   setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            // Populate the form with the selected row data\n            setCurrentItem({\n                id: selectedRow.id,\n                product: selectedRow.product,\n                size: selectedRow.size,\n                quantity: selectedRow.quantity,\n                nameForPrinting: selectedRow.nameForPrinting,\n                nameForPrintingFlag: selectedRow.nameForPrinting,\n                comments: selectedRow.comments,\n                updateFlag: 1\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-row gap-4 justify-center text-skin-primary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleEdit,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faPenToSquare\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDelete,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\", \" text-red-500\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faTrash\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Column definitions for the grid\n    const columnDefs = [\n        {\n            headerName: \"Product\",\n            field: \"product.label\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Size\",\n            field: \"size.label\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Quantity\",\n            field: \"quantity\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Name for Printing\",\n            field: \"nameForPrinting\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Comments\",\n            field: \"comments\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            hide: requestStatus === 5 || !requestStatus,\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: 1.25,\n            filterParams: {\n                values: [\n                    \"Pending Review\",\n                    \"Approved\",\n                    \"Rejected\",\n                    \"Ordered\",\n                    \"Draft\"\n                ]\n            }\n        },\n        {\n            headerName: \"Actions\",\n            cellRenderer: IconsRenderer,\n            headerClass: \"header-with-border\",\n            flex: 1\n        }\n    ];\n    //#endregion ag-grid\n    //#region api's\n    function getAllProducts() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-products\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to fetch products: \".concat(error.message));\n            throw error;\n        });\n    }\n    function getAllSites() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/sites\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        });\n    }\n    //#endregion api's\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const siteData = await getAllSites();\n                const formattedSites = siteData === null || siteData === void 0 ? void 0 : siteData.map((site)=>({\n                        value: site.id,\n                        label: site.name\n                    }));\n                setSiteData(formattedSites);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error fetching Sites\", error);\n            }\n        };\n        const fetchProducts = async ()=>{\n            try {\n                const productsJson = await getAllProducts();\n                const filterOutHiddenProducts = productsJson.filter((product)=>!product.IsProductHidden);\n                const formattedProducts = filterOutHiddenProducts === null || filterOutHiddenProducts === void 0 ? void 0 : filterOutHiddenProducts.map((product)=>({\n                        value: product.ProductId,\n                        label: product.ProductName,\n                        size_required: product.IsSizeRequired || product.size_required,\n                        sizes: product.AvailableSizes ? product.AvailableSizes.split(\", \").map((size)=>({\n                                value: size.toLowerCase().replace(/\\s+/g, \"\"),\n                                label: size\n                            })) : [],\n                        productCode: product.ProductCode,\n                        packageQuantity: product.PackageQuantity,\n                        productType: product.ProductType,\n                        name_printable: product.name_printable\n                    }));\n                setProductsData(formattedProducts);\n            } catch (error) {\n                console.error(\"Error fetching products:\", error);\n            }\n        };\n        fetchProducts();\n        fetchSites();\n        setLoading(false);\n    }, []);\n    const updateSelectedRowStatusForSingleItem = (updatedStatusID, params1)=>{\n        if (updatedStatusID == 3) {\n            if (cancelledReasonapi) {\n                setIsValidCancelReason(true);\n            } else {\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        console.log(\"sssssssssssssssssssssssssssssssssssss\", params1);\n        const apiPayload = {\n            request_no: params1.id,\n            item_number: params1.item_number,\n            ProductName: params1.product_name,\n            Size: params1.size,\n            Quantity: params1.quantity,\n            NameForPrinting: params1.NameForPrinting,\n            Comments: updatedStatusID == 3 ? cancelledReasonapi : params1.comment,\n            RequestItemID: params1.request_item_id,\n            commentOnUpdatingRequest: comment,\n            action_id: updatedStatusID,\n            SubmitterUserID: userData.user_id,\n            SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n            orignatorEmail: params1 === null || params1 === void 0 ? void 0 : params1.orignatorEmail\n        };\n        try {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-product-request-items/\").concat(params1.id), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n                getData().then((data)=>{\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            item_number: row.item_number,\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: (row.action_id === 3 ? \"Rejected Reason: \".concat(row.latest_comment) : row.comments) || \"\",\n                            size: row.size,\n                            quantity: row.quantity,\n                            NameForPrinting: row.name_for_printing,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    setRequestRowData(formattedData);\n                    setCancelledReasonapi(\"\");\n                // closeModal();\n                // setShowStatusDialog(false);\n                });\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    //#region Validate\n    // Update the validate function to use savedItems instead of items\n    const validate = ()=>{\n        const newErrors = {};\n        if (!site) newErrors.site = \"Site is required.\";\n        if (!requiredDate) newErrors.requiredDate = \"Required date is missing.\";\n        if (!savedItems || savedItems.length === 0) {\n            newErrors.savedItems = \"At least one item must be added.\";\n        }\n        // Update state for field-level messages\n        setErrors(newErrors);\n        const hasErrors = Object.keys(newErrors).length > 0;\n        if (hasErrors) {\n            const errorMessages = Object.values(newErrors);\n            // setPopupConfig({\n            //   title: \"Validation Error\",\n            //   message: errorMessages.join(\"\\n\"),\n            //   type: \"error\",\n            // });\n            // setShowPopup(true); // trigger popup\n            const errorsLength = Object.keys(newErrors).length;\n            if (errorsLength > 1) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill all required fields.\");\n            } else if (errorsLength == 1 && newErrors.savedItems) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please add at least one item.\");\n            }\n            return false;\n        }\n        return true;\n    };\n    const handleSiteAdd = (value)=>{\n        setSite(value);\n        // Clear the error for site if value is valid\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (value) {\n                delete updated.site;\n            }\n            return updated;\n        });\n    };\n    // Add date validation function\n    const validateRequiredDate = (selectedDate)=>{\n        // setRequiredDate(selectedDate);\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (selectedDate) {\n                delete updated.requiredDate;\n            }\n            return updated;\n        });\n        if (!selectedDate) {\n            setDateWarning(\"\");\n            return;\n        }\n        const today = new Date();\n        const selected = new Date(selectedDate);\n        const twoWeeksFromNow = new Date();\n        twoWeeksFromNow.setDate(today.getDate() + 14);\n        if (selected < twoWeeksFromNow) {\n            setDateWarning(\"Notice: Less than 2 weeks lead time may affect availability.\");\n        } else {\n            setDateWarning(\"\");\n        }\n    };\n    // #region handlers\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    const handleCancelReason = (data)=>{\n        if (data) {\n            setIsValidCancelReason(true);\n        } else {\n            setIsValidCancelReason(false);\n        }\n    };\n    const handleSaveClick = (e)=>{\n        // e.preventDefault();\n        if (hasUnsavedChanges) {\n            setPopupType(\"unsavedWarning\");\n            setIsOpen(true); // your existing modal state\n        } else {\n            saveCurrentItem(e);\n        }\n    };\n    const saveCurrentItem = (e)=>{\n        var _currentItem_product, _currentItem_product_sizes, _currentItem_product1;\n        console.log(\"save order request\");\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (e) {\n                delete updated.savedItems;\n            }\n            return updated;\n        });\n        if (!e) {\n            console.log(\"returning here\");\n            setDateWarning(\"\");\n            return;\n        }\n        e.preventDefault();\n        let count = 0;\n        if (!currentItem.product) {\n            console.log(\"++ product\");\n            count++;\n        // setProductValid(\"Please select a product\");\n        } else {\n        // setProductValid(\"\");\n        }\n        if (!currentItem.quantity || currentItem.quantity < 1) {\n            console.log(\"++ quantity\");\n            count++;\n        // setQuantityValid(\"Please enter a valid quantity\");\n        } else {\n            if (currentItem.quantity > 1 && currentItem.nameForPrinting) {\n                // currentItem.nameForPrinting=\"\"\n                setCurrentItem((prev)=>({\n                        ...prev,\n                        nameForPrinting: \"\"\n                    }));\n            }\n        // setQuantityValid(\"\");\n        }\n        if (((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) && ((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) && !currentItem.size) {\n            console.log(\"++ product size\");\n            count++;\n        // Add size validation if needed\n        }\n        if (count > 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill in all mandatory fields to add items with valid quantity\");\n            return;\n        }\n        //     if (editingRowId) {\n        //         // Update existing item\n        //   const updatedItems = savedItems.map(item =>\n        //     item.id === editingRowId ? { ...currentItem, id: editingRowId } : item\n        //   );\n        //   setSavedItems(updatedItems);\n        //   setEditingRowId(null);\n        // } else {\n        //   // Add new item\n        //   setSavedItems([...savedItems, { ...currentItem, id: Date.now() }]);\n        // }\n        if (editingRowId) {\n            // Add the edited item back to savedItems\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: editingRowId,\n                    product: currentItem.product ? {\n                        ...currentItem.product\n                    } : null,\n                    size: currentItem.size ? {\n                        ...currentItem.size\n                    } : null\n                }\n            ]);\n            setEditingRowId(null);\n        } else {\n            // Add new item\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: \"row-\".concat(Date.now())\n                }\n            ]);\n        }\n        // Reset current item\n        setCurrentItem({\n            ...emptyItem\n        });\n        setEditingRowId(null);\n        setHasUnsavedChanges(false);\n    };\n    // Handle current item changes\n    const handleCurrentItemChange = (field, value)=>{\n        if (field === \"product\") {\n            // console.log(\"field-------------\",value)\n            // Reset other fields when product changes\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    size: null,\n                    quantity: \"\",\n                    nameForPrinting: \"\",\n                    nameForPrintingFlag: \"\",\n                    comments: \"\"\n                }));\n        } else if (field === \"quantity\" && value > 1) {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    nameForPrinting: \"\"\n                }));\n        } else {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value\n                }));\n        }\n    };\n    //#region Save\n    const handleSubmit = (status_ID)=>{\n        if (status_ID != 1 && !validate()) {\n            return;\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssssssssssssssssssss\",savedItems);\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        // Transform savedItems to API format\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size;\n            return {\n                requestItemId: item.requestItemId,\n                // RequestItemID: typeof item.id === 'string' && item.id.startsWith('row-') ? null : item.id,\n                ProductID: (_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value,\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            Status_id: status_ID,\n            SubmitterUserID: userData === null || userData === void 0 ? void 0 : userData.user_id,\n            TargetSiteID: (site === null || site === void 0 ? void 0 : site.value) || 3,\n            RequiredDate: requiredDate,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            items: items,\n            orignatorEmail: emailOfOriginator\n        };\n        // console.log(\"items--------------------------------------------------------\",items)\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/create-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    //#region updated conditional\n    const updateSelectedRowStatus = (updatedStatusID, existingStatusId)=>{\n        const STATUS_MAP = {\n            1: \"Pending Review\",\n            2: \"Approved\",\n            3: \"Rejected\",\n            4: \"Ordered\",\n            5: \"Draft\"\n        };\n        const toBeUpdated = savedItems.filter((item)=>item.status == STATUS_MAP[existingStatusId]);\n        if (toBeUpdated.length === 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"No items found with the specified status\");\n            return;\n        }\n        setLoading(true);\n        if (updatedStatusID == 3) {\n            if (cancelledReasonapi) {\n                setIsValidCancelReason(true);\n                console.log(\"triggerrerererered\", cancelledReasonapi);\n            } else {\n                console.log(\"triggerrerererered\");\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssss\", toBeUpdated);\n        const updatePromises = toBeUpdated.map(async (item)=>{\n            const apiPayload = {\n                request_no: item.id,\n                item_number: item.item_number,\n                ProductName: item.product.label,\n                Size: item.size.label,\n                Quantity: item.quantity,\n                NameForPrinting: item.nameForPrinting,\n                Comments: updatedStatusID == 3 ? cancelledReasonapi : item.comments,\n                RequestItemID: item.id,\n                commentOnUpdatingRequest: cancelledReasonapi,\n                action_id: updatedStatusID,\n                SubmitterUserID: userData.user_id,\n                SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n                orignatorEmail: emailOfOriginator,\n                cancelledReasonapi: cancelledReasonapi\n            };\n            console.log(\"apiPayload\", apiPayload); // Log for debugging purposes.\n            const res = await fetch(\"\".concat(_services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress, \"ppe-consumables/update-product-request-items/\").concat(item.id), {\n                method: \"POST\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            });\n            if (res.status === 200) {\n                return res.json();\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    router.push(redirectUrl);\n                }, 3000);\n                throw new Error(\"Unauthorized\");\n            } else {\n                const errorText = await res.text();\n                console.error(\"Update failed:\", errorText);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to update item status.\");\n                throw new Error(\"Update failed\");\n            }\n        });\n        Promise.all(updatePromises).then((results)=>{\n            const successCount = results.filter((result)=>result === null || result === void 0 ? void 0 : result.msg).length;\n            if (successCount > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Successfully updated \".concat(successCount, \" item(s)\"), {\n                    position: \"top-right\"\n                });\n                setTimeout(()=>{\n                    router.replace(\"/ppe-consumable\");\n                }, 2000);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"No items were successfully updated\");\n                router.push(\"/ppe-consumable\");\n            }\n        }).catch((error)=>{\n            console.error(\"Error updating items:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Some items failed to update\");\n        }).finally(()=>{\n            setLoading(false);\n        });\n    };\n    //#endregion updated conditional\n    // #region update\n    const handleUpdate = (action_id)=>{\n        if (!validate()) {\n            return;\n        }\n        // handleSaveClick();\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        console.log(\"savedItems\", savedItems);\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size, _item_size1;\n            return {\n                // RequestItemID: item.id,\n                RequestItemID: typeof item.id === \"string\" && item.id.startsWith(\"row-\") ? null : item.id,\n                ProductID: item.product_id || ((_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value),\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                // SizeID: item.size?.value || null, //TODO no size handel\n                SizeID: ((_item_size1 = item.size) === null || _item_size1 === void 0 ? void 0 : _item_size1.value) && !isNaN(Number(item.size.value)) ? Number(item.size.value) : null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            requestId: ppeId,\n            action_id: action_id,\n            submitterUserId: userData.user_id,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            targetSiteId: site.value,\n            requiredDate: requiredDate,\n            items: items,\n            comment: cancelledReasonapi,\n            orignatorEmail: emailOfOriginator\n        };\n        console.log(\"apiPayload--------------------------------------------------------\", apiPayload);\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n        setLoading(false);\n        setTimeout(()=>{\n            setLoading(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Request submitted!\");\n            router.push(\"/ppe-consumable\");\n        }, 1000);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"update\") {\n            setIsEdit(true);\n        }\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n    }, [\n        0\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"flex flex-col justify-start max-w-full  mx-auto mr-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row gap-8 bg-white p-6 rounded-lg shadow-[0_0_1px_rgba(0,0,0,0.1)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1129,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: nameOfOriginator || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1130,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1128,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1138,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: emailOfOriginator || (userData === null || userData === void 0 ? void 0 : userData.email) || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1139,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1137,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Created Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1147,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: new Date().toLocaleDateString(\"en-CA\"),\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1148,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1146,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Site\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500 ml-[2px]\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1157,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1156,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            value: site,\n                                            onChange: (e)=>{\n                                                setSite();\n                                                handleSiteAdd(e);\n                                            },\n                                            options: siteData,\n                                            isDisabled: (sites === null || sites === void 0 ? void 0 : sites.length) === 1 || submitted,\n                                            styles: customSelectStyles,\n                                            isClearable: true,\n                                            className: \" w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1159,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        errors.site && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.site\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1171,\n                                            columnNumber: 31\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1155,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Required Date \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1175,\n                                                    columnNumber: 31\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1174,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            min: new Date().toISOString().split(\"T\")[0],\n                                            value: requiredDate,\n                                            onChange: (e)=>{\n                                                setRequiredDate(e.target.value);\n                                                validateRequiredDate(e.target.value);\n                                            },\n                                            className: \"block w-full px-4 border rounded-lg form-input\",\n                                            disabled: submitted\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1177,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        dateWarning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-orange-500 text-sm\",\n                                            children: dateWarning\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1189,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.requiredDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.requiredDate\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1192,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1173,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1127,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row justify-between items-end my-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"flex mb-1 font-semibold tracking-wider text-base pl-2\",\n                                    children: \"Items\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1212,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1211,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col mb-28px bg-white p-6 rounded-lg py-8 shadow-[0_0_2px_rgba(0,0,0,0.2)]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Product \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1221,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1220,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        value: currentItem.product,\n                                                        onChange: (val)=>handleCurrentItemChange(\"product\", val),\n                                                        options: productsData,\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1223,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1219,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Size\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 \".concat(!((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) || !((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) || submitted ? \"hidden\" : \"\"),\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1237,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1235,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        value: currentItem.size,\n                                                        onChange: (val)=>handleCurrentItemChange(\"size\", val),\n                                                        options: ((_currentItem_product2 = currentItem.product) === null || _currentItem_product2 === void 0 ? void 0 : _currentItem_product2.sizes) || [],\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: !((_currentItem_product3 = currentItem.product) === null || _currentItem_product3 === void 0 ? void 0 : _currentItem_product3.size_required) || !((_currentItem_product4 = currentItem.product) === null || _currentItem_product4 === void 0 ? void 0 : (_currentItem_product_sizes1 = _currentItem_product4.sizes) === null || _currentItem_product_sizes1 === void 0 ? void 0 : _currentItem_product_sizes1.length) || submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1249,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1234,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Quantity \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1266,\n                                                                columnNumber: 28\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1265,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        // min={0}\n                                                        max: 999,\n                                                        value: currentItem.quantity,\n                                                        onChange: (e)=>handleCurrentItemChange(\"quantity\", e.target.value > 999 ? 999 : parseInt(e.target.value)),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1268,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1264,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Name for Printing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1284,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        maxLength: \"50\" //TODO change in db also\n                                                        ,\n                                                        value: currentItem.nameForPrinting,\n                                                        onChange: (e)=>handleCurrentItemChange(\"nameForPrinting\", e.target.value),\n                                                        disabled: currentItem.quantity !== 1 || submitted || !((_currentItem_product5 = currentItem.product) === null || _currentItem_product5 === void 0 ? void 0 : _currentItem_product5.name_printable),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        placeholder: \"Only if quantity = 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1285,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1283,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Comments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1302,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        value: currentItem.comments,\n                                                        maxLength: \"500\",\n                                                        onChange: (e)=>handleCurrentItemChange(\"comments\", e.target.value),\n                                                        className: \"disabled:text-gray-400 disabled:bg-[#F6F3F3] block w-full h-8 px-4 border rounded-lg form-input resize-none\",\n                                                        rows: 1,\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1303,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1301,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    onClick: saveCurrentItem,\n                                                    // onClick={handleSaveClick}\n                                                    className: \"px-2 py-1 2xl:px-3.5 border border-skin-primary rounded-md text-skin-primary cursor-pointer \".concat(submitted ? \"hidden\" : \"\"),\n                                                    disabled: submitted,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faFloppyDisk\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1324,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1316,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1314,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1218,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ag-theme-alpine my-[24px]\",\n                                        style: {\n                                            height: 250,\n                                            width: \"100%\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__.AgGridReact, {\n                                            ref: gridRef,\n                                            columnDefs: columnDefs,\n                                            rowData: savedItems,\n                                            rowHeight: 35,\n                                            getRowClass: getRowClass,\n                                            onGridReady: onGridReady\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1335,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1331,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    popupType === \"unsavedWarning\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"One item is being edited. If you continue, changes will be lost.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1353,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: closeModal,\n                                                        className: \"border px-4 py-2 rounded\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1358,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            saveCurrentItem(); // discard editing row\n                                                            closeModal();\n                                                        },\n                                                        className: \"bg-blue-600 text-white px-4 py-2 rounded\",\n                                                        children: \"Save Anyway\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1364,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1357,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1352,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1217,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1210,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end gap-4 rounded-lg  p-4 mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    router.push(\"/ppe-consumable\");\n                                },\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6\",\n                                disabled: loading,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1380,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (pageType === \"edit\") {\n                                        console.log(\"hasUnsavedChanges\", hasUnsavedChanges);\n                                        if (hasUnsavedChanges) {\n                                            setPopupType(\"Save\");\n                                            setIsOpen(true);\n                                        } else {\n                                            handleUpdate(5);\n                                        }\n                                    } else {\n                                        handleSubmit(5);\n                                    }\n                                },\n                                disabled: loading || requestStatus === 1,\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6 \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1390,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (hasUnsavedChanges) {\n                                        setPopupType(\"submitWhileEditing\");\n                                        setIsOpen(true);\n                                    } else {\n                                        if (!validate()) return;\n                                        setPopupType(\"submit\");\n                                        setIsOpen(true);\n                                    }\n                                },\n                                className: \"border border-skin-primary bg-skin-primary text-white rounded-md px-6 font-medium \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                disabled: loading || requestStatus === 1,\n                                children: \"Submit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1412,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"reject\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-red-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Reject All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1431,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"approve\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Approve All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1450,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                // onClick={() => {\n                                //   updateSelectedRowStatus(4, 2);\n                                // }}\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"markOrdered\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(requestStatusList.includes(2) ? \"\" : \"hidden\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Mark All Approved Requests to Ordered\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1469,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1379,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1117,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition, {\n                appear: true,\n                show: isOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: (value)=>{\n                        if (popupType !== \"reject\" || isValidCancelReason) {\n                            closeModal();\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1514,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1505,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                            lineNumber: 1535,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1534,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Confirm \".concat(popupType == \"submit\" ? \"Submission\" : popupType == \"reject\" ? \"Rejection\" : popupType == \"approve\" ? \"Approval\" : popupType == \"markOrdered\" ? \"markOrdered\" : popupType == \"Save\" ? \"Save\" : popupType == \"submitWhileEditing\" ? \"Submission\" : \" \")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1533,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeModal,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1559,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1553,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1532,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                popupType == \"submit\" ? \"submit\" : popupType == \"rejectSingleItem\" ? \"reject\" : popupType == \"approveSingleItem\" ? \"approve\" : popupType == \"markSingleItemOrdered\" ? \"mark approved items as ordered\" : popupType == \"reject\" ? \"reject\" : popupType == \"approve\" ? \"approve\" : popupType == \"markOrdered\" ? \"mark approved items as ordered\" : popupType == \"Save\" ? \"save? there is a item being edited,\\n it will be lost if you Save \" : popupType == \"submitWhileEditing\" ? \"Submit? there is a item being edited,\\n it will be lost if you Submit \" : \" \",\n                                                                \" \",\n                                                                \"this request?\",\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1567,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        popupType == \"reject\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                    className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                                    rows: \"8\",\n                                                                    value: cancelledReasonapi,\n                                                                    onChange: (e)=>{\n                                                                        setCancelledReasonapi(e.target.value);\n                                                                        if (e.target.value) {\n                                                                            setIsValidCancelReason(true);\n                                                                        }\n                                                                    },\n                                                                    onBlur: (e)=>{\n                                                                        const trimmedValue = trimInputText(e.target.value);\n                                                                        setCancelledReasonapi(trimmedValue);\n                                                                    },\n                                                                    placeholder: \"Provide reason for rejection...\",\n                                                                    maxLength: \"500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1593,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                popupType == \"reject\" && !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"Please Provide reason for cancellation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1613,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1591,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1566,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                closeModal(), setCancelledReasonapi(\"\");\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1622,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                if (popupType === \"reject\" && !(cancelledReasonapi === null || cancelledReasonapi === void 0 ? void 0 : cancelledReasonapi.trim())) {\n                                                                    setIsValidCancelReason(false);\n                                                                    return;\n                                                                }\n                                                                if (pageType == \"add\") {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleSubmit(1);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleSubmit(1);\n                                                                    }\n                                                                } else {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleUpdate(1);\n                                                                    } else if (popupType === \"reject\") {\n                                                                        updateSelectedRowStatus(3, 1);\n                                                                    } else if (popupType === \"approve\") {\n                                                                        updateSelectedRowStatus(2, 1);\n                                                                    } else if (popupType === \"rejectSingleItem\") {\n                                                                        updateSelectedRowStatusForSingleItem(3);\n                                                                    } else if (popupType === \"approveSingleItem\") {\n                                                                        updateSelectedRowStatusForSingleItem(2);\n                                                                    } else if (popupType === \"Save\") {\n                                                                        handleUpdate(5);\n                                                                    } else if (popupType === \"markOrdered\") {\n                                                                        updateSelectedRowStatus(4, 2);\n                                                                    } else if (popupType === \"markSingleItemOrdered\") {\n                                                                        updateSelectedRowStatusForSingleItem(4, 2);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleUpdate(1);\n                                                                    }\n                                                                }\n                                                                setCancelledReasonapi(\"\");\n                                                                closeModal();\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center h-full border border-skin-primary\",\n                                                            children: \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1632,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1621,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1530,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1528,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1519,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1518,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1517,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 1496,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1495,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(PpeConsumable, \"O68D4V6/DUUfJI1W3US3XF3lslU=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = PpeConsumable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PpeConsumable);\nvar _c;\n$RefreshReg$(_c, \"PpeConsumable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/PpeConsumable.js\n"));

/***/ })

});
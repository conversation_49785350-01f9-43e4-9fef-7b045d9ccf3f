"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable",{

/***/ "./components/Navbar.js":
/*!******************************!*\
  !*** ./components/Navbar.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   themes: function() { return /* binding */ themes; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst themes = [\n    {\n        bg: \"#022d71\",\n        text: \"#022d71\",\n        name: \"DPS\"\n    },\n    {\n        bg: \"#2e9b28\",\n        text: \"#2e9b28\",\n        name: \"EFC\"\n    },\n    {\n        bg: \"#a91e23\",\n        text: \"#a91e23\",\n        name: \"M&S\"\n    },\n    {\n        bg: \"#3d6546\",\n        text: \"#3d6546\",\n        name: \"FPP\"\n    }\n];\nconst Navbar = (param)=>{\n    let { userData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [pageName, setPageName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentRoute, setCurrentRoute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentPath = router.pathname;\n        console.log(currentPath);\n        setCurrentRoute(currentPath);\n        if (currentPath === \"/finished-product-request/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Goods Request\");\n        } else if (currentPath === \"/raw-material-request/add\") {\n            setPageName(\"Raw Material Request\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n        } else if (currentPath === \"/packaging-form/add\") {\n            setPageName(\"Packaging Request\");\n        } else if (currentPath === \"/ppe-consumable/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"PPE-Consumable Request\");\n        } else if (currentPath === \"/ppe-consumable/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n        } else if (currentPath === \"/variety/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"New Variety Request\");\n        } else if (currentPath === \"/variety/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"Edit New Variety Request\");\n        } else if (currentPath === \"/packaging-form/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Edit Packaging Request\");\n        } else if (currentPath === \"/raw-material-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Edit Raw Material Request\");\n        } else if (currentPath === \"/finished-product-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Edit Finished Goods Request\");\n        } else if ((currentPath === null || currentPath === void 0 ? void 0 : currentPath.startsWith(\"/supplier\")) && (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit\"))) {\n            setPageName(\"Edit Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/add\")) {\n            setPageName(\"Add Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit/forms\")) {\n            setPageName(\"Supplier Form\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/confirm\")) {\n            setPageName(\"Confirm Details for Supplier\");\n        } else if ((currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/bulk-upload\")) || currentPath == \"/supplier/bulk-upload/validation/[batchId]\") {\n            setPageName(\"Bulk Upload\");\n        } else if (currentPath === \"/suppliers\") {\n            setPageName(\"Suppliers\");\n        } else if (currentPath === \"/users\") {\n            setPageName(\"User Management\");\n        } else if (currentPath === \"/viewlogs\") {\n            setPageName(\"View Logs\");\n        } else if (currentPath === \"/products\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Products\");\n        } else if (currentPath === \"/variety\") {\n            setPageName(\"Variety\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n        } else if (currentPath === \"/finishedProductRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Product Request\");\n        } else if (currentPath === \"/rawMaterialRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Raw Material Request\");\n        } else if (currentPath === \"/whatif\" || currentPath === \"/whatif/reports/masterRollingForecast\") {\n            setPageName(\"Whatif\");\n        } else if (currentPath === \"/service_level\" || currentPath === \"/service_level/reports/masterForcast\") {\n            setPageName(\"Service Level\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.startsWith(\"/ppe-consumable\")) {\n            setPageName(\"PPE Consumables\");\n        }\n    }, [\n        router.pathname\n    ]);\n    const baseCompanyOptions = [\n        {\n            value: \"dpsltd\",\n            label: \"DPS\"\n        },\n        {\n            value: \"dpsltdms\",\n            label: \"DPS M&S\"\n        },\n        {\n            value: \"efcltd\",\n            label: \"EFC\"\n        },\n        {\n            value: \"fpp-ltd\",\n            label: \"FPP\"\n        }\n    ];\n    const companyOptions = [\n        ...baseCompanyOptions,\n        ...(userData === null || userData === void 0 ? void 0 : userData.role_id) === 6 || \"<EMAIL>\".split(\";\").includes(userData === null || userData === void 0 ? void 0 : userData.email) ? [\n            {\n                value: \"issproduce\",\n                label: \"ISS\"\n            },\n            {\n                value: \"flrs\",\n                label: \"FLRS\",\n                disabled: true\n            },\n            {\n                value: \"thl\",\n                label: \"THL\",\n                disabled: true\n            }\n        ] : []\n    ];\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((userData === null || userData === void 0 ? void 0 : userData.ADCompanyName) === \"DPS MS\" ? \"dpsltdms\" : (userData === null || userData === void 0 ? void 0 : userData.company) || \"\");\n    const handleCompanyChange = async (event)=>{\n        const company = event.target.value;\n        if (isUpdating) return; // Prevent multiple rapid clicks\n        setIsUpdating(true);\n        try {\n            const apiBase = \"http://localhost:8081\" || 0;\n            // Call the API to update company in session\n            const response = await fetch(\"\".concat(apiBase, \"/api/auth/update-company\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    company\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"Failed to update company\");\n            }\n            const result = await response.json();\n            // Update local state\n            setSelectedCompany(company);\n            // Reload the page to reflect changes throughout the application\n            router.reload();\n        } catch (error) {\n            console.error(\"Error updating company:\", error);\n            alert(\"Failed to update company. Please try again.\");\n            // Reset the select to previous value on error\n            event.target.value = selectedCompany;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"titlebar\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row justify-between w-full bg-skin-primary h-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row items-center page-heading cursor-pointer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__.faChevronLeft,\n                                    className: \"pageName text-white\",\n                                    onClick: ()=>router.back()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    // href={currentRoute}\n                                    className: \"ml-4 text-xl font-medium font-poppinsregular pageName text-white tracking-[0.05em]\",\n                                    children: pageName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, undefined),\n                    ((userData === null || userData === void 0 ? void 0 : userData.role_id) == 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) == 6 || \"<EMAIL>\".split(\";\").includes(userData === null || userData === void 0 ? void 0 : userData.email)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end w-1/2 items-center mr-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedCompany,\n                                onChange: handleCompanyChange,\n                                disabled: isUpdating,\n                                className: \"bg-white text-black rounded disabled:opacity-50\",\n                                children: companyOptions.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: opt.value,\n                                        disabled: opt.disabled,\n                                        children: opt.label\n                                    }, opt.value, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 189,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, undefined),\n                            isUpdating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-white text-sm\",\n                                children: \"Updating...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 199,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 181,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"EcIBICVLCJu3IdXMDDAfVy7bwBw=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Navbar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Navbar);\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Navbar.js\n"));

/***/ })

});
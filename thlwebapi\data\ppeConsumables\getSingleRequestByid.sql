SELECT r.[id],
       r.[request_id],
       r.[user_id],
       r.[site_id],
       r.[created_at],
       r.[required_date],
       r.[is_active],
       ori.[id] as order_request_items,
       ori.[size],
       ori.[quantity],
       ori.[name_for_printing],
       ori.[comments],
       pro.[id] as product_id,
       pro.[name] as product_name,
       ste.[name] as site_name,
       sus.[action_id],
       sus.[actioned_by],
       sus.[actioned_by_email],
       sus.[comment] as latest_comment,
       u.[full_name] AS user_name,
       sus.[actioned_at] as created_date
FROM [Iss_ppe_consumables_portal].[dbo].[order_requests] r
LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[order_request_items] ori
       ON r.request_id = ori.request_id AND ori.is_active = 1
LEFT JOIN [Iss_ppe_consumables_portal].[dbo].sites ste
       ON r.site_id = ste.id AND ste.is_active = 1
LEFT JOIN [Iss_ppe_consumables_portal].[dbo].status sus
       ON ori.id = sus.request_item_id AND sus.is_active = 1 AND sus.is_latest = 1
LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[products] pro
       ON ori.product_id = pro.id
  LEFT JOIN [users] u ON r.user_id = u.user_id

WHERE r.is_active = 1
  AND r.request_id = @ProductId
ORDER BY r.created_at DESC;

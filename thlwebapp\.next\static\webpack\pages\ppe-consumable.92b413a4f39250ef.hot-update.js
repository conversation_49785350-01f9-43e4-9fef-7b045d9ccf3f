"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable",{

/***/ "./components/Navbar.js":
/*!******************************!*\
  !*** ./components/Navbar.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   themes: function() { return /* binding */ themes; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst themes = [\n    {\n        bg: \"#022d71\",\n        text: \"#022d71\",\n        name: \"DPS\"\n    },\n    {\n        bg: \"#2e9b28\",\n        text: \"#2e9b28\",\n        name: \"EFC\"\n    },\n    {\n        bg: \"#a91e23\",\n        text: \"#a91e23\",\n        name: \"M&S\"\n    },\n    {\n        bg: \"#3d6546\",\n        text: \"#3d6546\",\n        name: \"FPP\"\n    }\n];\nconst Navbar = (param)=>{\n    let { userData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [pageName, setPageName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentRoute, setCurrentRoute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentPath = router.pathname;\n        console.log(currentPath);\n        setCurrentRoute(currentPath);\n        if (currentPath === \"/finished-product-request/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Goods Request\");\n        } else if (currentPath === \"/raw-material-request/add\") {\n            setPageName(\"Raw Material Request\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n        } else if (currentPath === \"/packaging-form/add\") {\n            setPageName(\"Packaging Request\");\n        } else if (currentPath === \"/ppe-consumable/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"Ppe-consumable Request\");\n        } else if (currentPath === \"/ppe-consumable/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n        } else if (currentPath === \"/variety/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"New Variety Request\");\n        } else if (currentPath === \"/variety/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"Edit New Variety Request\");\n        } else if (currentPath === \"/packaging-form/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Edit Packaging Request\");\n        } else if (currentPath === \"/raw-material-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Edit Raw Material Request\");\n        } else if (currentPath === \"/finished-product-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Edit Finished Goods Request\");\n        } else if ((currentPath === null || currentPath === void 0 ? void 0 : currentPath.startsWith(\"/supplier\")) && (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit\"))) {\n            setPageName(\"Edit Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/add\")) {\n            setPageName(\"Add Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit/forms\")) {\n            setPageName(\"Supplier Form\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/confirm\")) {\n            setPageName(\"Confirm Details for Supplier\");\n        } else if ((currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/bulk-upload\")) || currentPath == \"/supplier/bulk-upload/validation/[batchId]\") {\n            setPageName(\"Bulk Upload\");\n        } else if (currentPath === \"/suppliers\") {\n            setPageName(\"Suppliers\");\n        } else if (currentPath === \"/users\") {\n            setPageName(\"User Management\");\n        } else if (currentPath === \"/viewlogs\") {\n            setPageName(\"View Logs\");\n        } else if (currentPath === \"/products\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Products\");\n        } else if (currentPath === \"/variety\") {\n            setPageName(\"Variety\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n        } else if (currentPath === \"/finishedProductRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Product Request\");\n        } else if (currentPath === \"/rawMaterialRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Raw Material Request\");\n        } else if (currentPath === \"/whatif\" || currentPath === \"/whatif/reports/masterRollingForecast\") {\n            setPageName(\"Whatif\");\n        } else if (currentPath === \"/service_level\" || currentPath === \"/service_level/reports/masterForcast\") {\n            setPageName(\"Service Level\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.startsWith(\"/ppe-consumable\")) {\n            setPageName(\"PPE Consumables\");\n        }\n    }, [\n        router.pathname\n    ]);\n    const baseCompanyOptions = [\n        {\n            value: \"dpsltd\",\n            label: \"DPS\"\n        },\n        {\n            value: \"dpsltdms\",\n            label: \"DPS M&S\"\n        },\n        {\n            value: \"efcltd\",\n            label: \"EFC\"\n        },\n        {\n            value: \"fpp-ltd\",\n            label: \"FPP\"\n        }\n    ];\n    const companyOptions = [\n        ...baseCompanyOptions,\n        ...(userData === null || userData === void 0 ? void 0 : userData.role_id) === 6 || \"<EMAIL>\".split(\";\").includes(userData === null || userData === void 0 ? void 0 : userData.email) ? [\n            {\n                value: \"issproduce\",\n                label: \"ISS\"\n            },\n            {\n                value: \"flrs\",\n                label: \"FLRS\",\n                disabled: true\n            },\n            {\n                value: \"thl\",\n                label: \"THL\",\n                disabled: true\n            }\n        ] : []\n    ];\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((userData === null || userData === void 0 ? void 0 : userData.ADCompanyName) === \"DPS MS\" ? \"dpsltdms\" : (userData === null || userData === void 0 ? void 0 : userData.company) || \"\");\n    const handleCompanyChange = async (event)=>{\n        const company = event.target.value;\n        if (isUpdating) return; // Prevent multiple rapid clicks\n        setIsUpdating(true);\n        try {\n            const apiBase = \"http://localhost:8081\" || 0;\n            // Call the API to update company in session\n            const response = await fetch(\"\".concat(apiBase, \"/api/auth/update-company\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    company\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"Failed to update company\");\n            }\n            const result = await response.json();\n            // Update local state\n            setSelectedCompany(company);\n            // Reload the page to reflect changes throughout the application\n            router.reload();\n        } catch (error) {\n            console.error(\"Error updating company:\", error);\n            alert(\"Failed to update company. Please try again.\");\n            // Reset the select to previous value on error\n            event.target.value = selectedCompany;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"titlebar\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row justify-between w-full bg-skin-primary h-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row items-center page-heading cursor-pointer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__.faChevronLeft,\n                                    className: \"pageName text-white\",\n                                    onClick: ()=>router.back()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    // href={currentRoute}\n                                    className: \"ml-4 text-xl font-medium font-poppinsregular pageName text-white tracking-[0.05em]\",\n                                    children: pageName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, undefined),\n                    ((userData === null || userData === void 0 ? void 0 : userData.role_id) == 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) == 6 || \"<EMAIL>\".split(\";\").includes(userData === null || userData === void 0 ? void 0 : userData.email)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end w-1/2 items-center mr-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedCompany,\n                                onChange: handleCompanyChange,\n                                disabled: isUpdating,\n                                className: \"bg-white text-black rounded disabled:opacity-50\",\n                                children: companyOptions.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: opt.value,\n                                        disabled: opt.disabled,\n                                        children: opt.label\n                                    }, opt.value, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 189,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, undefined),\n                            isUpdating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-white text-sm\",\n                                children: \"Updating...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 199,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 181,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"EcIBICVLCJu3IdXMDDAfVy7bwBw=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Navbar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Navbar);\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Navbar.js\n"));

/***/ }),

/***/ "./pages/ppe-consumable.js":
/*!*********************************!*\
  !*** ./pages/ppe-consumable.js ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _components_AddProductDialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/AddProductDialog */ \"./components/AddProductDialog.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var _utils_renderer_ppeActionRenderer__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/renderer/ppeActionRenderer */ \"./utils/renderer/ppeActionRenderer.js\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _utils_renderer_ppeActiveRenderer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/renderer/ppeActiveRenderer */ \"./utils/renderer/ppeActiveRenderer.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst PPEConsumables = (param)=>{\n    let { userData } = param;\n    _s();\n    // console.log(\"PARETN component rendered\", userData);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    // const [rowData, setRowData] = useState([]);\n    const [requestRowData, setRequestRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__.useLoading)();\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [orderView, setOrderView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [productTypes, setProductTypes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    //#region pop up states\n    const [showAddProduct, setShowAddProduct] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isEditingProduct, setIsEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editProductData, setEditProductData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Controlled filter states\n    const [siteFilter, setSiteFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [dateFilter, setDateFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showStatusDialog, setShowStatusDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedRowData, setSelectedRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [statusAction, setStatusAction] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Load saved view from localStorage on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Only run on client side\n        if (true) {\n            const savedView = localStorage.getItem(\"ppeSelectedView\");\n            if (savedView && (savedView === \"Orders\" || savedView === \"Products\")) {\n                setSelectedView(savedView);\n                setOrderView(savedView === \"Orders\");\n            } else {\n                setSelectedView(\"Orders\");\n                setOrderView(true);\n            }\n        }\n    }, []); // Empty dependency array - runs only on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!selectedView) return;\n        const fetchProducts = async ()=>{\n            if (selectedView === \"Orders\") {\n                getData().then((data)=>{\n                    // console.log(\"data\", data);\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        return {\n                            id: row.id,\n                            // request_id: row.request_id,\n                            // request_id: `${row.request_id} - ${counters[row.request_id]}`, // add numbering\n                            // request_id: `${row.request_id} - ${index}`, // stable per request_id\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.request_item_id),\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: row.comments,\n                            NameForPrinting: row.name_for_printing,\n                            size: row.size,\n                            quantity: row.quantity,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    // console.log(\"Formatted Data:\", formattedData);\n                    // setRowData(formattedData);\n                    setRequestRowData(formattedData);\n                });\n            } else {\n                const [productsJson, productTypesJson] = await Promise.all([\n                    getAllProducts(),\n                    getProductTypes()\n                ]);\n                // console.log(\"Fetched Products JSON:\", productsJson);\n                // console.log(\"Fetched Product Types JSON:\", productTypesJson);\n                // Save product types for later use (e.g., filters or dialogs)\n                if (Array.isArray(productTypesJson)) {\n                    setProductTypes(productTypesJson);\n                } else {\n                    setProductTypes([]);\n                }\n                // console.log(\"Fetched Products JSON:\", productsJson);\n                if (productsJson && productsJson.length > 0) {\n                    // console.log(\n                    //   \"Fetched Products JSON inside code execution\",\n                    //   productsJson\n                    // );\n                    const formattedProductData = productsJson.map((product)=>({\n                            product_id: product.ProductId,\n                            product_name: product.ProductName || \"Unnamed Product\",\n                            category: product.ProductType || \"Uncategorized\",\n                            type_id: product.TypeId,\n                            stock: 0,\n                            sizes: product.AvailableSizes || \"One Size\",\n                            unit: deriveUnitFromPackageQuantity(product.PackageQuantity),\n                            name_printable: product.name_printable,\n                            IsProductHidden: product.IsProductHidden\n                        }));\n                    // setRowData(formattedProductData);\n                    setRequestRowData(formattedProductData);\n                } else {\n                    const fallbackProductData = [\n                        {\n                            product_name: \"Nitrile Gloves\",\n                            category: \"Gloves\",\n                            stock: 1200,\n                            unit: \"Box\",\n                            status: \"Available\"\n                        }\n                    ];\n                // setRowData(fallbackProductData);\n                }\n            }\n        };\n        fetchProducts();\n    }, [\n        selectedView\n    ]);\n    // #region get data\n    const getData = async ()=>{\n        // setRowData([]);\n        setRequestRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        console.log(\"\");\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-ppe-requests\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await logout();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            console.error(error);\n        });\n    };\n    async function getAllProducts() {\n        // setRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-products\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await logout();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to fetch products: \".concat(error.message));\n            throw error;\n        });\n    }\n    async function getProductTypes() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/get-product_types\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await logout();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch product types\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to fetch product types: \".concat(error.message));\n            throw error;\n        });\n    }\n    //#region column def\n    // Example column definitions for Products\n    // Replace with proper filter handling\n    const handleSiteFilterChange = (value)=>{\n        setSiteFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"site_name\");\n            if (filterInstance) {\n                if (value) {\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        filter: value\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"status\");\n            if (filterInstance) {\n                if (value) {\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        filter: value\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    const handleSelectedView = (selectedType)=>{\n        setSelectedView(selectedType);\n        setOrderView(selectedType === \"Orders\");\n        // Only save to localStorage on client side\n        if (true) {\n            localStorage.setItem(\"ppeSelectedView\", selectedType);\n        }\n    };\n    const handleDateFilterChange = (value)=>{\n        setDateFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"required_date\");\n            if (filterInstance) {\n                if (value) {\n                    const selectedDate = new Date(value);\n                    const yyyy = selectedDate.getFullYear();\n                    const mm = String(selectedDate.getMonth() + 1).padStart(2, \"0\");\n                    const dd = String(selectedDate.getDate()).padStart(2, \"0\");\n                    const formattedDate = \"\".concat(yyyy, \"-\").concat(mm, \"-\").concat(dd);\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        dateFrom: formattedDate\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    // Example column definitions\n    const requestColumnDefs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>[\n            {\n                headerName: \"Request ID\",\n                field: \"request_id\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Required Date\",\n                field: \"required_date\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agDateColumnFilter\",\n                filterParams: {\n                    comparator: (filterLocalDateAtMidnight, cellValue)=>{\n                        const dateAsString = cellValue;\n                        if (!dateAsString) return 0;\n                        const cellDate = new Date(dateAsString);\n                        const cellDateAtMidnight = new Date(cellDate);\n                        cellDateAtMidnight.setHours(0, 0, 0, 0);\n                        if (cellDateAtMidnight < filterLocalDateAtMidnight) {\n                            return -1;\n                        } else if (cellDateAtMidnight > filterLocalDateAtMidnight) {\n                            return 1;\n                        } else {\n                            return 0;\n                        }\n                    }\n                },\n                valueFormatter: (params)=>{\n                    return params.value ? new Date(params.value).toLocaleDateString() : \"\";\n                }\n            },\n            {\n                headerName: \"Site\",\n                field: \"site_name\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\",\n                filterParams: {\n                    values: [\n                        \"ISS1-Teynham\",\n                        \"ISS2-Linton\",\n                        \"ISS3-Sittingbourne\"\n                    ]\n                }\n            },\n            {\n                headerName: \"Product\",\n                field: \"product_name\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Size\",\n                field: \"size\",\n                flex: 0.75,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Qty\",\n                field: \"quantity\",\n                flex: 0.75,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Requestor\",\n                field: \"requestor\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Comment\",\n                field: \"comment\",\n                flex: 1,\n                hide: !orderView\n            },\n            {\n                headerName: \"Status\",\n                field: \"status\",\n                hide: !orderView,\n                cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                cellStyle: ()=>({\n                        justifyContent: \"center\"\n                    }),\n                flex: 1.25,\n                filterParams: {\n                    values: [\n                        \"Pending Review\",\n                        \"Approved\",\n                        \"Rejected\",\n                        \"Ordered\",\n                        \"Draft\"\n                    ]\n                }\n            },\n            //product view\n            {\n                headerName: \"Product Name\",\n                field: \"product_name\",\n                flex: 1,\n                hide: orderView\n            },\n            {\n                headerName: \"Category\",\n                field: \"category\",\n                flex: 0.5,\n                hide: orderView\n            },\n            {\n                headerName: \"Sizes\",\n                field: \"sizes\",\n                flex: 0.5,\n                hide: orderView\n            },\n            {\n                headerName: \"Name Printing\",\n                field: \"name_printable\",\n                flex: 0.5,\n                hide: orderView,\n                cellRenderer: \"agCheckboxCellRenderer\",\n                cellRendererParams: {\n                    disabled: true\n                },\n                cellStyle: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\"\n                },\n                headerClass: \"ag-header-cell-centered\"\n            },\n            {\n                headerName: \"Status\",\n                // field: \"site_id\",\n                flex: 0.5,\n                cellRenderer: (params)=>(0,_utils_renderer_ppeActiveRenderer__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(params, setShowAddProduct, orderView, setIsEditingProduct, setEditProductData, //banana\n                    setShowStatusDialog, setSelectedRowData, setStatusAction),\n                sortable: false,\n                hide: orderView\n            },\n            {\n                headerName: \"Actions\",\n                field: \"site_id\",\n                flex: 1,\n                cellRenderer: (params)=>(0,_utils_renderer_ppeActionRenderer__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(params, setShowAddProduct, orderView, setIsEditingProduct, setEditProductData, //banana\n                    setShowStatusDialog, setSelectedRowData, setStatusAction, userData),\n                sortable: false\n            }\n        ], [\n        orderView\n    ]);\n    // console.log(\"order vbiew\", orderView);\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: true,\n            filterParams: {\n                debounceMs: 300\n            },\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }), []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsLoading(false);\n    // console.log(\n    //   \"Selected View:\",\n    //   selectedView,\n    //   \"Order View:\",\n    //   orderView,\n    //   selectedView === \"Orders\"\n    // );\n    }, [\n        selectedView\n    ]);\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(requestColumnDefs);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (gridRef.current && gridRef.current.api) {\n            gridRef.current.api.setColumnDefs(requestColumnDefs);\n            // Refresh the grid to apply new column definitions\n            gridRef.current.api.refreshHeader();\n            gridRef.current.api.refreshCells();\n        }\n    }, [\n        requestColumnDefs,\n        orderView\n    ]); // Add orderView dependency\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n        setSearchInput(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const handleClearFilters = ()=>{\n        // Clear AG Grid filters\n        if (gridRef.current && gridRef.current.api) {\n            gridRef.current.api.setFilterModel(null);\n            gridRef.current.api.onFilterChanged();\n        }\n        // Reset UI control states\n        setSiteFilter(\"\");\n        setStatusFilter(\"\");\n        setDateFilter(\"\");\n    };\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    //#region api\n    const updateSelectedRowStatus = (updatedStatusID, comment, params)=>{\n        if (updatedStatusID == 3) {\n            if (comment) {\n                setIsValidCancelReason(true);\n            } else {\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        console.log(\"sssssssssssssssssssssssssssssssssssss\", params);\n        const apiPayload = {\n            request_no: params.id,\n            ProductName: params.product_name,\n            Size: params.size,\n            Quantity: params.quantity,\n            NameForPrinting: params.NameForPrinting,\n            Comments: params.comment,\n            RequestItemID: params.request_item_id,\n            commentOnUpdatingRequest: comment,\n            action_id: updatedStatusID,\n            SubmitterUserID: userData.user_id,\n            SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n            orignatorEmail: params === null || params === void 0 ? void 0 : params.orignatorEmail\n        };\n        try {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-product-request-items/\").concat(params.id), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await logout();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n                getData().then((data)=>{\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.request_item_id),\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: row.comments,\n                            size: row.size,\n                            quantity: row.quantity,\n                            NameForPrinting: row.name_for_printing,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    setRequestRowData(formattedData);\n                    setCancelledReasonapi(\"\");\n                    setShowStatusDialog(false);\n                });\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_3__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                lineNumber: 726,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-20 md:mr-12 lg:mr-14\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                                children: [\n                                    (userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex rounded-full bg-gray-200 p-1 \\n                 \".concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\", \"\\n                \"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-1 rounded-full text-sm font-medium transition \".concat(selectedView === \"Orders\" ? \"bg-white shadow text-black\" : \"text-gray-500 hover:text-black\"),\n                                                    onClick: ()=>handleSelectedView(\"Orders\"),\n                                                    children: \"Requests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-1 rounded-full text-sm font-medium transition \".concat(selectedView === \"Products\" ? \"bg-white shadow text-black\" : \"text-gray-500 hover:text-black\"),\n                                                    onClick: ()=>{\n                                                        handleSelectedView(\"Products\"), handleClearFilters();\n                                                    },\n                                                    children: \"Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 734,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 733,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    orderView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        // style={{ marginBottom: \"10px\" }}\n                                        className: \"flex gap-4 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Site:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: siteFilter,\n                                                        onChange: (e)=>handleSiteFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"All Sites\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS1-Teynham\",\n                                                                children: \"ISS1-Teynham\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS2-Linton\",\n                                                                children: \"ISS2-Linton\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 785,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS3-Sittingbourne\",\n                                                                children: \"ISS3-Sittingbourne\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 786,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 775,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Status:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: statusFilter,\n                                                        onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"All Statuses\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 800,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Pending Review\",\n                                                                children: \"Pending\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Approved\",\n                                                                children: \"Approved\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Rejected\",\n                                                                children: \"Rejected\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Ordered\",\n                                                                children: \"Ordered\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 805,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Draft\",\n                                                                children: \"Draft\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 793,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Date:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 812,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: dateFilter,\n                                                        onChange: (e)=>handleDateFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 811,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleClearFilters,\n                                                className: \"ml-4 px-3 py-1 border rounded bg-gray-200 hover:bg-gray-300\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 821,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 771,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative block w-[47vh] text-gray-400  mt-0 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__.FontAwesomeIcon, {\n                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_16__.faSearch,\n                                                            className: \"fw-bold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"filter-text-box\",\n                                                        placeholder: \"Search...\",\n                                                        onInput: onFilterTextBoxChanged,\n                                                        value: searchInput,\n                                                        className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none shadow-[0_0_5px_rgba(0,0,0,0.1)]\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 834,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 830,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"ml-2 my-2 px-3 py-1 p-[6px] border rounded-md bg-skin-primary text-white text-sm cursor-pointer\",\n                                                onClick: ()=>{\n                                                    if (selectedView === \"Orders\") {\n                                                        router.push(\"/ppe-consumable/add\");\n                                                    } else {\n                                                        setShowAddProduct(true);\n                                                        setIsEditingProduct(false);\n                                                    }\n                                                },\n                                                children: orderView ? \"New Request\" : \"Add Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 843,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 829,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 729,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddProductDialog__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                open: showAddProduct,\n                                onOpenChange: (_, data)=>{\n                                    setShowAddProduct(data.open);\n                                    setIsEditingProduct(false);\n                                },\n                                onSubmit: (param)=>{\n                                    let { productName, printing, sizes } = param;\n                                    // handle your submit logic here\n                                    console.log({\n                                        productName,\n                                        printing,\n                                        sizes\n                                    });\n                                    setShowAddProduct(false);\n                                    router.reload();\n                                },\n                                isEditingProduct: isEditingProduct,\n                                editProductData: editProductData,\n                                productTypes: productTypes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 858,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative ag-theme-alpine !rounded-md\",\n                                    style: {\n                                        height: \"calc(100vh - 180px)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_5__.AgGridReact, {\n                                            rowData: requestRowData,\n                                            ref: gridRef,\n                                            requestColumnDefs: requestColumnDefs,\n                                            defaultColDef: defaultColDef,\n                                            suppressRowClickSelection: true,\n                                            pagination: true,\n                                            paginationPageSize: pageSize,\n                                            onPageSizeChanged: handlePageSizeChange,\n                                            tooltipShowDelay: 0,\n                                            tooltipHideDelay: 1000,\n                                            onGridReady: handleGridReady\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 880,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-start mt-2 pagination-style\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"page-size-select pagination\",\n                                                className: \"inputs\",\n                                                children: [\n                                                    \"Show\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"page-size-select\",\n                                                        onChange: handlePageSizeChange,\n                                                        value: pageSize,\n                                                        className: \"focus:outline-none\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 10,\n                                                                children: \"10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 902,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 15,\n                                                                children: \"15\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 903,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 25,\n                                                                children: \"25\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 904,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 50,\n                                                                children: \"50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 905,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 100,\n                                                                children: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 906,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" \",\n                                                    \"Entries\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 894,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 893,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                    lineNumber: 876,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 875,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                        lineNumber: 728,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.FluentProvider, {\n                        theme: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.webLightTheme,\n                        className: \"!bg-transparent\",\n                        style: {\n                            fontFamily: \"poppinsregular\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.Dialog, {\n                                open: showStatusDialog,\n                                onOpenChange: (_, data)=>{\n                                    if (!data.open) {\n                                        setIsValidCancelReason(true);\n                                        setCancelledReasonapi(\"\"); // Also clear the text\n                                    }\n                                    setShowStatusDialog(data.open);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogTrigger, {\n                                        disableButtonEnhancement: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"none\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 930,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 929,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogSurface, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogBody, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogTitle, {\n                                                    children: \"Update Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mb-6\",\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                statusAction === 2 ? \"approve\" : statusAction === 3 ? \"reject\" : statusAction === 4 ? \"mark as ordered\" : \"update\",\n                                                                \" \",\n                                                                \"this request ?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 936,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        statusAction === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                            rows: \"8\",\n                                                            value: cancelledReasonapi,\n                                                            onChange: (e)=>{\n                                                                setCancelledReasonapi(e.target.value);\n                                                                if (e.target.value) {\n                                                                    setIsValidCancelReason(true);\n                                                                }\n                                                            },\n                                                            onBlur: (e)=>{\n                                                                const trimmedValue = trimInputText(e.target.value);\n                                                                setCancelledReasonapi(trimmedValue);\n                                                            },\n                                                            placeholder: \"Provide reason for rejection...\",\n                                                            maxLength: \"500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 948,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        statusAction === 3 && !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"Please Provide reason for cancellation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 967,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"8px\",\n                                                                marginTop: \"16px\"\n                                                            },\n                                                            className: \"flex justify-end\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        setShowStatusDialog(false);\n                                                                        setCancelledReasonapi(\"\");\n                                                                    },\n                                                                    \"data-modal-hide\": \"default-modal\",\n                                                                    type: \"button\",\n                                                                    className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                    lineNumber: 975,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        // Handle status update logic here\n                                                                        console.log(\"Updating status:\", statusAction, selectedRowData);\n                                                                        updateSelectedRowStatus(statusAction, cancelledReasonapi, selectedRowData);\n                                                                    },\n                                                                    \"data-modal-hide\": \"default-modal\",\n                                                                    type: \"button\",\n                                                                    className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                                    children: \"Continue\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                    lineNumber: 983,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 971,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 935,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 933,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 932,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 919,\n                                columnNumber: 11\n                            }, undefined),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                        lineNumber: 914,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                lineNumber: 727,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(PPEConsumables, \"j80CPzQ/fhm9JozSpRdK+Ar7Bt0=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__.useLoading\n    ];\n});\n_c = PPEConsumables;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PPEConsumables);\n// Helper function to derive unit from package quantity\nfunction deriveUnitFromPackageQuantity(packageQuantity) {\n    if (!packageQuantity) return \"Unit\";\n    const lower = packageQuantity.toLowerCase();\n    if (lower.includes(\"box\")) return \"Box\";\n    if (lower.includes(\"pack\")) return \"Pack\";\n    if (lower.includes(\"pair\")) return \"Pair\";\n    return \"Unit\";\n}\nvar _c;\n$RefreshReg$(_c, \"PPEConsumables\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/ppe-consumable.js\n"));

/***/ })

});
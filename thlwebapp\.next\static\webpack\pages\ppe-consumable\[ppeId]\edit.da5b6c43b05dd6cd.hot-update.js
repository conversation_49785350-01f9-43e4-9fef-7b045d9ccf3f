"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable/[ppeId]/edit",{

/***/ "./components/PpeConsumable.js":
/*!*************************************!*\
  !*** ./components/PpeConsumable.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lodash/forEach */ \"./node_modules/lodash/forEach.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(lodash_forEach__WEBPACK_IMPORTED_MODULE_12__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    valueContainer: (provided)=>({\n            ...provided,\n            height: \"28px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: ()=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst emptyItem = {\n    product: null,\n    size: null,\n    quantity: \"\",\n    nameForPrinting: \"\",\n    comments: \"\"\n};\nconst PpeConsumable = (param)=>{\n    let { userData, pageType, sites, OrderRequestData, ppeId = null } = param;\n    var _currentItem_product, _currentItem_product_sizes, _currentItem_product1, _currentItem_product2, _currentItem_product3, _currentItem_product_sizes1, _currentItem_product4, _currentItem_product5;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [nameOfOriginator, setNameOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [emailOfOriginator, setEmailOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [siteData, setSiteData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            ...emptyItem\n        }\n    ]);\n    const [requiredDate, setRequiredDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [createdDate, setCreatedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [site, setSite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((sites === null || sites === void 0 ? void 0 : sites[0]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [productsData, setProductsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [currentItem, setCurrentItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        ...emptyItem\n    });\n    const [savedItems, setSavedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // console.log(\"dsksldibiasdbilsdbv\", siteData);\n    // Add validation states for current item\n    // const [productValid, setProductValid] = useState(\"\");\n    // const [quantityValid, setQuantityValid] = useState(\"\");\n    const [dateWarning, setDateWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // const [isEditMode, setIsEditMode] = useState();\n    const [requestStatus, setRequestStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const [requestStatusList, setRequestStatusList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [submitted, setSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popupType, setPopupType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [editingRowId, setEditingRowId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gridApi, setGridApi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const closeModal = (e)=>{\n        if (e) {\n            e.preventDefault();\n        }\n        setIsValidCancelReason(true);\n        setCancelledReasonapi(\"\");\n        setIsOpen(false);\n    };\n    //#region Load data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n        if (OrderRequestData || (OrderRequestData === null || OrderRequestData === void 0 ? void 0 : OrderRequestData.length) > 0) {\n            var _OrderRequestData_, _OrderRequestData_1, _OrderRequestData_2, _OrderRequestData_3;\n            // console.log(\"OrderRequestData\", OrderRequestData);\n            if ((_OrderRequestData_ = OrderRequestData[0]) === null || _OrderRequestData_ === void 0 ? void 0 : _OrderRequestData_.action_id) {\n                var _OrderRequestData_4, _OrderRequestData_5;\n                setRequestStatus((_OrderRequestData_4 = OrderRequestData[0]) === null || _OrderRequestData_4 === void 0 ? void 0 : _OrderRequestData_4.action_id);\n                if (((_OrderRequestData_5 = OrderRequestData[0]) === null || _OrderRequestData_5 === void 0 ? void 0 : _OrderRequestData_5.action_id) !== 5) {\n                    setSubmitted(true);\n                }\n            }\n            if ((_OrderRequestData_1 = OrderRequestData[0]) === null || _OrderRequestData_1 === void 0 ? void 0 : _OrderRequestData_1.site_id) {\n                var _OrderRequestData_6, _OrderRequestData_7;\n                setSite({\n                    label: (_OrderRequestData_6 = OrderRequestData[0]) === null || _OrderRequestData_6 === void 0 ? void 0 : _OrderRequestData_6.site_name,\n                    value: (_OrderRequestData_7 = OrderRequestData[0]) === null || _OrderRequestData_7 === void 0 ? void 0 : _OrderRequestData_7.site_id\n                });\n            }\n            if ((_OrderRequestData_2 = OrderRequestData[0]) === null || _OrderRequestData_2 === void 0 ? void 0 : _OrderRequestData_2.required_date) {\n                var _OrderRequestData_8, _OrderRequestData_9;\n                setRequiredDate(((_OrderRequestData_8 = OrderRequestData[0]) === null || _OrderRequestData_8 === void 0 ? void 0 : _OrderRequestData_8.required_date) ? new Date((_OrderRequestData_9 = OrderRequestData[0]) === null || _OrderRequestData_9 === void 0 ? void 0 : _OrderRequestData_9.required_date).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if ((_OrderRequestData_3 = OrderRequestData[0]) === null || _OrderRequestData_3 === void 0 ? void 0 : _OrderRequestData_3.created_at) {\n                var _OrderRequestData_10, _OrderRequestData_11;\n                setCreatedDate(((_OrderRequestData_10 = OrderRequestData[0]) === null || _OrderRequestData_10 === void 0 ? void 0 : _OrderRequestData_10.created_at) ? new Date((_OrderRequestData_11 = OrderRequestData[0]) === null || _OrderRequestData_11 === void 0 ? void 0 : _OrderRequestData_11.created_at).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if (OrderRequestData[0].user_name) {\n                // setNameOfOriginator();\n                setNameOfOriginator(OrderRequestData[0].user_name);\n            }\n            if (OrderRequestData[0].actioned_by_email) {\n                // setNameOfOriginator();\n                setEmailOfOriginator(OrderRequestData[0].actioned_by_email);\n            }\n            // setrowdata\n            if (OrderRequestData && Array.isArray(OrderRequestData)) {\n                console.log(\"load data\", OrderRequestData, requestStatus);\n                const STATUS_MAP = {\n                    1: \"Pending Review\",\n                    2: \"Approved\",\n                    3: \"Rejected\",\n                    4: \"Ordered\",\n                    5: \"Draft\"\n                };\n                const mappedItems = OrderRequestData.map((item)=>({\n                        id: item.order_request_items,\n                        product: {\n                            value: item.product_id,\n                            label: item.product_name\n                        },\n                        size: {\n                            value: item.size,\n                            label: item.size || \"N/A\"\n                        },\n                        quantity: item.quantity,\n                        nameForPrinting: item.name_for_printing,\n                        comments: (item.action_id === 3 ? \"Rejected Reason: \".concat(item.latest_comment) : item.comments) || \"\",\n                        updateFlag: 0,\n                        status: STATUS_MAP[item.action_id]\n                    }));\n                const uniqueValues = [\n                    ...new Set(OrderRequestData.map((item)=>item.action_id))\n                ];\n                setRequestStatusList(uniqueValues);\n                setSavedItems(mappedItems);\n                console.log(\"uniqueValues\", uniqueValues);\n            }\n        }\n    }, [\n        OrderRequestData\n    ]);\n    //#endregion Load data\n    //#region ag-grid\n    const onRowSelected = (event)=>{\n        if (event.node.isSelected()) {\n            setEditingRowId(event.data.id);\n            setCurrentItem(event.data);\n        }\n    };\n    const onGridReady = (params1)=>{\n        setGridApi(params1.api);\n    };\n    // inside your component\n    const getRowClass = (params1)=>{\n        return params1.data.id === editingRowId ? \"bg-blue-100\" : \"\";\n    };\n    const IconsRenderer = (props)=>{\n        const handleDelete = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n        };\n        const handleEdit = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            setHasUnsavedChanges(true);\n            setEditingRowId(selectedRow.id);\n            // setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n            const handleRowUpdate = (actionId)=>()=>{\n                    setSelectedRowData(params.data);\n                    setStatusAction(actionId);\n                    setShowStatusDialog(true);\n                };\n            // event.preventDefault();\n            // const selectedRow = props.data;\n            // setEditingRowId(selectedRow.id || props.node.id);\n            // // const updatedData = savedItems.filter((item) => item !== selectedRow);\n            // // setSavedItems(updatedData);\n            //   setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            // Populate the form with the selected row data\n            setCurrentItem({\n                id: selectedRow.id,\n                product: selectedRow.product,\n                size: selectedRow.size,\n                quantity: selectedRow.quantity,\n                nameForPrinting: selectedRow.nameForPrinting,\n                nameForPrintingFlag: selectedRow.nameForPrinting,\n                comments: selectedRow.comments,\n                updateFlag: 1\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-row gap-4 justify-center text-skin-primary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleEdit,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faPenToSquare\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDelete,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\", \" text-red-500\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faTrash\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Column definitions for the grid\n    const columnDefs = [\n        {\n            headerName: \"Product\",\n            field: \"product.label\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Size\",\n            field: \"size.label\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Quantity\",\n            field: \"quantity\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Name for Printing\",\n            field: \"nameForPrinting\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Comments\",\n            field: \"comments\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            hide: requestStatus === 5 || !requestStatus,\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: 1.25,\n            filterParams: {\n                values: [\n                    \"Pending Review\",\n                    \"Approved\",\n                    \"Rejected\",\n                    \"Ordered\",\n                    \"Draft\"\n                ]\n            }\n        },\n        {\n            headerName: \"Actions\",\n            cellRenderer: IconsRenderer,\n            headerClass: \"header-with-border\",\n            flex: 1\n        }\n    ];\n    //#endregion ag-grid\n    //#region api's\n    function getAllProducts() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-products\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to fetch products: \".concat(error.message));\n            throw error;\n        });\n    }\n    function getAllSites() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/sites\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        });\n    }\n    //#endregion api's\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const siteData = await getAllSites();\n                const formattedSites = siteData === null || siteData === void 0 ? void 0 : siteData.map((site)=>({\n                        value: site.id,\n                        label: site.name\n                    }));\n                setSiteData(formattedSites);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error fetching Sites\", error);\n            }\n        };\n        const fetchProducts = async ()=>{\n            try {\n                const productsJson = await getAllProducts();\n                const filterOutHiddenProducts = productsJson.filter((product)=>!product.IsProductHidden);\n                const formattedProducts = filterOutHiddenProducts === null || filterOutHiddenProducts === void 0 ? void 0 : filterOutHiddenProducts.map((product)=>({\n                        value: product.ProductId,\n                        label: product.ProductName,\n                        size_required: product.IsSizeRequired || product.size_required,\n                        sizes: product.AvailableSizes ? product.AvailableSizes.split(\", \").map((size)=>({\n                                value: size.toLowerCase().replace(/\\s+/g, \"\"),\n                                label: size\n                            })) : [],\n                        productCode: product.ProductCode,\n                        packageQuantity: product.PackageQuantity,\n                        productType: product.ProductType,\n                        name_printable: product.name_printable\n                    }));\n                setProductsData(formattedProducts);\n            } catch (error) {\n                console.error(\"Error fetching products:\", error);\n            }\n        };\n        fetchProducts();\n        fetchSites();\n        setLoading(false);\n    }, []);\n    const updateSelectedRowStatusForSingleItem = (updatedStatusID, comment, params1)=>{\n        if (updatedStatusID == 3) {\n            if (comment) {\n                setIsValidCancelReason(true);\n            } else {\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        console.log(\"sssssssssssssssssssssssssssssssssssss\", params1);\n        const apiPayload = {\n            request_no: params1.id,\n            item_number: params1.item_number,\n            ProductName: params1.product_name,\n            Size: params1.size,\n            Quantity: params1.quantity,\n            NameForPrinting: params1.NameForPrinting,\n            Comments: params1.comment,\n            RequestItemID: params1.request_item_id,\n            commentOnUpdatingRequest: comment,\n            action_id: updatedStatusID,\n            SubmitterUserID: userData.user_id,\n            SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n            orignatorEmail: params1 === null || params1 === void 0 ? void 0 : params1.orignatorEmail\n        };\n        try {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-product-request-items/\").concat(params1.id), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n                getData().then((data)=>{\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            item_number: row.item_number,\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: (row.action_id === 3 ? \"Rejected Reason: \".concat(row.latest_comment) : row.comments) || \"\",\n                            size: row.size,\n                            quantity: row.quantity,\n                            NameForPrinting: row.name_for_printing,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    setRequestRowData(formattedData);\n                    setCancelledReasonapi(\"\");\n                // closeModal();\n                // setShowStatusDialog(false);\n                });\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    //#region Validate\n    // Update the validate function to use savedItems instead of items\n    const validate = ()=>{\n        const newErrors = {};\n        if (!site) newErrors.site = \"Site is required.\";\n        if (!requiredDate) newErrors.requiredDate = \"Required date is missing.\";\n        if (!savedItems || savedItems.length === 0) {\n            newErrors.savedItems = \"At least one item must be added.\";\n        }\n        // Update state for field-level messages\n        setErrors(newErrors);\n        const hasErrors = Object.keys(newErrors).length > 0;\n        if (hasErrors) {\n            const errorMessages = Object.values(newErrors);\n            // setPopupConfig({\n            //   title: \"Validation Error\",\n            //   message: errorMessages.join(\"\\n\"),\n            //   type: \"error\",\n            // });\n            // setShowPopup(true); // trigger popup\n            const errorsLength = Object.keys(newErrors).length;\n            if (errorsLength > 1) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill all required fields.\");\n            } else if (errorsLength == 1 && newErrors.savedItems) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please add at least one item.\");\n            }\n            return false;\n        }\n        return true;\n    };\n    const handleSiteAdd = (value)=>{\n        setSite(value);\n        // Clear the error for site if value is valid\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (value) {\n                delete updated.site;\n            }\n            return updated;\n        });\n    };\n    // Add date validation function\n    const validateRequiredDate = (selectedDate)=>{\n        // setRequiredDate(selectedDate);\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (selectedDate) {\n                delete updated.requiredDate;\n            }\n            return updated;\n        });\n        if (!selectedDate) {\n            setDateWarning(\"\");\n            return;\n        }\n        const today = new Date();\n        const selected = new Date(selectedDate);\n        const twoWeeksFromNow = new Date();\n        twoWeeksFromNow.setDate(today.getDate() + 14);\n        if (selected < twoWeeksFromNow) {\n            setDateWarning(\"Notice: Less than 2 weeks lead time may affect availability.\");\n        } else {\n            setDateWarning(\"\");\n        }\n    };\n    // #region handlers\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    const handleCancelReason = (data)=>{\n        if (data) {\n            setIsValidCancelReason(true);\n        } else {\n            setIsValidCancelReason(false);\n        }\n    };\n    const handleSaveClick = (e)=>{\n        // e.preventDefault();\n        if (hasUnsavedChanges) {\n            setPopupType(\"unsavedWarning\");\n            setIsOpen(true); // your existing modal state\n        } else {\n            saveCurrentItem(e);\n        }\n    };\n    const saveCurrentItem = (e)=>{\n        var _currentItem_product, _currentItem_product_sizes, _currentItem_product1;\n        console.log(\"save order request\");\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (e) {\n                delete updated.savedItems;\n            }\n            return updated;\n        });\n        if (!e) {\n            console.log(\"returning here\");\n            setDateWarning(\"\");\n            return;\n        }\n        e.preventDefault();\n        let count = 0;\n        if (!currentItem.product) {\n            console.log(\"++ product\");\n            count++;\n        // setProductValid(\"Please select a product\");\n        } else {\n        // setProductValid(\"\");\n        }\n        if (!currentItem.quantity || currentItem.quantity < 1) {\n            console.log(\"++ quantity\");\n            count++;\n        // setQuantityValid(\"Please enter a valid quantity\");\n        } else {\n            if (currentItem.quantity > 1 && currentItem.nameForPrinting) {\n                // currentItem.nameForPrinting=\"\"\n                setCurrentItem((prev)=>({\n                        ...prev,\n                        nameForPrinting: \"\"\n                    }));\n            }\n        // setQuantityValid(\"\");\n        }\n        if (((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) && ((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) && !currentItem.size) {\n            console.log(\"++ product size\");\n            count++;\n        // Add size validation if needed\n        }\n        if (count > 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill in all mandatory fields to add items with valid quantity\");\n            return;\n        }\n        //     if (editingRowId) {\n        //         // Update existing item\n        //   const updatedItems = savedItems.map(item =>\n        //     item.id === editingRowId ? { ...currentItem, id: editingRowId } : item\n        //   );\n        //   setSavedItems(updatedItems);\n        //   setEditingRowId(null);\n        // } else {\n        //   // Add new item\n        //   setSavedItems([...savedItems, { ...currentItem, id: Date.now() }]);\n        // }\n        if (editingRowId) {\n            // Add the edited item back to savedItems\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: editingRowId,\n                    product: currentItem.product ? {\n                        ...currentItem.product\n                    } : null,\n                    size: currentItem.size ? {\n                        ...currentItem.size\n                    } : null\n                }\n            ]);\n            setEditingRowId(null);\n        } else {\n            // Add new item\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: \"row-\".concat(Date.now())\n                }\n            ]);\n        }\n        // Reset current item\n        setCurrentItem({\n            ...emptyItem\n        });\n        setEditingRowId(null);\n        setHasUnsavedChanges(false);\n    };\n    // Handle current item changes\n    const handleCurrentItemChange = (field, value)=>{\n        if (field === \"product\") {\n            // console.log(\"field-------------\",value)\n            // Reset other fields when product changes\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    size: null,\n                    quantity: \"\",\n                    nameForPrinting: \"\",\n                    nameForPrintingFlag: \"\",\n                    comments: \"\"\n                }));\n        } else if (field === \"quantity\" && value > 1) {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    nameForPrinting: \"\"\n                }));\n        } else {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value\n                }));\n        }\n    };\n    //#region Save\n    const handleSubmit = (status_ID)=>{\n        if (status_ID != 1 && !validate()) {\n            return;\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssssssssssssssssssss\",savedItems);\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        // Transform savedItems to API format\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size;\n            return {\n                requestItemId: item.requestItemId,\n                // RequestItemID: typeof item.id === 'string' && item.id.startsWith('row-') ? null : item.id,\n                ProductID: (_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value,\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            Status_id: status_ID,\n            SubmitterUserID: userData === null || userData === void 0 ? void 0 : userData.user_id,\n            TargetSiteID: (site === null || site === void 0 ? void 0 : site.value) || 3,\n            RequiredDate: requiredDate,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            items: items,\n            orignatorEmail: emailOfOriginator\n        };\n        // console.log(\"items--------------------------------------------------------\",items)\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/create-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    // setLoading(false);\n    // setTimeout(() => {\n    //   setLoading(false);\n    //   toast.success(\"Request submitted!\");\n    //   router.push(\"/ppe-consumable\");\n    // }, 1000);\n    };\n    //#region updated conditional\n    const updateSelectedRowStatus = (updatedStatusID, existingStatusId)=>{\n        const STATUS_MAP = {\n            1: \"Pending Review\",\n            2: \"Approved\",\n            3: \"Rejected\",\n            4: \"Ordered\",\n            5: \"Draft\"\n        };\n        const toBeUpdated = savedItems.filter((item)=>item.status == STATUS_MAP[existingStatusId]);\n        if (toBeUpdated.length === 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"No items found with the specified status\");\n            return;\n        }\n        setLoading(true);\n        if (updatedStatusID == 3) {\n            if (cancelledReasonapi) {\n                setIsValidCancelReason(true);\n                console.log(\"triggerrerererered\", cancelledReasonapi);\n            } else {\n                console.log(\"triggerrerererered\");\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssss\", toBeUpdated);\n        const updatePromises = toBeUpdated.map(async (item)=>{\n            const apiPayload = {\n                request_no: item.id,\n                item_number: item.item_number,\n                ProductName: item.product.label,\n                Size: item.size.label,\n                Quantity: item.quantity,\n                NameForPrinting: item.nameForPrinting,\n                Comments: updatedStatusID == 3 ? cancelledReasonapi : item.comments,\n                RequestItemID: item.id,\n                commentOnUpdatingRequest: cancelledReasonapi,\n                action_id: updatedStatusID,\n                SubmitterUserID: userData.user_id,\n                SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n                orignatorEmail: emailOfOriginator,\n                cancelledReasonapi: cancelledReasonapi\n            };\n            console.log(\"apiPayload\", apiPayload); // Log for debugging purposes.\n            const res = await fetch(\"\".concat(_services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress, \"ppe-consumables/update-product-request-items/\").concat(item.id), {\n                method: \"POST\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            });\n            if (res.status === 200) {\n                return res.json();\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    router.push(redirectUrl);\n                }, 3000);\n                throw new Error(\"Unauthorized\");\n            } else {\n                const errorText = await res.text();\n                console.error(\"Update failed:\", errorText);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to update item status.\");\n                throw new Error(\"Update failed\");\n            }\n        });\n        Promise.all(updatePromises).then((results)=>{\n            const successCount = results.filter((result)=>result === null || result === void 0 ? void 0 : result.msg).length;\n            if (successCount > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Successfully updated \".concat(successCount, \" item(s)\"), {\n                    position: \"top-right\"\n                });\n                setTimeout(()=>{\n                    router.replace(\"/ppe-consumable\");\n                }, 2000);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"No items were successfully updated\");\n                router.push(\"/ppe-consumable\");\n            }\n        }).catch((error)=>{\n            console.error(\"Error updating items:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Some items failed to update\");\n        }).finally(()=>{\n            setLoading(false);\n        });\n    };\n    //#endregion updated conditional\n    // #region update\n    const handleUpdate = (action_id)=>{\n        if (!validate()) {\n            return;\n        }\n        // handleSaveClick();\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        console.log(\"savedItems\", savedItems);\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size, _item_size1;\n            return {\n                // RequestItemID: item.id,\n                RequestItemID: typeof item.id === \"string\" && item.id.startsWith(\"row-\") ? null : item.id,\n                ProductID: item.product_id || ((_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value),\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                // SizeID: item.size?.value || null, //TODO no size handel\n                SizeID: ((_item_size1 = item.size) === null || _item_size1 === void 0 ? void 0 : _item_size1.value) && !isNaN(Number(item.size.value)) ? Number(item.size.value) : null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            requestId: ppeId,\n            action_id: action_id,\n            submitterUserId: userData.user_id,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            targetSiteId: site.value,\n            requiredDate: requiredDate,\n            items: items,\n            comment: cancelledReasonapi,\n            orignatorEmail: emailOfOriginator\n        };\n        console.log(\"apiPayload--------------------------------------------------------\", apiPayload);\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n        setLoading(false);\n        setTimeout(()=>{\n            setLoading(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Request submitted!\");\n            router.push(\"/ppe-consumable\");\n        }, 1000);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"update\") {\n            setIsEdit(true);\n        }\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n    }, [\n        0\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"flex flex-col justify-start max-w-full  mx-auto mr-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row gap-8 bg-white p-6 rounded-lg shadow-[0_0_1px_rgba(0,0,0,0.1)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1135,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: nameOfOriginator || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1136,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1134,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1144,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: emailOfOriginator || (userData === null || userData === void 0 ? void 0 : userData.email) || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1145,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1143,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Created Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1153,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: new Date().toLocaleDateString(\"en-CA\"),\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1154,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1152,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Site\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500 ml-[2px]\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1163,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1162,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            value: site,\n                                            onChange: (e)=>{\n                                                setSite();\n                                                handleSiteAdd(e);\n                                            },\n                                            options: siteData,\n                                            isDisabled: (sites === null || sites === void 0 ? void 0 : sites.length) === 1 || submitted,\n                                            styles: customSelectStyles,\n                                            isClearable: true,\n                                            className: \" w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1165,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        errors.site && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.site\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1177,\n                                            columnNumber: 31\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1161,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Required Date \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1181,\n                                                    columnNumber: 31\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1180,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            min: new Date().toISOString().split(\"T\")[0],\n                                            value: requiredDate,\n                                            onChange: (e)=>{\n                                                setRequiredDate(e.target.value);\n                                                validateRequiredDate(e.target.value);\n                                            },\n                                            className: \"block w-full px-4 border rounded-lg form-input\",\n                                            disabled: submitted\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1183,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        dateWarning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-orange-500 text-sm\",\n                                            children: dateWarning\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1195,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.requiredDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.requiredDate\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1198,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1179,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1133,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1127,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row justify-between items-end my-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"flex mb-1 font-semibold tracking-wider text-base pl-2\",\n                                    children: \"Items\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1218,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1217,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col mb-28px bg-white p-6 rounded-lg py-8 shadow-[0_0_2px_rgba(0,0,0,0.2)]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Product \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1227,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1226,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        value: currentItem.product,\n                                                        onChange: (val)=>handleCurrentItemChange(\"product\", val),\n                                                        options: productsData,\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1229,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1225,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Size\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 \".concat(!((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) || !((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) || submitted ? \"hidden\" : \"\"),\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1243,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1241,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        value: currentItem.size,\n                                                        onChange: (val)=>handleCurrentItemChange(\"size\", val),\n                                                        options: ((_currentItem_product2 = currentItem.product) === null || _currentItem_product2 === void 0 ? void 0 : _currentItem_product2.sizes) || [],\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: !((_currentItem_product3 = currentItem.product) === null || _currentItem_product3 === void 0 ? void 0 : _currentItem_product3.size_required) || !((_currentItem_product4 = currentItem.product) === null || _currentItem_product4 === void 0 ? void 0 : (_currentItem_product_sizes1 = _currentItem_product4.sizes) === null || _currentItem_product_sizes1 === void 0 ? void 0 : _currentItem_product_sizes1.length) || submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1255,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1240,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Quantity \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1272,\n                                                                columnNumber: 28\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1271,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        // min={0}\n                                                        max: 999,\n                                                        value: currentItem.quantity,\n                                                        onChange: (e)=>handleCurrentItemChange(\"quantity\", e.target.value > 999 ? 999 : parseInt(e.target.value)),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1274,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1270,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Name for Printing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1290,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        maxLength: \"50\" //TODO change in db also\n                                                        ,\n                                                        value: currentItem.nameForPrinting,\n                                                        onChange: (e)=>handleCurrentItemChange(\"nameForPrinting\", e.target.value),\n                                                        disabled: currentItem.quantity !== 1 || submitted || !((_currentItem_product5 = currentItem.product) === null || _currentItem_product5 === void 0 ? void 0 : _currentItem_product5.name_printable),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        placeholder: \"Only if quantity = 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1291,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1289,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Comments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1308,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        value: currentItem.comments,\n                                                        maxLength: \"500\",\n                                                        onChange: (e)=>handleCurrentItemChange(\"comments\", e.target.value),\n                                                        className: \"disabled:text-gray-400 disabled:bg-[#F6F3F3] block w-full h-8 px-4 border rounded-lg form-input resize-none\",\n                                                        rows: 1,\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1309,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1307,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    onClick: saveCurrentItem,\n                                                    // onClick={handleSaveClick}\n                                                    className: \"px-2 py-1 2xl:px-3.5 border border-skin-primary rounded-md text-skin-primary cursor-pointer \".concat(submitted ? \"hidden\" : \"\"),\n                                                    disabled: submitted,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faFloppyDisk\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1330,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1322,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1320,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1224,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ag-theme-alpine my-[24px]\",\n                                        style: {\n                                            height: 250,\n                                            width: \"100%\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__.AgGridReact, {\n                                            ref: gridRef,\n                                            columnDefs: columnDefs,\n                                            rowData: savedItems,\n                                            rowHeight: 35,\n                                            getRowClass: getRowClass,\n                                            onGridReady: onGridReady\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1341,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1337,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    popupType === \"unsavedWarning\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"One item is being edited. If you continue, changes will be lost.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1359,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: closeModal,\n                                                        className: \"border px-4 py-2 rounded\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1364,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            saveCurrentItem(); // discard editing row\n                                                            closeModal();\n                                                        },\n                                                        className: \"bg-blue-600 text-white px-4 py-2 rounded\",\n                                                        children: \"Save Anyway\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1370,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1363,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1358,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1223,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1216,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end gap-4 rounded-lg  p-4 mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    router.push(\"/ppe-consumable\");\n                                },\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6\",\n                                disabled: loading,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1386,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (pageType === \"edit\") {\n                                        console.log(\"hasUnsavedChanges\", hasUnsavedChanges);\n                                        if (hasUnsavedChanges) {\n                                            setPopupType(\"Save\");\n                                            setIsOpen(true);\n                                        } else {\n                                            handleUpdate(5);\n                                        }\n                                    } else {\n                                        handleSubmit(5);\n                                    }\n                                },\n                                disabled: loading || requestStatus === 1,\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6 \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1396,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (hasUnsavedChanges) {\n                                        setPopupType(\"submitWhileEditing\");\n                                        setIsOpen(true);\n                                    } else {\n                                        if (!validate()) return;\n                                        setPopupType(\"submit\");\n                                        setIsOpen(true);\n                                    }\n                                },\n                                className: \"border border-skin-primary bg-skin-primary text-white rounded-md px-6 font-medium \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                disabled: loading || requestStatus === 1,\n                                children: \"Submit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1418,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"reject\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-red-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Reject All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1437,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"approve\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Approve All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1456,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                // onClick={() => {\n                                //   updateSelectedRowStatus(4, 2);\n                                // }}\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"markOrdered\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(requestStatusList.includes(2) ? \"\" : \"hidden\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Mark All Approved Requests to Ordered\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1475,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1385,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1123,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition, {\n                appear: true,\n                show: isOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: (value)=>{\n                        if (popupType !== \"reject\" || isValidCancelReason) {\n                            closeModal();\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1520,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1511,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                            lineNumber: 1541,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1540,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Confirm \".concat(popupType == \"submit\" ? \"Submission\" : popupType == \"reject\" ? \"Rejection\" : popupType == \"approve\" ? \"Approval\" : popupType == \"markOrdered\" ? \"markOrdered\" : popupType == \"Save\" ? \"Save\" : popupType == \"submitWhileEditing\" ? \"Submission\" : \" \")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1539,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeModal,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1565,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1559,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1538,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                popupType == \"submit\" ? \"submit\" : popupType == \"reject\" ? \"reject\" : popupType == \"approve\" ? \"approve\" : popupType == \"markOrdered\" ? \"mark approved items as ordered\" : popupType == \"Save\" ? \"save? there is a item being edited,\\n it will be lost if you Save \" : popupType == \"submitWhileEditing\" ? \"Submit? there is a item being edited,\\n it will be lost if you Submit \" : \" \",\n                                                                \" \",\n                                                                \"this request?\",\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1573,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        popupType == \"reject\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                    className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                                    rows: \"8\",\n                                                                    value: cancelledReasonapi,\n                                                                    onChange: (e)=>{\n                                                                        setCancelledReasonapi(e.target.value);\n                                                                        if (e.target.value) {\n                                                                            setIsValidCancelReason(true);\n                                                                        }\n                                                                    },\n                                                                    onBlur: (e)=>{\n                                                                        const trimmedValue = trimInputText(e.target.value);\n                                                                        setCancelledReasonapi(trimmedValue);\n                                                                    },\n                                                                    placeholder: \"Provide reason for rejection...\",\n                                                                    maxLength: \"500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1593,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                popupType == \"reject\" && !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"Please Provide reason for cancellation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1613,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1591,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1572,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                closeModal(), setCancelledReasonapi(\"\");\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1622,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                if (popupType === \"reject\" && !(cancelledReasonapi === null || cancelledReasonapi === void 0 ? void 0 : cancelledReasonapi.trim())) {\n                                                                    setIsValidCancelReason(false);\n                                                                    return;\n                                                                }\n                                                                if (pageType == \"add\") {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleSubmit(1);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleSubmit(1);\n                                                                    }\n                                                                } else {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleUpdate(1);\n                                                                    } else if (popupType === \"reject\") {\n                                                                        updateSelectedRowStatus(3, 1);\n                                                                    } else if (popupType === \"approve\") {\n                                                                        updateSelectedRowStatus(2, 1);\n                                                                    } else if (popupType === \"Save\") {\n                                                                        handleUpdate(5);\n                                                                    } else if (popupType === \"markOrdered\") {\n                                                                        updateSelectedRowStatus(4, 2);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleUpdate(1);\n                                                                    }\n                                                                }\n                                                                setCancelledReasonapi(\"\");\n                                                                closeModal();\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center h-full border border-skin-primary\",\n                                                            children: \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1632,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1621,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1536,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1534,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1525,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1524,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1523,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 1502,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1501,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(PpeConsumable, \"O68D4V6/DUUfJI1W3US3XF3lslU=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = PpeConsumable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PpeConsumable);\nvar _c;\n$RefreshReg$(_c, \"PpeConsumable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/PpeConsumable.js\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable",{

/***/ "./pages/ppe-consumable.js":
/*!*********************************!*\
  !*** ./pages/ppe-consumable.js ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _components_AddProductDialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/AddProductDialog */ \"./components/AddProductDialog.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var _utils_renderer_ppeActionRenderer__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/renderer/ppeActionRenderer */ \"./utils/renderer/ppeActionRenderer.js\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _utils_renderer_ppeActiveRenderer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/renderer/ppeActiveRenderer */ \"./utils/renderer/ppeActiveRenderer.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst PPEConsumables = (param)=>{\n    let { userData } = param;\n    _s();\n    // console.log(\"PARETN component rendered\", userData);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    // const [rowData, setRowData] = useState([]);\n    const [requestRowData, setRequestRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__.useLoading)();\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [orderView, setOrderView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [productTypes, setProductTypes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    //#region pop up states\n    const [showAddProduct, setShowAddProduct] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isEditingProduct, setIsEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editProductData, setEditProductData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Controlled filter states\n    const [siteFilter, setSiteFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [dateFilter, setDateFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showStatusDialog, setShowStatusDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedRowData, setSelectedRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [statusAction, setStatusAction] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Load saved view from localStorage on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Only run on client side\n        if (true) {\n            const savedView = localStorage.getItem(\"ppeSelectedView\");\n            if (savedView && (savedView === \"Orders\" || savedView === \"Products\")) {\n                setSelectedView(savedView);\n                setOrderView(savedView === \"Orders\");\n            } else {\n                setSelectedView(\"Orders\");\n                setOrderView(true);\n            }\n        }\n    }, []); // Empty dependency array - runs only on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!selectedView) return;\n        const fetchProducts = async ()=>{\n            if (selectedView === \"Orders\") {\n                getData().then((data)=>{\n                    console.log(\"data\", data);\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            item_number: row.item_number,\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: (row.action_id === 3 ? \"Rejected Reason: \".concat(row.latest_comment) : row.comments) || \"\",\n                            NameForPrinting: row.name_for_printing,\n                            size: row.size,\n                            quantity: row.quantity,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    // console.log(\"Formatted Data:\", formattedData);\n                    // setRowData(formattedData);\n                    setRequestRowData(formattedData);\n                });\n            } else {\n                const [productsJson, productTypesJson] = await Promise.all([\n                    getAllProducts(),\n                    getProductTypes()\n                ]);\n                // console.log(\"Fetched Products JSON:\", productsJson);\n                // console.log(\"Fetched Product Types JSON:\", productTypesJson);\n                // Save product types for later use (e.g., filters or dialogs)\n                if (Array.isArray(productTypesJson)) {\n                    setProductTypes(productTypesJson);\n                } else {\n                    setProductTypes([]);\n                }\n                // console.log(\"Fetched Products JSON:\", productsJson);\n                if (productsJson && productsJson.length > 0) {\n                    // console.log(\n                    //   \"Fetched Products JSON inside code execution\",\n                    //   productsJson\n                    // );\n                    const formattedProductData = productsJson.map((product)=>({\n                            product_id: product.ProductId,\n                            product_name: product.ProductName || \"Unnamed Product\",\n                            category: product.ProductType || \"Uncategorized\",\n                            type_id: product.TypeId,\n                            stock: 0,\n                            sizes: product.AvailableSizes || \"One Size\",\n                            unit: deriveUnitFromPackageQuantity(product.PackageQuantity),\n                            name_printable: product.name_printable,\n                            IsProductHidden: product.IsProductHidden\n                        }));\n                    // setRowData(formattedProductData);\n                    setRequestRowData(formattedProductData);\n                } else {\n                    const fallbackProductData = [\n                        {\n                            product_name: \"Nitrile Gloves\",\n                            category: \"Gloves\",\n                            stock: 1200,\n                            unit: \"Box\",\n                            status: \"Available\"\n                        }\n                    ];\n                // setRowData(fallbackProductData);\n                }\n            }\n        };\n        fetchProducts();\n    }, [\n        selectedView\n    ]);\n    // #region get data\n    const getData = async ()=>{\n        // setRowData([]);\n        setRequestRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        console.log(\"\");\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-ppe-requests\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            console.error(error);\n        });\n    };\n    async function getAllProducts() {\n        // setRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-products\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to fetch products: \".concat(error.message));\n            throw error;\n        });\n    }\n    async function getProductTypes() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/get-product_types\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch product types\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to fetch product types: \".concat(error.message));\n            throw error;\n        });\n    }\n    //#region column def\n    // Example column definitions for Products\n    // Replace with proper filter handling\n    const handleSiteFilterChange = (value)=>{\n        setSiteFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"site_name\");\n            if (filterInstance) {\n                if (value) {\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        filter: value\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"status\");\n            if (filterInstance) {\n                if (value) {\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        filter: value\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    const handleSelectedView = (selectedType)=>{\n        setSelectedView(selectedType);\n        setOrderView(selectedType === \"Orders\");\n        // Only save to localStorage on client side\n        if (true) {\n            localStorage.setItem(\"ppeSelectedView\", selectedType);\n        }\n    };\n    const handleDateFilterChange = (value)=>{\n        setDateFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"required_date\");\n            if (filterInstance) {\n                if (value) {\n                    const selectedDate = new Date(value);\n                    const yyyy = selectedDate.getFullYear();\n                    const mm = String(selectedDate.getMonth() + 1).padStart(2, \"0\");\n                    const dd = String(selectedDate.getDate()).padStart(2, \"0\");\n                    const formattedDate = \"\".concat(yyyy, \"-\").concat(mm, \"-\").concat(dd);\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        dateFrom: formattedDate\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    // Example column definitions\n    const requestColumnDefs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>[\n            {\n                headerName: \"Request ID\",\n                field: \"request_id\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Required Date\",\n                field: \"required_date\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agDateColumnFilter\",\n                filterParams: {\n                    comparator: (filterLocalDateAtMidnight, cellValue)=>{\n                        const dateAsString = cellValue;\n                        if (!dateAsString) return 0;\n                        const cellDate = new Date(dateAsString);\n                        const cellDateAtMidnight = new Date(cellDate);\n                        cellDateAtMidnight.setHours(0, 0, 0, 0);\n                        if (cellDateAtMidnight < filterLocalDateAtMidnight) {\n                            return -1;\n                        } else if (cellDateAtMidnight > filterLocalDateAtMidnight) {\n                            return 1;\n                        } else {\n                            return 0;\n                        }\n                    }\n                },\n                valueFormatter: (params)=>{\n                    return params.value ? new Date(params.value).toLocaleDateString() : \"\";\n                }\n            },\n            {\n                headerName: \"Site\",\n                field: \"site_name\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\",\n                filterParams: {\n                    values: [\n                        \"ISS1-Teynham\",\n                        \"ISS2-Linton\",\n                        \"ISS3-Sittingbourne\"\n                    ]\n                }\n            },\n            {\n                headerName: \"Product\",\n                field: \"product_name\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Size\",\n                field: \"size\",\n                flex: 0.75,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Qty\",\n                field: \"quantity\",\n                flex: 0.75,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Requestor\",\n                field: \"requestor\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Comment\",\n                field: \"comment\",\n                flex: 1,\n                hide: !orderView\n            },\n            {\n                headerName: \"Status\",\n                field: \"status\",\n                hide: !orderView,\n                cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                cellStyle: ()=>({\n                        justifyContent: \"center\"\n                    }),\n                flex: 1.25,\n                filterParams: {\n                    values: [\n                        \"Pending Review\",\n                        \"Approved\",\n                        \"Rejected\",\n                        \"Ordered\",\n                        \"Draft\"\n                    ]\n                }\n            },\n            //product view\n            {\n                headerName: \"Product Name\",\n                field: \"product_name\",\n                flex: 1,\n                hide: orderView\n            },\n            {\n                headerName: \"Category\",\n                field: \"category\",\n                flex: 0.5,\n                hide: orderView\n            },\n            {\n                headerName: \"Sizes\",\n                field: \"sizes\",\n                flex: 0.5,\n                hide: orderView\n            },\n            {\n                headerName: \"Name Printing\",\n                field: \"name_printable\",\n                flex: 0.5,\n                hide: orderView,\n                cellRenderer: \"agCheckboxCellRenderer\",\n                cellRendererParams: {\n                    disabled: true\n                },\n                cellStyle: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\"\n                },\n                headerClass: \"ag-header-cell-centered\"\n            },\n            {\n                headerName: \"Status\",\n                // field: \"site_id\",\n                flex: 0.5,\n                cellRenderer: (params)=>(0,_utils_renderer_ppeActiveRenderer__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(params, setShowAddProduct, orderView, setIsEditingProduct, setEditProductData, //banana\n                    setShowStatusDialog, setSelectedRowData, setStatusAction),\n                sortable: false,\n                hide: orderView\n            },\n            {\n                headerName: \"Actions\",\n                field: \"site_id\",\n                flex: 1,\n                cellRenderer: (params)=>(0,_utils_renderer_ppeActionRenderer__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(params, setShowAddProduct, orderView, setIsEditingProduct, setEditProductData, //banana\n                    setShowStatusDialog, setSelectedRowData, setStatusAction, userData),\n                sortable: false\n            }\n        ], [\n        orderView\n    ]);\n    // console.log(\"order vbiew\", orderView);\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: true,\n            filterParams: {\n                debounceMs: 300\n            },\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }), []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsLoading(false);\n    // console.log(\n    //   \"Selected View:\",\n    //   selectedView,\n    //   \"Order View:\",\n    //   orderView,\n    //   selectedView === \"Orders\"\n    // );\n    }, [\n        selectedView\n    ]);\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(requestColumnDefs);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (gridRef.current && gridRef.current.api) {\n            gridRef.current.api.setColumnDefs(requestColumnDefs);\n            // Refresh the grid to apply new column definitions\n            gridRef.current.api.refreshHeader();\n            gridRef.current.api.refreshCells();\n        }\n    }, [\n        requestColumnDefs,\n        orderView\n    ]); // Add orderView dependency\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n        setSearchInput(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const handleClearFilters = ()=>{\n        // Clear AG Grid filters\n        if (gridRef.current && gridRef.current.api) {\n            gridRef.current.api.setFilterModel(null);\n            gridRef.current.api.onFilterChanged();\n        }\n        // Reset UI control states\n        setSiteFilter(\"\");\n        setStatusFilter(\"\");\n        setDateFilter(\"\");\n    };\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    //#region api\n    const updateSelectedRowStatus = (updatedStatusID, comment, params)=>{\n        if (updatedStatusID == 3) {\n            if (comment) {\n                setIsValidCancelReason(true);\n            } else {\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        console.log(\"sssssssssssssssssssssssssssssssssssss\", params);\n        const apiPayload = {\n            request_no: params.id,\n            item_number: params.item_number,\n            ProductName: params.product_name,\n            Size: params.size,\n            Quantity: params.quantity,\n            NameForPrinting: params.NameForPrinting,\n            Comments: params.comment,\n            RequestItemID: params.request_item_id,\n            commentOnUpdatingRequest: comment,\n            action_id: updatedStatusID,\n            SubmitterUserID: userData.user_id,\n            SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n            orignatorEmail: params === null || params === void 0 ? void 0 : params.orignatorEmail\n        };\n        try {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-product-request-items/\").concat(params.id), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n                getData().then((data)=>{\n                    console.log(\"data\", data);\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            item_number: row.item_number,\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: (row.action_id === 3 ? \"Rejected Reason: \".concat(row.comments) : row.comments) || \"\",\n                            size: row.size,\n                            quantity: row.quantity,\n                            NameForPrinting: row.name_for_printing,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    setRequestRowData(formattedData);\n                    setCancelledReasonapi(\"\");\n                    setShowStatusDialog(false);\n                });\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_3__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                lineNumber: 728,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-20 md:mr-12 lg:mr-14\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                                children: [\n                                    (userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex rounded-full bg-gray-200 p-1 \\n                 \".concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\", \"\\n                \"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-1 rounded-full text-sm font-medium transition \".concat(selectedView === \"Orders\" ? \"bg-white shadow text-black\" : \"text-gray-500 hover:text-black\"),\n                                                    onClick: ()=>handleSelectedView(\"Orders\"),\n                                                    children: \"Requests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-1 rounded-full text-sm font-medium transition \".concat(selectedView === \"Products\" ? \"bg-white shadow text-black\" : \"text-gray-500 hover:text-black\"),\n                                                    onClick: ()=>{\n                                                        handleSelectedView(\"Products\"), handleClearFilters();\n                                                    },\n                                                    children: \"Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 736,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 735,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    orderView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        // style={{ marginBottom: \"10px\" }}\n                                        className: \"flex gap-4 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Site:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: siteFilter,\n                                                        onChange: (e)=>handleSiteFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"All Sites\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS1-Teynham\",\n                                                                children: \"ISS1-Teynham\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 786,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS2-Linton\",\n                                                                children: \"ISS2-Linton\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 787,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS3-Sittingbourne\",\n                                                                children: \"ISS3-Sittingbourne\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 788,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 777,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Status:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 796,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: statusFilter,\n                                                        onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Pending Review\",\n                                                                children: \"Pending\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Approved\",\n                                                                children: \"Approved\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Rejected\",\n                                                                children: \"Rejected\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 805,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Ordered\",\n                                                                children: \"Ordered\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Draft\",\n                                                                children: \"Draft\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 807,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 797,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 795,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Date:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: dateFilter,\n                                                        onChange: (e)=>handleDateFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 814,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 812,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleClearFilters,\n                                                className: \"ml-4 px-3 py-1 border rounded bg-gray-200 hover:bg-gray-300\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 822,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 773,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative block w-[47vh] text-gray-400  mt-0 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__.FontAwesomeIcon, {\n                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_17__.faSearch,\n                                                            className: \"fw-bold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 833,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 832,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"filter-text-box\",\n                                                        placeholder: \"Search...\",\n                                                        onInput: onFilterTextBoxChanged,\n                                                        value: searchInput,\n                                                        className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none shadow-[0_0_5px_rgba(0,0,0,0.1)]\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 831,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"ml-2 my-2 px-3 py-1 p-[6px] border rounded-md bg-skin-primary text-white text-sm cursor-pointer\",\n                                                onClick: ()=>{\n                                                    if (selectedView === \"Orders\") {\n                                                        router.push(\"/ppe-consumable/add\");\n                                                    } else {\n                                                        setShowAddProduct(true);\n                                                        setIsEditingProduct(false);\n                                                    }\n                                                },\n                                                children: orderView ? \"New Request\" : \"Add Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 844,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 830,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 731,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddProductDialog__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                open: showAddProduct,\n                                onOpenChange: (_, data)=>{\n                                    setShowAddProduct(data.open);\n                                    setIsEditingProduct(false);\n                                },\n                                onSubmit: (param)=>{\n                                    let { productName, printing, sizes } = param;\n                                    // handle your submit logic here\n                                    console.log({\n                                        productName,\n                                        printing,\n                                        sizes\n                                    });\n                                    setShowAddProduct(false);\n                                    router.reload();\n                                },\n                                isEditingProduct: isEditingProduct,\n                                editProductData: editProductData,\n                                productTypes: productTypes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 859,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative ag-theme-alpine !rounded-md\",\n                                    style: {\n                                        height: \"calc(100vh - 180px)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_5__.AgGridReact, {\n                                            rowData: requestRowData,\n                                            ref: gridRef,\n                                            requestColumnDefs: requestColumnDefs,\n                                            defaultColDef: defaultColDef,\n                                            suppressRowClickSelection: true,\n                                            pagination: true,\n                                            paginationPageSize: pageSize,\n                                            onPageSizeChanged: handlePageSizeChange,\n                                            tooltipShowDelay: 0,\n                                            tooltipHideDelay: 1000,\n                                            onGridReady: handleGridReady\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 881,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-start mt-2 pagination-style\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"page-size-select pagination\",\n                                                className: \"inputs\",\n                                                children: [\n                                                    \"Show\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"page-size-select\",\n                                                        onChange: handlePageSizeChange,\n                                                        value: pageSize,\n                                                        className: \"focus:outline-none\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 10,\n                                                                children: \"10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 903,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 15,\n                                                                children: \"15\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 904,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 25,\n                                                                children: \"25\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 905,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 50,\n                                                                children: \"50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 906,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 100,\n                                                                children: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 907,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" \",\n                                                    \"Entries\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 895,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 894,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                    lineNumber: 877,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 876,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                        lineNumber: 730,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.FluentProvider, {\n                        theme: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.webLightTheme,\n                        className: \"!bg-transparent\",\n                        style: {\n                            fontFamily: \"poppinsregular\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.Dialog, {\n                                open: showStatusDialog,\n                                onOpenChange: (_, data)=>{\n                                    if (!data.open) {\n                                        setIsValidCancelReason(true);\n                                        setCancelledReasonapi(\"\"); // Also clear the text\n                                    }\n                                    setShowStatusDialog(data.open);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogTrigger, {\n                                        disableButtonEnhancement: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"none\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 931,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 930,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogSurface, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogBody, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogTitle, {\n                                                    children: \"Update Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 935,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mb-6\",\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                statusAction === 2 ? \"approve\" : statusAction === 3 ? \"reject\" : statusAction === 4 ? \"mark as ordered\" : \"update\",\n                                                                \" \",\n                                                                \"this request ?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 937,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        statusAction === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                            rows: \"8\",\n                                                            value: cancelledReasonapi,\n                                                            onChange: (e)=>{\n                                                                setCancelledReasonapi(e.target.value);\n                                                                if (e.target.value) {\n                                                                    setIsValidCancelReason(true);\n                                                                }\n                                                            },\n                                                            onBlur: (e)=>{\n                                                                const trimmedValue = trimInputText(e.target.value);\n                                                                setCancelledReasonapi(trimmedValue);\n                                                            },\n                                                            placeholder: \"Provide reason for rejection...\",\n                                                            maxLength: \"500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 949,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        statusAction === 3 && !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"Please Provide reason for cancellation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 968,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"8px\",\n                                                                marginTop: \"16px\"\n                                                            },\n                                                            className: \"flex justify-end\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        setShowStatusDialog(false);\n                                                                        setCancelledReasonapi(\"\");\n                                                                    },\n                                                                    \"data-modal-hide\": \"default-modal\",\n                                                                    type: \"button\",\n                                                                    className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                    lineNumber: 976,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        // Handle status update logic here\n                                                                        console.log(\"Updating status:\", statusAction, selectedRowData);\n                                                                        updateSelectedRowStatus(statusAction, cancelledReasonapi, selectedRowData);\n                                                                    },\n                                                                    \"data-modal-hide\": \"default-modal\",\n                                                                    type: \"button\",\n                                                                    className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                                    children: \"Continue\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                    lineNumber: 984,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 972,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 936,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 934,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 933,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 920,\n                                columnNumber: 11\n                            }, undefined),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                        lineNumber: 915,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                lineNumber: 729,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(PPEConsumables, \"j80CPzQ/fhm9JozSpRdK+Ar7Bt0=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__.useLoading\n    ];\n});\n_c = PPEConsumables;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PPEConsumables);\n// Helper function to derive unit from package quantity\nfunction deriveUnitFromPackageQuantity(packageQuantity) {\n    if (!packageQuantity) return \"Unit\";\n    const lower = packageQuantity.toLowerCase();\n    if (lower.includes(\"box\")) return \"Box\";\n    if (lower.includes(\"pack\")) return \"Pack\";\n    if (lower.includes(\"pair\")) return \"Pair\";\n    return \"Unit\";\n}\nvar _c;\n$RefreshReg$(_c, \"PPEConsumables\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/ppe-consumable.js\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable",{

/***/ "./pages/ppe-consumable.js":
/*!*********************************!*\
  !*** ./pages/ppe-consumable.js ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _components_AddProductDialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/AddProductDialog */ \"./components/AddProductDialog.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var _utils_renderer_ppeActionRenderer__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/renderer/ppeActionRenderer */ \"./utils/renderer/ppeActionRenderer.js\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _utils_renderer_ppeActiveRenderer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/renderer/ppeActiveRenderer */ \"./utils/renderer/ppeActiveRenderer.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst PPEConsumables = (param)=>{\n    let { userData } = param;\n    _s();\n    // console.log(\"PARETN component rendered\", userData);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    // const [rowData, setRowData] = useState([]);\n    const [requestRowData, setRequestRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__.useLoading)();\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [orderView, setOrderView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [productTypes, setProductTypes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    //#region pop up states\n    const [showAddProduct, setShowAddProduct] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isEditingProduct, setIsEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editProductData, setEditProductData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Controlled filter states\n    const [siteFilter, setSiteFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [dateFilter, setDateFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showStatusDialog, setShowStatusDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedRowData, setSelectedRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [statusAction, setStatusAction] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Load saved view from localStorage on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Only run on client side\n        if (true) {\n            const savedView = localStorage.getItem(\"ppeSelectedView\");\n            if (savedView && (savedView === \"Orders\" || savedView === \"Products\")) {\n                setSelectedView(savedView);\n                setOrderView(savedView === \"Orders\");\n            } else {\n                setSelectedView(\"Orders\");\n                setOrderView(true);\n            }\n        }\n    }, []); // Empty dependency array - runs only on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!selectedView) return;\n        const fetchProducts = async ()=>{\n            if (selectedView === \"Orders\") {\n                getData().then((data)=>{\n                    // console.log(\"data\", data);\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            item_number: row.item_number,\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: (row.action_id === 3 ? \"Rejected Reason: \".concat(row.comments) : row.comments) || \"\",\n                            NameForPrinting: row.name_for_printing,\n                            size: row.size,\n                            quantity: row.quantity,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    // console.log(\"Formatted Data:\", formattedData);\n                    // setRowData(formattedData);\n                    setRequestRowData(formattedData);\n                });\n            } else {\n                const [productsJson, productTypesJson] = await Promise.all([\n                    getAllProducts(),\n                    getProductTypes()\n                ]);\n                // console.log(\"Fetched Products JSON:\", productsJson);\n                // console.log(\"Fetched Product Types JSON:\", productTypesJson);\n                // Save product types for later use (e.g., filters or dialogs)\n                if (Array.isArray(productTypesJson)) {\n                    setProductTypes(productTypesJson);\n                } else {\n                    setProductTypes([]);\n                }\n                // console.log(\"Fetched Products JSON:\", productsJson);\n                if (productsJson && productsJson.length > 0) {\n                    // console.log(\n                    //   \"Fetched Products JSON inside code execution\",\n                    //   productsJson\n                    // );\n                    const formattedProductData = productsJson.map((product)=>({\n                            product_id: product.ProductId,\n                            product_name: product.ProductName || \"Unnamed Product\",\n                            category: product.ProductType || \"Uncategorized\",\n                            type_id: product.TypeId,\n                            stock: 0,\n                            sizes: product.AvailableSizes || \"One Size\",\n                            unit: deriveUnitFromPackageQuantity(product.PackageQuantity),\n                            name_printable: product.name_printable,\n                            IsProductHidden: product.IsProductHidden\n                        }));\n                    // setRowData(formattedProductData);\n                    setRequestRowData(formattedProductData);\n                } else {\n                    const fallbackProductData = [\n                        {\n                            product_name: \"Nitrile Gloves\",\n                            category: \"Gloves\",\n                            stock: 1200,\n                            unit: \"Box\",\n                            status: \"Available\"\n                        }\n                    ];\n                // setRowData(fallbackProductData);\n                }\n            }\n        };\n        fetchProducts();\n    }, [\n        selectedView\n    ]);\n    // #region get data\n    const getData = async ()=>{\n        // setRowData([]);\n        setRequestRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        console.log(\"\");\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-ppe-requests\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            console.error(error);\n        });\n    };\n    async function getAllProducts() {\n        // setRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-products\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to fetch products: \".concat(error.message));\n            throw error;\n        });\n    }\n    async function getProductTypes() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/get-product_types\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch product types\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to fetch product types: \".concat(error.message));\n            throw error;\n        });\n    }\n    //#region column def\n    // Example column definitions for Products\n    // Replace with proper filter handling\n    const handleSiteFilterChange = (value)=>{\n        setSiteFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"site_name\");\n            if (filterInstance) {\n                if (value) {\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        filter: value\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"status\");\n            if (filterInstance) {\n                if (value) {\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        filter: value\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    const handleSelectedView = (selectedType)=>{\n        setSelectedView(selectedType);\n        setOrderView(selectedType === \"Orders\");\n        // Only save to localStorage on client side\n        if (true) {\n            localStorage.setItem(\"ppeSelectedView\", selectedType);\n        }\n    };\n    const handleDateFilterChange = (value)=>{\n        setDateFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"required_date\");\n            if (filterInstance) {\n                if (value) {\n                    const selectedDate = new Date(value);\n                    const yyyy = selectedDate.getFullYear();\n                    const mm = String(selectedDate.getMonth() + 1).padStart(2, \"0\");\n                    const dd = String(selectedDate.getDate()).padStart(2, \"0\");\n                    const formattedDate = \"\".concat(yyyy, \"-\").concat(mm, \"-\").concat(dd);\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        dateFrom: formattedDate\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    // Example column definitions\n    const requestColumnDefs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>[\n            {\n                headerName: \"Request ID\",\n                field: \"request_id\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Required Date\",\n                field: \"required_date\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agDateColumnFilter\",\n                filterParams: {\n                    comparator: (filterLocalDateAtMidnight, cellValue)=>{\n                        const dateAsString = cellValue;\n                        if (!dateAsString) return 0;\n                        const cellDate = new Date(dateAsString);\n                        const cellDateAtMidnight = new Date(cellDate);\n                        cellDateAtMidnight.setHours(0, 0, 0, 0);\n                        if (cellDateAtMidnight < filterLocalDateAtMidnight) {\n                            return -1;\n                        } else if (cellDateAtMidnight > filterLocalDateAtMidnight) {\n                            return 1;\n                        } else {\n                            return 0;\n                        }\n                    }\n                },\n                valueFormatter: (params)=>{\n                    return params.value ? new Date(params.value).toLocaleDateString() : \"\";\n                }\n            },\n            {\n                headerName: \"Site\",\n                field: \"site_name\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\",\n                filterParams: {\n                    values: [\n                        \"ISS1-Teynham\",\n                        \"ISS2-Linton\",\n                        \"ISS3-Sittingbourne\"\n                    ]\n                }\n            },\n            {\n                headerName: \"Product\",\n                field: \"product_name\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Size\",\n                field: \"size\",\n                flex: 0.75,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Qty\",\n                field: \"quantity\",\n                flex: 0.75,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Requestor\",\n                field: \"requestor\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Comment\",\n                field: \"comment\",\n                flex: 1,\n                hide: !orderView\n            },\n            {\n                headerName: \"Status\",\n                field: \"status\",\n                hide: !orderView,\n                cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                cellStyle: ()=>({\n                        justifyContent: \"center\"\n                    }),\n                flex: 1.25,\n                filterParams: {\n                    values: [\n                        \"Pending Review\",\n                        \"Approved\",\n                        \"Rejected\",\n                        \"Ordered\",\n                        \"Draft\"\n                    ]\n                }\n            },\n            //product view\n            {\n                headerName: \"Product Name\",\n                field: \"product_name\",\n                flex: 1,\n                hide: orderView\n            },\n            {\n                headerName: \"Category\",\n                field: \"category\",\n                flex: 0.5,\n                hide: orderView\n            },\n            {\n                headerName: \"Sizes\",\n                field: \"sizes\",\n                flex: 0.5,\n                hide: orderView\n            },\n            {\n                headerName: \"Name Printing\",\n                field: \"name_printable\",\n                flex: 0.5,\n                hide: orderView,\n                cellRenderer: \"agCheckboxCellRenderer\",\n                cellRendererParams: {\n                    disabled: true\n                },\n                cellStyle: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\"\n                },\n                headerClass: \"ag-header-cell-centered\"\n            },\n            {\n                headerName: \"Status\",\n                // field: \"site_id\",\n                flex: 0.5,\n                cellRenderer: (params)=>(0,_utils_renderer_ppeActiveRenderer__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(params, setShowAddProduct, orderView, setIsEditingProduct, setEditProductData, //banana\n                    setShowStatusDialog, setSelectedRowData, setStatusAction),\n                sortable: false,\n                hide: orderView\n            },\n            {\n                headerName: \"Actions\",\n                field: \"site_id\",\n                flex: 1,\n                cellRenderer: (params)=>(0,_utils_renderer_ppeActionRenderer__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(params, setShowAddProduct, orderView, setIsEditingProduct, setEditProductData, //banana\n                    setShowStatusDialog, setSelectedRowData, setStatusAction, userData),\n                sortable: false\n            }\n        ], [\n        orderView\n    ]);\n    // console.log(\"order vbiew\", orderView);\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: true,\n            filterParams: {\n                debounceMs: 300\n            },\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }), []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsLoading(false);\n    // console.log(\n    //   \"Selected View:\",\n    //   selectedView,\n    //   \"Order View:\",\n    //   orderView,\n    //   selectedView === \"Orders\"\n    // );\n    }, [\n        selectedView\n    ]);\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(requestColumnDefs);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (gridRef.current && gridRef.current.api) {\n            gridRef.current.api.setColumnDefs(requestColumnDefs);\n            // Refresh the grid to apply new column definitions\n            gridRef.current.api.refreshHeader();\n            gridRef.current.api.refreshCells();\n        }\n    }, [\n        requestColumnDefs,\n        orderView\n    ]); // Add orderView dependency\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n        setSearchInput(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const handleClearFilters = ()=>{\n        // Clear AG Grid filters\n        if (gridRef.current && gridRef.current.api) {\n            gridRef.current.api.setFilterModel(null);\n            gridRef.current.api.onFilterChanged();\n        }\n        // Reset UI control states\n        setSiteFilter(\"\");\n        setStatusFilter(\"\");\n        setDateFilter(\"\");\n    };\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    //#region api\n    const updateSelectedRowStatus = (updatedStatusID, comment, params)=>{\n        if (updatedStatusID == 3) {\n            if (comment) {\n                setIsValidCancelReason(true);\n            } else {\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        console.log(\"sssssssssssssssssssssssssssssssssssss\", params);\n        const apiPayload = {\n            request_no: params.id,\n            item_number: params.item_number,\n            ProductName: params.product_name,\n            Size: params.size,\n            Quantity: params.quantity,\n            NameForPrinting: params.NameForPrinting,\n            Comments: params.comment,\n            RequestItemID: params.request_item_id,\n            commentOnUpdatingRequest: comment,\n            action_id: updatedStatusID,\n            SubmitterUserID: userData.user_id,\n            SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n            orignatorEmail: params === null || params === void 0 ? void 0 : params.orignatorEmail\n        };\n        try {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-product-request-items/\").concat(params.id), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_16__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n                getData().then((data)=>{\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            item_number: row.item_number,\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: (row.action_id === 3 ? \"Rejected Reason: \".concat(row.comments) : row.comments) || \"\",\n                            size: row.size,\n                            quantity: row.quantity,\n                            NameForPrinting: row.name_for_printing,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    setRequestRowData(formattedData);\n                    setCancelledReasonapi(\"\");\n                    setShowStatusDialog(false);\n                });\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_3__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                lineNumber: 727,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-20 md:mr-12 lg:mr-14\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                                children: [\n                                    (userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex rounded-full bg-gray-200 p-1 \\n                 \".concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\", \"\\n                \"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-1 rounded-full text-sm font-medium transition \".concat(selectedView === \"Orders\" ? \"bg-white shadow text-black\" : \"text-gray-500 hover:text-black\"),\n                                                    onClick: ()=>handleSelectedView(\"Orders\"),\n                                                    children: \"Requests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-1 rounded-full text-sm font-medium transition \".concat(selectedView === \"Products\" ? \"bg-white shadow text-black\" : \"text-gray-500 hover:text-black\"),\n                                                    onClick: ()=>{\n                                                        handleSelectedView(\"Products\"), handleClearFilters();\n                                                    },\n                                                    children: \"Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 735,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 734,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    orderView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        // style={{ marginBottom: \"10px\" }}\n                                        className: \"flex gap-4 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Site:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: siteFilter,\n                                                        onChange: (e)=>handleSiteFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"All Sites\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS1-Teynham\",\n                                                                children: \"ISS1-Teynham\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 785,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS2-Linton\",\n                                                                children: \"ISS2-Linton\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 786,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS3-Sittingbourne\",\n                                                                children: \"ISS3-Sittingbourne\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 787,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 776,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Status:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: statusFilter,\n                                                        onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"All Statuses\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 801,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Pending Review\",\n                                                                children: \"Pending\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Approved\",\n                                                                children: \"Approved\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Rejected\",\n                                                                children: \"Rejected\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 805,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Ordered\",\n                                                                children: \"Ordered\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Draft\",\n                                                                children: \"Draft\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 807,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 796,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 794,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Date:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: dateFilter,\n                                                        onChange: (e)=>handleDateFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 814,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 812,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleClearFilters,\n                                                className: \"ml-4 px-3 py-1 border rounded bg-gray-200 hover:bg-gray-300\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 822,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 772,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative block w-[47vh] text-gray-400  mt-0 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__.FontAwesomeIcon, {\n                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_17__.faSearch,\n                                                            className: \"fw-bold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 833,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 832,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"filter-text-box\",\n                                                        placeholder: \"Search...\",\n                                                        onInput: onFilterTextBoxChanged,\n                                                        value: searchInput,\n                                                        className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none shadow-[0_0_5px_rgba(0,0,0,0.1)]\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 831,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"ml-2 my-2 px-3 py-1 p-[6px] border rounded-md bg-skin-primary text-white text-sm cursor-pointer\",\n                                                onClick: ()=>{\n                                                    if (selectedView === \"Orders\") {\n                                                        router.push(\"/ppe-consumable/add\");\n                                                    } else {\n                                                        setShowAddProduct(true);\n                                                        setIsEditingProduct(false);\n                                                    }\n                                                },\n                                                children: orderView ? \"New Request\" : \"Add Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 844,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 830,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 730,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddProductDialog__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                open: showAddProduct,\n                                onOpenChange: (_, data)=>{\n                                    setShowAddProduct(data.open);\n                                    setIsEditingProduct(false);\n                                },\n                                onSubmit: (param)=>{\n                                    let { productName, printing, sizes } = param;\n                                    // handle your submit logic here\n                                    console.log({\n                                        productName,\n                                        printing,\n                                        sizes\n                                    });\n                                    setShowAddProduct(false);\n                                    router.reload();\n                                },\n                                isEditingProduct: isEditingProduct,\n                                editProductData: editProductData,\n                                productTypes: productTypes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 859,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative ag-theme-alpine !rounded-md\",\n                                    style: {\n                                        height: \"calc(100vh - 180px)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_5__.AgGridReact, {\n                                            rowData: requestRowData,\n                                            ref: gridRef,\n                                            requestColumnDefs: requestColumnDefs,\n                                            defaultColDef: defaultColDef,\n                                            suppressRowClickSelection: true,\n                                            pagination: true,\n                                            paginationPageSize: pageSize,\n                                            onPageSizeChanged: handlePageSizeChange,\n                                            tooltipShowDelay: 0,\n                                            tooltipHideDelay: 1000,\n                                            onGridReady: handleGridReady\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 881,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-start mt-2 pagination-style\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"page-size-select pagination\",\n                                                className: \"inputs\",\n                                                children: [\n                                                    \"Show\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"page-size-select\",\n                                                        onChange: handlePageSizeChange,\n                                                        value: pageSize,\n                                                        className: \"focus:outline-none\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 10,\n                                                                children: \"10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 903,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 15,\n                                                                children: \"15\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 904,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 25,\n                                                                children: \"25\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 905,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 50,\n                                                                children: \"50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 906,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 100,\n                                                                children: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 907,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" \",\n                                                    \"Entries\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 895,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 894,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                    lineNumber: 877,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 876,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                        lineNumber: 729,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.FluentProvider, {\n                        theme: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.webLightTheme,\n                        className: \"!bg-transparent\",\n                        style: {\n                            fontFamily: \"poppinsregular\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.Dialog, {\n                                open: showStatusDialog,\n                                onOpenChange: (_, data)=>{\n                                    if (!data.open) {\n                                        setIsValidCancelReason(true);\n                                        setCancelledReasonapi(\"\"); // Also clear the text\n                                    }\n                                    setShowStatusDialog(data.open);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogTrigger, {\n                                        disableButtonEnhancement: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"none\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 931,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 930,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogSurface, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogBody, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogTitle, {\n                                                    children: \"Update Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 935,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_18__.DialogContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mb-6\",\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                statusAction === 2 ? \"approve\" : statusAction === 3 ? \"reject\" : statusAction === 4 ? \"mark as ordered\" : \"update\",\n                                                                \" \",\n                                                                \"this request ?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 937,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        statusAction === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                            rows: \"8\",\n                                                            value: cancelledReasonapi,\n                                                            onChange: (e)=>{\n                                                                setCancelledReasonapi(e.target.value);\n                                                                if (e.target.value) {\n                                                                    setIsValidCancelReason(true);\n                                                                }\n                                                            },\n                                                            onBlur: (e)=>{\n                                                                const trimmedValue = trimInputText(e.target.value);\n                                                                setCancelledReasonapi(trimmedValue);\n                                                            },\n                                                            placeholder: \"Provide reason for rejection...\",\n                                                            maxLength: \"500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 949,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        statusAction === 3 && !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"Please Provide reason for cancellation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 968,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"8px\",\n                                                                marginTop: \"16px\"\n                                                            },\n                                                            className: \"flex justify-end\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        setShowStatusDialog(false);\n                                                                        setCancelledReasonapi(\"\");\n                                                                    },\n                                                                    \"data-modal-hide\": \"default-modal\",\n                                                                    type: \"button\",\n                                                                    className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                    lineNumber: 976,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        // Handle status update logic here\n                                                                        console.log(\"Updating status:\", statusAction, selectedRowData);\n                                                                        updateSelectedRowStatus(statusAction, cancelledReasonapi, selectedRowData);\n                                                                    },\n                                                                    \"data-modal-hide\": \"default-modal\",\n                                                                    type: \"button\",\n                                                                    className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                                    children: \"Continue\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                    lineNumber: 984,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 972,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 936,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 934,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 933,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 920,\n                                columnNumber: 11\n                            }, undefined),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                        lineNumber: 915,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                lineNumber: 728,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(PPEConsumables, \"j80CPzQ/fhm9JozSpRdK+Ar7Bt0=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__.useLoading\n    ];\n});\n_c = PPEConsumables;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PPEConsumables);\n// Helper function to derive unit from package quantity\nfunction deriveUnitFromPackageQuantity(packageQuantity) {\n    if (!packageQuantity) return \"Unit\";\n    const lower = packageQuantity.toLowerCase();\n    if (lower.includes(\"box\")) return \"Box\";\n    if (lower.includes(\"pack\")) return \"Pack\";\n    if (lower.includes(\"pair\")) return \"Pair\";\n    return \"Unit\";\n}\nvar _c;\n$RefreshReg$(_c, \"PPEConsumables\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/ppe-consumable.js\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable",{

/***/ "./components/Navbar.js":
/*!******************************!*\
  !*** ./components/Navbar.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   themes: function() { return /* binding */ themes; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst themes = [\n    {\n        bg: \"#022d71\",\n        text: \"#022d71\",\n        name: \"DPS\"\n    },\n    {\n        bg: \"#2e9b28\",\n        text: \"#2e9b28\",\n        name: \"EFC\"\n    },\n    {\n        bg: \"#a91e23\",\n        text: \"#a91e23\",\n        name: \"M&S\"\n    },\n    {\n        bg: \"#3d6546\",\n        text: \"#3d6546\",\n        name: \"FPP\"\n    }\n];\nconst Navbar = (param)=>{\n    let { userData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [pageName, setPageName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentRoute, setCurrentRoute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentPath = router.pathname;\n        setCurrentRoute(currentPath);\n        if (currentPath === \"/finished-product-request/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Goods Request\");\n        } else if (currentPath === \"/raw-material-request/add\") {\n            setPageName(\"Raw Material Request\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n        } else if (currentPath === \"/packaging-form/add\") {\n            setPageName(\"Packaging Request\");\n        } else if (currentPath === \"/ppe-consumable/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"PPE-Consumable Request\");\n        } else if (currentPath === \"/ppe-consumable/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n        } else if (currentPath === \"/variety/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"New Variety Request\");\n        } else if (currentPath === \"/variety/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"Edit New Variety Request\");\n        } else if (currentPath === \"/packaging-form/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Edit Packaging Request\");\n        } else if (currentPath === \"/raw-material-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Edit Raw Material Request\");\n        } else if (currentPath === \"/finished-product-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Edit Finished Goods Request\");\n        } else if ((currentPath === null || currentPath === void 0 ? void 0 : currentPath.startsWith(\"/supplier\")) && (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit\"))) {\n            setPageName(\"Edit Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/add\")) {\n            setPageName(\"Add Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit/forms\")) {\n            setPageName(\"Supplier Form\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/confirm\")) {\n            setPageName(\"Confirm Details for Supplier\");\n        } else if (currentPath === \"/suppliers\") {\n            setPageName(\"Suppliers\");\n        } else if (currentPath === \"/users\") {\n            setPageName(\"User Management\");\n        } else if (currentPath === \"/viewlogs\") {\n            setPageName(\"View Logs\");\n        } else if (currentPath === \"/products\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Products\");\n        } else if (currentPath === \"/variety\") {\n            setPageName(\"Variety\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n        } else if (currentPath === \"/finishedProductRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Product Request\");\n        } else if (currentPath === \"/rawMaterialRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Raw Material Request\");\n        } else if (currentPath === \"/whatif\" || currentPath === \"/whatif/reports/masterRollingForecast\") {\n            setPageName(\"Whatif\");\n        } else if (currentPath === \"/service_level\" || currentPath === \"/service_level/reports/masterForcast\") {\n            setPageName(\"Service Level\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.startsWith(\"/ppe-consumable\")) {\n            setPageName(\"PPE Consumables\");\n        }\n    }, [\n        router.pathname\n    ]);\n    const baseCompanyOptions = [\n        {\n            value: \"dpsltd\",\n            label: \"DPS\"\n        },\n        {\n            value: \"dpsltdms\",\n            label: \"DPS M&S\"\n        },\n        {\n            value: \"efcltd\",\n            label: \"EFC\"\n        },\n        {\n            value: \"fpp-ltd\",\n            label: \"FPP\"\n        }\n    ];\n    const companyOptions = [\n        ...baseCompanyOptions,\n        ...(userData === null || userData === void 0 ? void 0 : userData.role_id) === 6 || \"<EMAIL>\".split(\";\").includes(userData === null || userData === void 0 ? void 0 : userData.email) ? [\n            {\n                value: \"issproduce\",\n                label: \"ISS\"\n            },\n            {\n                value: \"flrs\",\n                label: \"FLRS\",\n                disabled: true\n            },\n            {\n                value: \"thl\",\n                label: \"THL\",\n                disabled: true\n            }\n        ] : []\n    ];\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((userData === null || userData === void 0 ? void 0 : userData.ADCompanyName) === \"DPS MS\" ? \"dpsltdms\" : (userData === null || userData === void 0 ? void 0 : userData.company) || \"\");\n    const handleCompanyChange = async (event)=>{\n        const company = event.target.value;\n        if (isUpdating) return; // Prevent multiple rapid clicks\n        setIsUpdating(true);\n        try {\n            const apiBase = \"http://localhost:8081\" || 0;\n            // Call the API to update company in session\n            const response = await fetch(\"\".concat(apiBase, \"/api/auth/update-company\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    company\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"Failed to update company\");\n            }\n            const result = await response.json();\n            // Update local state\n            setSelectedCompany(company);\n            // Reload the page to reflect changes throughout the application\n            router.reload();\n        } catch (error) {\n            console.error(\"Error updating company:\", error);\n            alert(\"Failed to update company. Please try again.\");\n            // Reset the select to previous value on error\n            event.target.value = selectedCompany;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"titlebar\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row justify-between w-full bg-skin-primary h-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row items-center page-heading cursor-pointer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__.faChevronLeft,\n                                    className: \"pageName text-white\",\n                                    onClick: ()=>router.back()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    // href={currentRoute}\n                                    className: \"ml-4 text-xl font-medium font-poppinsregular pageName text-white tracking-[0.05em]\",\n                                    children: pageName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, undefined),\n                    ((userData === null || userData === void 0 ? void 0 : userData.role_id) == 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) == 6 || \"<EMAIL>\".split(\";\").includes(userData === null || userData === void 0 ? void 0 : userData.email)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end w-1/2 items-center mr-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedCompany,\n                                onChange: handleCompanyChange,\n                                disabled: isUpdating,\n                                className: \"bg-white text-black rounded disabled:opacity-50\",\n                                children: companyOptions.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: opt.value,\n                                        disabled: opt.disabled,\n                                        children: opt.label\n                                    }, opt.value, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 185,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, undefined),\n                            isUpdating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-white text-sm\",\n                                children: \"Updating...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 195,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 177,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                lineNumber: 160,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n            lineNumber: 159,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"EcIBICVLCJu3IdXMDDAfVy7bwBw=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Navbar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Navbar);\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Navbar.js\n"));

/***/ })

});
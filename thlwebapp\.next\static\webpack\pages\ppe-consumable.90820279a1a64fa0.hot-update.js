"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable",{

/***/ "./pages/ppe-consumable.js":
/*!*********************************!*\
  !*** ./pages/ppe-consumable.js ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _components_AddProductDialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/AddProductDialog */ \"./components/AddProductDialog.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var _utils_renderer_ppeActionRenderer__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/renderer/ppeActionRenderer */ \"./utils/renderer/ppeActionRenderer.js\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _utils_renderer_ppeActiveRenderer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/renderer/ppeActiveRenderer */ \"./utils/renderer/ppeActiveRenderer.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst PPEConsumables = (param)=>{\n    let { userData } = param;\n    _s();\n    // console.log(\"PARETN component rendered\", userData);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    // const [rowData, setRowData] = useState([]);\n    const [requestRowData, setRequestRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__.useLoading)();\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [orderView, setOrderView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [productTypes, setProductTypes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    //#region pop up states\n    const [showAddProduct, setShowAddProduct] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isEditingProduct, setIsEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editProductData, setEditProductData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Controlled filter states\n    const [siteFilter, setSiteFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [dateFilter, setDateFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showStatusDialog, setShowStatusDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedRowData, setSelectedRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [statusAction, setStatusAction] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Load saved view from localStorage on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Only run on client side\n        if (true) {\n            const savedView = localStorage.getItem(\"ppeSelectedView\");\n            if (savedView && (savedView === \"Orders\" || savedView === \"Products\")) {\n                setSelectedView(savedView);\n                setOrderView(savedView === \"Orders\");\n            } else {\n                setSelectedView(\"Orders\");\n                setOrderView(true);\n            }\n        }\n    }, []); // Empty dependency array - runs only on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!selectedView) return;\n        const fetchProducts = async ()=>{\n            if (selectedView === \"Orders\") {\n                getData().then((data)=>{\n                    // console.log(\"data\", data);\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: row.comments,\n                            NameForPrinting: row.name_for_printing,\n                            size: row.size,\n                            quantity: row.quantity,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    // console.log(\"Formatted Data:\", formattedData);\n                    // setRowData(formattedData);\n                    setRequestRowData(formattedData);\n                });\n            } else {\n                const [productsJson, productTypesJson] = await Promise.all([\n                    getAllProducts(),\n                    getProductTypes()\n                ]);\n                // console.log(\"Fetched Products JSON:\", productsJson);\n                // console.log(\"Fetched Product Types JSON:\", productTypesJson);\n                // Save product types for later use (e.g., filters or dialogs)\n                if (Array.isArray(productTypesJson)) {\n                    setProductTypes(productTypesJson);\n                } else {\n                    setProductTypes([]);\n                }\n                // console.log(\"Fetched Products JSON:\", productsJson);\n                if (productsJson && productsJson.length > 0) {\n                    // console.log(\n                    //   \"Fetched Products JSON inside code execution\",\n                    //   productsJson\n                    // );\n                    const formattedProductData = productsJson.map((product)=>({\n                            product_id: product.ProductId,\n                            product_name: product.ProductName || \"Unnamed Product\",\n                            category: product.ProductType || \"Uncategorized\",\n                            type_id: product.TypeId,\n                            stock: 0,\n                            sizes: product.AvailableSizes || \"One Size\",\n                            unit: deriveUnitFromPackageQuantity(product.PackageQuantity),\n                            name_printable: product.name_printable,\n                            IsProductHidden: product.IsProductHidden\n                        }));\n                    // setRowData(formattedProductData);\n                    setRequestRowData(formattedProductData);\n                } else {\n                    const fallbackProductData = [\n                        {\n                            product_name: \"Nitrile Gloves\",\n                            category: \"Gloves\",\n                            stock: 1200,\n                            unit: \"Box\",\n                            status: \"Available\"\n                        }\n                    ];\n                // setRowData(fallbackProductData);\n                }\n            }\n        };\n        fetchProducts();\n    }, [\n        selectedView\n    ]);\n    // #region get data\n    const getData = async ()=>{\n        // setRowData([]);\n        setRequestRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        console.log(\"\");\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-ppe-requests\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await logout();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            console.error(error);\n        });\n    };\n    async function getAllProducts() {\n        // setRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-products\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await logout();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to fetch products: \".concat(error.message));\n            throw error;\n        });\n    }\n    async function getProductTypes() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/get-product_types\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await logout();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch product types\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to fetch product types: \".concat(error.message));\n            throw error;\n        });\n    }\n    //#region column def\n    // Example column definitions for Products\n    // Replace with proper filter handling\n    const handleSiteFilterChange = (value)=>{\n        setSiteFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"site_name\");\n            if (filterInstance) {\n                if (value) {\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        filter: value\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"status\");\n            if (filterInstance) {\n                if (value) {\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        filter: value\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    const handleSelectedView = (selectedType)=>{\n        setSelectedView(selectedType);\n        setOrderView(selectedType === \"Orders\");\n        // Only save to localStorage on client side\n        if (true) {\n            localStorage.setItem(\"ppeSelectedView\", selectedType);\n        }\n    };\n    const handleDateFilterChange = (value)=>{\n        setDateFilter(value);\n        if (gridRef.current && gridRef.current.api) {\n            const filterInstance = gridRef.current.api.getFilterInstance(\"required_date\");\n            if (filterInstance) {\n                if (value) {\n                    const selectedDate = new Date(value);\n                    const yyyy = selectedDate.getFullYear();\n                    const mm = String(selectedDate.getMonth() + 1).padStart(2, \"0\");\n                    const dd = String(selectedDate.getDate()).padStart(2, \"0\");\n                    const formattedDate = \"\".concat(yyyy, \"-\").concat(mm, \"-\").concat(dd);\n                    filterInstance.setModel({\n                        type: \"equals\",\n                        dateFrom: formattedDate\n                    });\n                } else {\n                    filterInstance.setModel(null);\n                }\n                gridRef.current.api.onFilterChanged();\n            }\n        }\n    };\n    // Example column definitions\n    const requestColumnDefs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>[\n            {\n                headerName: \"Request ID\",\n                field: \"request_id\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Required Date\",\n                field: \"required_date\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agDateColumnFilter\",\n                filterParams: {\n                    comparator: (filterLocalDateAtMidnight, cellValue)=>{\n                        const dateAsString = cellValue;\n                        if (!dateAsString) return 0;\n                        const cellDate = new Date(dateAsString);\n                        const cellDateAtMidnight = new Date(cellDate);\n                        cellDateAtMidnight.setHours(0, 0, 0, 0);\n                        if (cellDateAtMidnight < filterLocalDateAtMidnight) {\n                            return -1;\n                        } else if (cellDateAtMidnight > filterLocalDateAtMidnight) {\n                            return 1;\n                        } else {\n                            return 0;\n                        }\n                    }\n                },\n                valueFormatter: (params)=>{\n                    return params.value ? new Date(params.value).toLocaleDateString() : \"\";\n                }\n            },\n            {\n                headerName: \"Site\",\n                field: \"site_name\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\",\n                filterParams: {\n                    values: [\n                        \"ISS1-Teynham\",\n                        \"ISS2-Linton\",\n                        \"ISS3-Sittingbourne\"\n                    ]\n                }\n            },\n            {\n                headerName: \"Product\",\n                field: \"product_name\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Size\",\n                field: \"size\",\n                flex: 0.75,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Qty\",\n                field: \"quantity\",\n                flex: 0.75,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Requestor\",\n                field: \"requestor\",\n                flex: 1,\n                hide: !orderView,\n                filter: \"agTextColumnFilter\"\n            },\n            {\n                headerName: \"Comment\",\n                field: \"comment\",\n                flex: 1,\n                hide: !orderView\n            },\n            {\n                headerName: \"Status\",\n                field: \"status\",\n                hide: !orderView,\n                cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                cellStyle: ()=>({\n                        justifyContent: \"center\"\n                    }),\n                flex: 1.25,\n                filterParams: {\n                    values: [\n                        \"Pending Review\",\n                        \"Approved\",\n                        \"Rejected\",\n                        \"Ordered\",\n                        \"Draft\"\n                    ]\n                }\n            },\n            //product view\n            {\n                headerName: \"Product Name\",\n                field: \"product_name\",\n                flex: 1,\n                hide: orderView\n            },\n            {\n                headerName: \"Category\",\n                field: \"category\",\n                flex: 0.5,\n                hide: orderView\n            },\n            {\n                headerName: \"Sizes\",\n                field: \"sizes\",\n                flex: 0.5,\n                hide: orderView\n            },\n            {\n                headerName: \"Name Printing\",\n                field: \"name_printable\",\n                flex: 0.5,\n                hide: orderView,\n                cellRenderer: \"agCheckboxCellRenderer\",\n                cellRendererParams: {\n                    disabled: true\n                },\n                cellStyle: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\"\n                },\n                headerClass: \"ag-header-cell-centered\"\n            },\n            {\n                headerName: \"Status\",\n                // field: \"site_id\",\n                flex: 0.5,\n                cellRenderer: (params)=>(0,_utils_renderer_ppeActiveRenderer__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(params, setShowAddProduct, orderView, setIsEditingProduct, setEditProductData, //banana\n                    setShowStatusDialog, setSelectedRowData, setStatusAction),\n                sortable: false,\n                hide: orderView\n            },\n            {\n                headerName: \"Actions\",\n                field: \"site_id\",\n                flex: 1,\n                cellRenderer: (params)=>(0,_utils_renderer_ppeActionRenderer__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(params, setShowAddProduct, orderView, setIsEditingProduct, setEditProductData, //banana\n                    setShowStatusDialog, setSelectedRowData, setStatusAction, userData),\n                sortable: false\n            }\n        ], [\n        orderView\n    ]);\n    // console.log(\"order vbiew\", orderView);\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: true,\n            filterParams: {\n                debounceMs: 300\n            },\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }), []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsLoading(false);\n    // console.log(\n    //   \"Selected View:\",\n    //   selectedView,\n    //   \"Order View:\",\n    //   orderView,\n    //   selectedView === \"Orders\"\n    // );\n    }, [\n        selectedView\n    ]);\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(requestColumnDefs);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (gridRef.current && gridRef.current.api) {\n            gridRef.current.api.setColumnDefs(requestColumnDefs);\n            // Refresh the grid to apply new column definitions\n            gridRef.current.api.refreshHeader();\n            gridRef.current.api.refreshCells();\n        }\n    }, [\n        requestColumnDefs,\n        orderView\n    ]); // Add orderView dependency\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n        setSearchInput(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const handleClearFilters = ()=>{\n        // Clear AG Grid filters\n        if (gridRef.current && gridRef.current.api) {\n            gridRef.current.api.setFilterModel(null);\n            gridRef.current.api.onFilterChanged();\n        }\n        // Reset UI control states\n        setSiteFilter(\"\");\n        setStatusFilter(\"\");\n        setDateFilter(\"\");\n    };\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    //#region api\n    const updateSelectedRowStatus = (updatedStatusID, comment, params)=>{\n        if (updatedStatusID == 3) {\n            if (comment) {\n                setIsValidCancelReason(true);\n            } else {\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        console.log(\"sssssssssssssssssssssssssssssssssssss\", params);\n        const apiPayload = {\n            request_no: params.id,\n            // item_number: params.item_number,\n            ProductName: params.product_name,\n            Size: params.size,\n            Quantity: params.quantity,\n            NameForPrinting: params.NameForPrinting,\n            Comments: params.comment,\n            RequestItemID: params.request_item_id,\n            commentOnUpdatingRequest: comment,\n            action_id: updatedStatusID,\n            SubmitterUserID: userData.user_id,\n            SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n            orignatorEmail: params === null || params === void 0 ? void 0 : params.orignatorEmail\n        };\n        try {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_11__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-product-request-items/\").concat(params.id), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await logout();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n                getData().then((data)=>{\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: row.comments,\n                            size: row.size,\n                            quantity: row.quantity,\n                            NameForPrinting: row.name_for_printing,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    setRequestRowData(formattedData);\n                    setCancelledReasonapi(\"\");\n                    setShowStatusDialog(false);\n                });\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_3__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                lineNumber: 723,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-20 md:mr-12 lg:mr-14\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                                children: [\n                                    (userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex rounded-full bg-gray-200 p-1 \\n                 \".concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\", \"\\n                \"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-1 rounded-full text-sm font-medium transition \".concat(selectedView === \"Orders\" ? \"bg-white shadow text-black\" : \"text-gray-500 hover:text-black\"),\n                                                    onClick: ()=>handleSelectedView(\"Orders\"),\n                                                    children: \"Requests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 742,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-1 rounded-full text-sm font-medium transition \".concat(selectedView === \"Products\" ? \"bg-white shadow text-black\" : \"text-gray-500 hover:text-black\"),\n                                                    onClick: ()=>{\n                                                        handleSelectedView(\"Products\"), handleClearFilters();\n                                                    },\n                                                    children: \"Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 731,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 730,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    orderView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        // style={{ marginBottom: \"10px\" }}\n                                        className: \"flex gap-4 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Site:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: siteFilter,\n                                                        onChange: (e)=>handleSiteFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"All Sites\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS1-Teynham\",\n                                                                children: \"ISS1-Teynham\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS2-Linton\",\n                                                                children: \"ISS2-Linton\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ISS3-Sittingbourne\",\n                                                                children: \"ISS3-Sittingbourne\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 772,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Status:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 791,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: statusFilter,\n                                                        onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"All Statuses\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Pending Review\",\n                                                                children: \"Pending\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 799,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Approved\",\n                                                                children: \"Approved\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 800,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Rejected\",\n                                                                children: \"Rejected\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 801,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Ordered\",\n                                                                children: \"Ordered\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Draft\",\n                                                                children: \"Draft\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 790,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"mr-2\",\n                                                        children: \"Date:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 809,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: dateFilter,\n                                                        onChange: (e)=>handleDateFilterChange(e.target.value),\n                                                        className: \"border p-1 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 810,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 808,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleClearFilters,\n                                                className: \"ml-4 px-3 py-1 border rounded bg-gray-200 hover:bg-gray-300\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 818,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 768,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative block w-[47vh] text-gray-400  mt-0 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__.FontAwesomeIcon, {\n                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_16__.faSearch,\n                                                            className: \"fw-bold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 829,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"filter-text-box\",\n                                                        placeholder: \"Search...\",\n                                                        onInput: onFilterTextBoxChanged,\n                                                        value: searchInput,\n                                                        className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none shadow-[0_0_5px_rgba(0,0,0,0.1)]\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 827,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"ml-2 my-2 px-3 py-1 p-[6px] border rounded-md bg-skin-primary text-white text-sm cursor-pointer\",\n                                                onClick: ()=>{\n                                                    if (selectedView === \"Orders\") {\n                                                        router.push(\"/ppe-consumable/add\");\n                                                    } else {\n                                                        setShowAddProduct(true);\n                                                        setIsEditingProduct(false);\n                                                    }\n                                                },\n                                                children: orderView ? \"New Request\" : \"Add Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 840,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 826,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 726,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddProductDialog__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                open: showAddProduct,\n                                onOpenChange: (_, data)=>{\n                                    setShowAddProduct(data.open);\n                                    setIsEditingProduct(false);\n                                },\n                                onSubmit: (param)=>{\n                                    let { productName, printing, sizes } = param;\n                                    // handle your submit logic here\n                                    console.log({\n                                        productName,\n                                        printing,\n                                        sizes\n                                    });\n                                    setShowAddProduct(false);\n                                    router.reload();\n                                },\n                                isEditingProduct: isEditingProduct,\n                                editProductData: editProductData,\n                                productTypes: productTypes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 855,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative ag-theme-alpine !rounded-md\",\n                                    style: {\n                                        height: \"calc(100vh - 180px)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_5__.AgGridReact, {\n                                            rowData: requestRowData,\n                                            ref: gridRef,\n                                            requestColumnDefs: requestColumnDefs,\n                                            defaultColDef: defaultColDef,\n                                            suppressRowClickSelection: true,\n                                            pagination: true,\n                                            paginationPageSize: pageSize,\n                                            onPageSizeChanged: handlePageSizeChange,\n                                            tooltipShowDelay: 0,\n                                            tooltipHideDelay: 1000,\n                                            onGridReady: handleGridReady\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 877,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-start mt-2 pagination-style\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"page-size-select pagination\",\n                                                className: \"inputs\",\n                                                children: [\n                                                    \"Show\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"page-size-select\",\n                                                        onChange: handlePageSizeChange,\n                                                        value: pageSize,\n                                                        className: \"focus:outline-none\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 10,\n                                                                children: \"10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 899,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 15,\n                                                                children: \"15\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 900,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 25,\n                                                                children: \"25\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 901,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 50,\n                                                                children: \"50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 902,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 100,\n                                                                children: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                lineNumber: 903,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" \",\n                                                    \"Entries\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                lineNumber: 891,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 890,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                    lineNumber: 873,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 872,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                        lineNumber: 725,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.FluentProvider, {\n                        theme: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.webLightTheme,\n                        className: \"!bg-transparent\",\n                        style: {\n                            fontFamily: \"poppinsregular\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.Dialog, {\n                                open: showStatusDialog,\n                                onOpenChange: (_, data)=>{\n                                    if (!data.open) {\n                                        setIsValidCancelReason(true);\n                                        setCancelledReasonapi(\"\"); // Also clear the text\n                                    }\n                                    setShowStatusDialog(data.open);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogTrigger, {\n                                        disableButtonEnhancement: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"none\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 927,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 926,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogSurface, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogBody, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogTitle, {\n                                                    children: \"Update Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 931,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_17__.DialogContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mb-6\",\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                statusAction === 2 ? \"approve\" : statusAction === 3 ? \"reject\" : statusAction === 4 ? \"mark as ordered\" : \"update\",\n                                                                \" \",\n                                                                \"this request ?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        statusAction === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                            rows: \"8\",\n                                                            value: cancelledReasonapi,\n                                                            onChange: (e)=>{\n                                                                setCancelledReasonapi(e.target.value);\n                                                                if (e.target.value) {\n                                                                    setIsValidCancelReason(true);\n                                                                }\n                                                            },\n                                                            onBlur: (e)=>{\n                                                                const trimmedValue = trimInputText(e.target.value);\n                                                                setCancelledReasonapi(trimmedValue);\n                                                            },\n                                                            placeholder: \"Provide reason for rejection...\",\n                                                            maxLength: \"500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 945,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        statusAction === 3 && !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"Please Provide reason for cancellation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"8px\",\n                                                                marginTop: \"16px\"\n                                                            },\n                                                            className: \"flex justify-end\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        setShowStatusDialog(false);\n                                                                        setCancelledReasonapi(\"\");\n                                                                    },\n                                                                    \"data-modal-hide\": \"default-modal\",\n                                                                    type: \"button\",\n                                                                    className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                    lineNumber: 972,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        // Handle status update logic here\n                                                                        console.log(\"Updating status:\", statusAction, selectedRowData);\n                                                                        updateSelectedRowStatus(statusAction, cancelledReasonapi, selectedRowData);\n                                                                    },\n                                                                    \"data-modal-hide\": \"default-modal\",\n                                                                    type: \"button\",\n                                                                    className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                                    children: \"Continue\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                                    lineNumber: 980,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                            lineNumber: 968,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                            lineNumber: 930,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                        lineNumber: 929,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                                lineNumber: 916,\n                                columnNumber: 11\n                            }, undefined),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                        lineNumber: 911,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\pages\\\\ppe-consumable.js\",\n                lineNumber: 724,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(PPEConsumables, \"j80CPzQ/fhm9JozSpRdK+Ar7Bt0=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_9__.useLoading\n    ];\n});\n_c = PPEConsumables;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PPEConsumables);\n// Helper function to derive unit from package quantity\nfunction deriveUnitFromPackageQuantity(packageQuantity) {\n    if (!packageQuantity) return \"Unit\";\n    const lower = packageQuantity.toLowerCase();\n    if (lower.includes(\"box\")) return \"Box\";\n    if (lower.includes(\"pack\")) return \"Pack\";\n    if (lower.includes(\"pair\")) return \"Pair\";\n    return \"Unit\";\n}\nvar _c;\n$RefreshReg$(_c, \"PPEConsumables\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/ppe-consumable.js\n"));

/***/ })

});
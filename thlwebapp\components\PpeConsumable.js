import React, { useEffect, useRef, useState, Fragment } from "react";
import Select from "react-select";
import { toast } from "react-toastify";
import { useRouter } from "next/router";
import Cookies from "js-cookie";
import { apiConfig } from "@/services/apiConfig";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPenToSquare,
  faInfo,
  faXmark,
  // faCircleCheck,
  faSquareCheck,
} from "@fortawesome/free-solid-svg-icons";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles//ag-grid.css";
import "ag-grid-community/styles//ag-theme-alpine.css";
import { faFloppyDisk, faTrash } from "@fortawesome/free-solid-svg-icons";
import { Dialog, Transition } from "@headlessui/react";
import { logout } from "@/utils/secureStorage";
import {
  faXmarkCircle,
  faCircleCheck,
} from "@fortawesome/free-regular-svg-icons";
import productStatusRenderer from "@/utils/renderer/productStatusRenderer";
import { forEach } from "lodash";

const customSelectStyles = {
  control: (base) => ({
    ...base,
    height: "28px",
    minHeight: "28px",
  }),
  valueContainer: (provided) => ({
    ...provided,
    height: "28px",
    padding: "0 6px",
  }),
  input: (provided) => ({
    ...provided,
    margin: "0px",
  }),
  indicatorSeparator: () => ({
    display: "none",
  }),
  indicatorsContainer: (provided) => ({
    ...provided,
    height: "28px",
  }),
};

const emptyItem = {
  product: null,
  size: null,
  quantity: "",
  nameForPrinting: "",
  comments: "",
};
const PpeConsumable = ({
  userData,
  pageType,
  sites,
  OrderRequestData,
  ppeId = null,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [nameOfOriginator, setNameOfOriginator] = useState("");
  const [emailOfOriginator, setEmailOfOriginator] = useState("");
  const [siteData, setSiteData] = useState({});

  const [items, setItems] = useState([{ ...emptyItem }]);
  const [requiredDate, setRequiredDate] = useState("");
  const [createdDate, setCreatedDate] = useState("");
  const [site, setSite] = useState(sites?.[0] || null);
  const [loading, setLoading] = useState(false);
  const [productsData, setProductsData] = useState();
  const router = useRouter();
  const [currentItem, setCurrentItem] = useState({ ...emptyItem });
  const [savedItems, setSavedItems] = useState([]);
  const gridRef = useRef(null);
  // console.log("dsksldibiasdbilsdbv", siteData);

  // Add validation states for current item
  // const [productValid, setProductValid] = useState("");
  // const [quantityValid, setQuantityValid] = useState("");

  const [dateWarning, setDateWarning] = useState("");

  // const [isEditMode, setIsEditMode] = useState();
  const [requestStatus, setRequestStatus] = useState(5);
  const [requestStatusList, setRequestStatusList] = useState("");
  const [errors, setErrors] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [popupType, setPopupType] = useState("");
  const [cancelledReasonapi, setCancelledReasonapi] = useState("");
  const [isValidCancelReason, setIsValidCancelReason] = useState(true);
  const [editingRowId, setEditingRowId] = useState(null);
  const [gridApi, setGridApi] = useState(null);

  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const closeModal = (e) => {
    if (e) {
      e.preventDefault();
    }
    setIsValidCancelReason(true);
    setCancelledReasonapi("");
    setIsOpen(false);
  };
  //#region Load data
  useEffect(() => {
    setTimeout(function () {
      Cookies.set("rawWarning", true, { expires: 365 });
      Cookies.remove("finishWarning");
    }, 2000);

    if (pageType == "add" && userData) {
      setNameOfOriginator(userData.name);
    }
    if (OrderRequestData || OrderRequestData?.length > 0) {
      // console.log("OrderRequestData", OrderRequestData);
      if (OrderRequestData[0]?.action_id) {
        setRequestStatus(OrderRequestData[0]?.action_id);
        if (OrderRequestData[0]?.action_id !== 5) {
          setSubmitted(true);
        }
      }
      if (OrderRequestData[0]?.site_id) {
        setSite({
          label: OrderRequestData[0]?.site_name,
          value: OrderRequestData[0]?.site_id,
        });
      }
      if (OrderRequestData[0]?.required_date) {
        setRequiredDate(
          OrderRequestData[0]?.required_date
            ? new Date(OrderRequestData[0]?.required_date).toLocaleDateString(
                "en-CA",
                {
                  year: "numeric",
                  month: "2-digit",
                  day: "2-digit",
                }
              )
            : ""
        );
      }

      if (OrderRequestData[0]?.created_at) {
        setCreatedDate(
          OrderRequestData[0]?.created_at
            ? new Date(OrderRequestData[0]?.created_at).toLocaleDateString(
                "en-CA",
                {
                  year: "numeric",
                  month: "2-digit",
                  day: "2-digit",
                }
              )
            : ""
        );
      }
      if (OrderRequestData[0].user_name) {
        // setNameOfOriginator();
        setNameOfOriginator(OrderRequestData[0].user_name);
      }
      if (OrderRequestData[0].actioned_by_email) {
        // setNameOfOriginator();
        setEmailOfOriginator(OrderRequestData[0].actioned_by_email);
      }
      // setrowdata
      if (OrderRequestData && Array.isArray(OrderRequestData)) {
        console.log("load data", OrderRequestData, requestStatus);
        const STATUS_MAP = {
          1: "Pending Review",
          2: "Approved",
          3: "Rejected",
          4: "Ordered",
          5: "Draft",
        };
        const mappedItems = OrderRequestData.map((item) => ({
          id: item.order_request_items,
          product: { value: item.product_id, label: item.product_name },
          size: { value: item.size, label: item.size || "N/A" },
          quantity: item.quantity,
          nameForPrinting: item.name_for_printing,
          comments: (item.action_id === 3 ? `Rejected Reason: ${item.latest_comment}` : item.comments) || "",
          updateFlag: 0,
          status: STATUS_MAP[item.action_id],
        }));
        const uniqueValues = [
          ...new Set(OrderRequestData.map((item) => item.action_id)),
        ];
        setRequestStatusList(uniqueValues);
        setSavedItems(mappedItems);
        console.log("uniqueValues", uniqueValues);
      }
    }
  }, [OrderRequestData]);
  //#endregion Load data

  //#region ag-grid

  const onRowSelected = (event) => {
    if (event.node.isSelected()) {
      setEditingRowId(event.data.id);
      setCurrentItem(event.data);
    }
  };

  const onGridReady = (params) => {
    setGridApi(params.api);
  };

  // inside your component
  const getRowClass = (params) => {
    return params.data.id === editingRowId ? "bg-blue-100" : "";
  };

  const IconsRenderer = (props) => {
    const handleDelete = (event) => {
      event.preventDefault();
      const selectedRow = props.data;
      const updatedData = savedItems.filter((item) => item !== selectedRow);
      setSavedItems(updatedData);
    };

    const handleEdit = (event) => {
      event.preventDefault();
      const selectedRow = props.data;
      setHasUnsavedChanges(true);
      setEditingRowId(selectedRow.id);
      // setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));

      const updatedData = savedItems.filter((item) => item !== selectedRow);
      setSavedItems(updatedData);

      const handleRowUpdate = (actionId) => () => {
        setSelectedRowData(params.data);
        setStatusAction(actionId);
        setShowStatusDialog(true);
      };

      // event.preventDefault();
      // const selectedRow = props.data;
      // setEditingRowId(selectedRow.id || props.node.id);
      // // const updatedData = savedItems.filter((item) => item !== selectedRow);
      // // setSavedItems(updatedData);
      //   setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));

      // Populate the form with the selected row data
      setCurrentItem({
        id: selectedRow.id,
        product: selectedRow.product,
        size: selectedRow.size,
        quantity: selectedRow.quantity,
        nameForPrinting: selectedRow.nameForPrinting,
        nameForPrintingFlag: selectedRow.nameForPrinting, //TODO
        comments: selectedRow.comments,
        updateFlag: 1,
      });
    };

    return (
      <div className="flex flex-row gap-4 justify-center text-skin-primary">
        <button
          onClick={handleEdit}
          className={`${requestStatus !== 5 || !requestStatus ? "hidden" : ""}`}
        >
          <FontAwesomeIcon icon={faPenToSquare} />
        </button>
        <button
          onClick={handleDelete}
          className={`${
            requestStatus !== 5 || !requestStatus ? "hidden" : ""
          } text-red-500`}
        >
          <FontAwesomeIcon icon={faTrash} />
        </button>
        <button>
          <FontAwesomeIcon
            onClick={() => {
              if (!validate()) return;
              setPopupType("approveSingleItem");
              setIsOpen(true);
            }}
            // onClick={handleRowUpdate(4)}
            icon={faCircleCheck}
            className={`${requestStatus !== 1 || !requestStatus ? "hidden" : ""
              } text-green-500`}
          />
        </button>
        <button>
          <FontAwesomeIcon
            // onClick={handleRowUpdate(4)}
            onClick={() => {
              if (!validate()) return;
              setPopupType("rejectSingleItem");
              setIsOpen(true);
            }}
            icon={faXmarkCircle}
            className={`${requestStatus !== 1 || !requestStatus ? "hidden" : ""
              } text-red-500`}
          />
        </button>
        <button>
          <FontAwesomeIcon
            // onClick={handleRowUpdate(4)}
            onClick={() => {
              if (!validate()) return;
              setPopupType("markSingleItemOrdered");
              setIsOpen(true);
            }}
            icon={faSquareCheck}
            className={`${
              requestStatus !== 2 || !requestStatus ? "hidden" : ""
            } text-green-500`}
          />
        </button>
      </div>
    );
  };

  // Column definitions for the grid
  const columnDefs = [
    {
      headerName: "Product",
      field: "product.label",
      flex: 2,
      headerClass: "header-with-border",
    },
    {
      headerName: "Size",
      field: "size.label",
      flex: 1,
      headerClass: "header-with-border",
    },
    {
      headerName: "Quantity",
      field: "quantity",
      flex: 1,
      headerClass: "header-with-border",
    },
    {
      headerName: "Name for Printing",
      field: "nameForPrinting",
      flex: 2,
      headerClass: "header-with-border",
    },
    {
      headerName: "Comments",
      field: "comments",
      flex: 2,
      headerClass: "header-with-border",
    },
    {
      headerName: "Status",
      field: "status",
      hide: requestStatus === 5 || !requestStatus, // column won't show at all
      cellRenderer: productStatusRenderer,
      cellStyle: () => ({ justifyContent: "center" }),
      flex: 1.25,
      filterParams: {
        values: ["Pending Review", "Approved", "Rejected", "Ordered", "Draft"],
      },
    },
    {
      headerName: "Actions",
      cellRenderer: IconsRenderer,
      headerClass: "header-with-border",
      flex: 1,
      // cellStyle: { justifyContent: "center", paddingRight: "10px" },
    //   hide:
    //     requestStatus == 2 ||
    //     requestStatus == 3 ||
    //     requestStatus === 1 ||
    //     requestStatus == 4, // column won't show at all
    },
  ];
  //#endregion ag-grid

  //#region api's
  function getAllProducts() {
    let serverAddress = apiConfig.serverAddress;
    return fetch(`${serverAddress}ppe-consumables/all-products`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
    })
      .then(async (res) => {
        if (res.status == 502) {
          return;
        }
        if (res.status === 200) {
          return res.json();
        }
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async () => {
            await logout();
            router.push("/login");
          }, 3000);
        }
        throw new Error("Failed to fetch products");
      })
      .catch((error) => {
        console.error(error);
        toast.error(`Failed to fetch products: ${error.message}`);
        throw error;
      });
  }
  function getAllSites() {
    let serverAddress = apiConfig.serverAddress;
    return fetch(`${serverAddress}ppe-consumables/sites`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
    }).then(async (res) => {
      if (res.status == 502) {
        return;
      }
      if (res.status === 200) {
        return res.json();
      }
      if (res.status === 400) {
        toast.error(
          "There was an error with your request. Please check your data and try again."
        );
      } else if (res.status === 401) {
        toast.error("Your session has expired. Please log in again.");
        setTimeout(async () => {
          await logout();
          router.push("/login");
        }, 3000);
      }
      throw new Error("Failed to fetch products");
    });
  }
  //#endregion api's
  useEffect(() => {
    const fetchSites = async () => {
      try {
        const siteData = await getAllSites();
        const formattedSites = siteData?.map((site) => ({
          value: site.id,
          label: site.name,
        }));
        setSiteData(formattedSites);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching Sites", error);
      }
    };
    const fetchProducts = async () => {
      try {
        const productsJson = await getAllProducts();
        const filterOutHiddenProducts = productsJson.filter(
          (product) => !product.IsProductHidden
        );
        const formattedProducts = filterOutHiddenProducts?.map((product) => ({
          value: product.ProductId,
          label: product.ProductName,
          size_required: product.IsSizeRequired || product.size_required,
          sizes: product.AvailableSizes
            ? product.AvailableSizes.split(", ").map((size) => ({
                value: size.toLowerCase().replace(/\s+/g, ""),
                label: size,
              }))
            : [],
          productCode: product.ProductCode,
          packageQuantity: product.PackageQuantity,
          productType: product.ProductType,
          name_printable: product.name_printable,
        }));

        setProductsData(formattedProducts);
      } catch (error) {
        console.error("Error fetching products:", error);
      }
    };

    fetchProducts();
    fetchSites();
    setLoading(false);
  }, []);

    const updateSelectedRowStatusForSingleItem = (updatedStatusID, params) => {
      if (updatedStatusID == 3){
        if(cancelledReasonapi) {
          setIsValidCancelReason(true);
        } else {
          setIsValidCancelReason(false);
          return;
        }
      } 
  
      console.log("sssssssssssssssssssssssssssssssssssss", params);
      const apiPayload = {
        request_no: params.id,
        item_number: params.item_number,
        ProductName: params.product_name,
        Size: params.size,
        Quantity: params.quantity,
        NameForPrinting: params.NameForPrinting,
        Comments: updatedStatusID == 3 ? cancelledReasonapi : params.comment,
        RequestItemID: params.request_item_id,
        commentOnUpdatingRequest: cancelledReasonapi,
        action_id: updatedStatusID,
        SubmitterUserID: userData.user_id,
        SubmitterEmail: userData?.email,
        orignatorEmail: params?.orignatorEmail,
        cancelledReasonapi: cancelledReasonapi,
      };
  
  
      try {
        let serverAddress = apiConfig.serverAddress;
        fetch(
          `${serverAddress}ppe-consumables/update-product-request-items/${params.id}`,
          {
            method: "Post",
            headers: {
              "content-type": "application/json",
            },
            credentials: "include",
            body: JSON.stringify(apiPayload),
          }
        )
          .then((res) => {
            if (res.status === 200) {
              return res.json();
            } else if (res.status === 401) {
              toast.error("Your session has expired. Please log in again.");
              setTimeout(async () => {
                await logout();
                const redirectUrl = `/login?redirect=${encodeURIComponent(
                  window.location.pathname
                )}`;
                router.push(redirectUrl);
              }, 3000);
              return null;
            } else {
              toast.error("Failed to save product.");
              setLoading(false);
            }
          })
          .then((json) => {
            if (json && json.msg) {
              toast.success(json.msg, {
                position: "top-right",
              });
              setTimeout(() => {
                router.replace("/ppe-consumable");
              }, 2000);
            }
            getData().then((data) => {
              const grouped = data?.reduce((acc, row) => {
                if (!acc[row.request_id]) {
                  acc[row.request_id] = [];
                }
                acc[row.request_id].push(row);
                return acc;
              }, {});
              const STATUS_MAP = {
                1: "Pending Review",
                2: "Approved",
                3: "Rejected",
                4: "Ordered",
                5: "Draft",
              };
              const counters = {};
              const formattedData = data?.map((row) => {
                const siblings = grouped[row.request_id];
                const index = siblings.findIndex(r => r.request_item_id === row.request_item_id) + 1;
  
                counters[row.request_id] = (counters[row.request_id] || 0) + 1;
                return {
                  id: row.id,
                  request_id: `${row.request_id} - ${row.item_number}`, // stable per request_id
                  item_number: row.item_number, // stable per request_id
                  required_date: new Date(row.required_date)
                    .toISOString()
                    .split("T")[0],
                  site_name: row.site_name,
                  product_name: row.product_name || "Unnamed Product",
                  requestor: row.user_name,
                  orignatorEmail: row.actioned_by_email,
                  comment: (row.action_id === 3 ? `Rejected Reason: ${row.latest_comment}` : row.comments) || "",
                  size: row.size,
                  quantity: row.quantity,
                  NameForPrinting: row.name_for_printing,
                  status: STATUS_MAP[row.action_id],
                  request_item_id: row.request_item_id,
                  
                };
              });
              setRequestRowData(formattedData);
              setCancelledReasonapi("");
              // closeModal();
              // setShowStatusDialog(false);
            });
          });
      } catch (error) {
        console.log("errorin submitting the order", error);
      }
    };

  //#region Validate
  // Update the validate function to use savedItems instead of items
  const validate = () => {
    const newErrors = {};

    if (!site) newErrors.site = "Site is required.";
    if (!requiredDate) newErrors.requiredDate = "Required date is missing.";
    if (!savedItems || savedItems.length === 0) {
      newErrors.savedItems = "At least one item must be added.";
    }

    // Update state for field-level messages
    setErrors(newErrors);

    const hasErrors = Object.keys(newErrors).length > 0;
    if (hasErrors) {
      const errorMessages = Object.values(newErrors);

      // setPopupConfig({
      //   title: "Validation Error",
      //   message: errorMessages.join("\n"),
      //   type: "error",
      // });
      // setShowPopup(true); // trigger popup
      const errorsLength = Object.keys(newErrors).length;
      if (errorsLength > 1) {
        toast.error("Please fill all required fields.");
      } else if (errorsLength == 1 && newErrors.savedItems) {
        toast.error("Please add at least one item.");
      }
      return false;
    }

    return true;
  };
  const handleSiteAdd = (value) => {
    setSite(value);

    // Clear the error for site if value is valid
    setErrors((prev) => {
      const updated = { ...prev };
      if (value) {
        delete updated.site;
      }
      return updated;
    });
  };

  // Add date validation function
  const validateRequiredDate = (selectedDate) => {
    // setRequiredDate(selectedDate);

    setErrors((prev) => {
      const updated = { ...prev };
      if (selectedDate) {
        delete updated.requiredDate;
      }
      return updated;
    });

    if (!selectedDate) {
      setDateWarning("");
      return;
    }

    const today = new Date();
    const selected = new Date(selectedDate);
    const twoWeeksFromNow = new Date();
    twoWeeksFromNow.setDate(today.getDate() + 14);

    if (selected < twoWeeksFromNow) {
      setDateWarning(
        "Notice: Less than 2 weeks lead time may affect availability."
      );
    } else {
      setDateWarning("");
    }
  };

  // #region handlers

  const trimInputText = (input) => {
    return input.trim();
  };

  const handleCancelReason = (data) => {
    if (data) {
      setIsValidCancelReason(true);
    } else {
      setIsValidCancelReason(false);
    }
  };

  const handleSaveClick = (e) => {
    // e.preventDefault();
    if (hasUnsavedChanges) {
      setPopupType("unsavedWarning");
      setIsOpen(true); // your existing modal state
    } else {
      saveCurrentItem(e);
    }
  };

  const saveCurrentItem = (e) => {
    console.log("save order request");
    setErrors((prev) => {
      const updated = { ...prev };
      if (e) {
        delete updated.savedItems;
      }
      return updated;
    });

    if (!e) {
      console.log("returning here");
      setDateWarning("");
      return;
    }
    e.preventDefault();
    let count = 0;

    if (!currentItem.product) {
      console.log("++ product");
      count++;
      // setProductValid("Please select a product");
    } else {
      // setProductValid("");
    }

    if (!currentItem.quantity || currentItem.quantity < 1) {
      console.log("++ quantity");
      count++;
      // setQuantityValid("Please enter a valid quantity");
    } else {
      if (currentItem.quantity > 1 && currentItem.nameForPrinting) {
        // currentItem.nameForPrinting=""
        setCurrentItem((prev) => ({ ...prev, nameForPrinting: "" }));
      }
      // setQuantityValid("");
    }

    if (
      currentItem.product?.size_required &&
      currentItem.product?.sizes?.length &&
      !currentItem.size
    ) {
      console.log("++ product size");
      count++;
      // Add size validation if needed
    }

    if (count > 0) {
      toast.error(
        "Please fill in all mandatory fields to add items with valid quantity"
      );
      return;
    }
    //     if (editingRowId) {
    //         // Update existing item
    //   const updatedItems = savedItems.map(item =>
    //     item.id === editingRowId ? { ...currentItem, id: editingRowId } : item
    //   );
    //   setSavedItems(updatedItems);
    //   setEditingRowId(null);
    // } else {
    //   // Add new item
    //   setSavedItems([...savedItems, { ...currentItem, id: Date.now() }]);

    // }
    if (editingRowId) {
      // Add the edited item back to savedItems
      setSavedItems([
        ...savedItems,
        {
          ...currentItem,
          id: editingRowId,
          product: currentItem.product ? { ...currentItem.product } : null,
          size: currentItem.size ? { ...currentItem.size } : null,
        },
      ]);
      setEditingRowId(null);
    } else {
      // Add new item
      setSavedItems([
        ...savedItems,
        { ...currentItem, id: `row-${Date.now()}` },
      ]);
    }

    // Reset current item
    setCurrentItem({ ...emptyItem });
    setEditingRowId(null);
    setHasUnsavedChanges(false);
  };

  // Handle current item changes
  const handleCurrentItemChange = (field, value) => {
    if (field === "product") {
      // console.log("field-------------",value)
      // Reset other fields when product changes
      setCurrentItem((prev) => ({
        ...prev,
        [field]: value,
        size: null, // Reset size since different products have different available sizes
        quantity: "", // Reset quantity
        nameForPrinting: "", // Reset name for printing
        nameForPrintingFlag: "", // Reset name for printing
        comments: "", // Reset comments
      }));
    } else if (field === "quantity" && value > 1) {
      setCurrentItem((prev) => ({
        ...prev,
        [field]: value,
        nameForPrinting: "",
      }));
    } else {
      setCurrentItem((prev) => ({ ...prev, [field]: value }));
    }
  };

  //#region Save
  const handleSubmit = (status_ID) => {
    if (status_ID != 1 && !validate()) {
      return;
    }
    // console.log("sssssssssssssssssssssssssssssssssssssssssssssssssssss",savedItems);
    setLoading(true);
    let serverAddress = apiConfig.serverAddress;

    // Transform savedItems to API format
    const items = savedItems.map((item) => ({
      requestItemId: item.requestItemId,
      // RequestItemID: typeof item.id === 'string' && item.id.startsWith('row-') ? null : item.id,

      ProductID: item.product?.value, // ProductID
      ProductName: item.product?.label || item.product?.value, // ProductID
      Size: item.size?.label || null, // Size //TODO size id handel
      Quantity: Number(item.quantity) || 0, // Quantity (make sure it's numeric)
      NameForPrinting: item.nameForPrinting || "", // NameForPrinting
      Comments: item.comments || "", // Comments
    }));

    const apiPayload = {
      Status_id: status_ID,
      SubmitterUserID: userData?.user_id,
      TargetSiteID: site?.value || 3, // fallback to 3
      RequiredDate: requiredDate, // should be "YYYY-MM-DD"
      username: userData?.email || "system", // fallback username
      items: items, // array of items
      orignatorEmail: emailOfOriginator,
    };

    // console.log("items--------------------------------------------------------",items)

    try {
      fetch(`${serverAddress}ppe-consumables/create-order-request`, {
        method: "Post",
        headers: {
          "content-type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(apiPayload),
      })
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async () => {
              await logout();
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              router.push(redirectUrl);
            }, 3000);
            return null;
          } else {
            toast.error("Failed to save product.");
            setLoading(false);
          }
        })
        .then((json) => {
          if (json && json.msg) {
            toast.success(json.msg, {
              position: "top-right",
            });
            setTimeout(() => {
              router.replace("/ppe-consumable");
            }, 2000);
          }
        });
    } catch (error) {
      console.log("errorin submitting the order", error);
    }
  };

  //#region updated conditional
  const updateSelectedRowStatus = (
    updatedStatusID,
    existingStatusId,
  ) => {
    const STATUS_MAP = {
      1: "Pending Review",
      2: "Approved",
      3: "Rejected",
      4: "Ordered",
      5: "Draft",
    };
    const toBeUpdated = savedItems.filter(
      (item) => item.status == STATUS_MAP[existingStatusId]
    );

    if (toBeUpdated.length === 0) {
      toast.warning("No items found with the specified status");
      return;
    }

    setLoading(true);

    if (updatedStatusID == 3) {
      if (cancelledReasonapi) {
        setIsValidCancelReason(true);
        console.log("triggerrerererered", cancelledReasonapi);
      } else {
        console.log("triggerrerererered");
        setIsValidCancelReason(false);
        return;
      }
    }

    // console.log("sssssssssssssssssssssssssssssssssssss", toBeUpdated);
    const updatePromises = toBeUpdated.map(async (item) => {
      const apiPayload = {
        request_no: item.id,
        item_number: item.item_number,
        ProductName: item.product.label,
        Size: item.size.label,
        Quantity: item.quantity,
        NameForPrinting: item.nameForPrinting,
        Comments: updatedStatusID == 3 ? cancelledReasonapi : item.comments,
        RequestItemID: item.id,
        commentOnUpdatingRequest: cancelledReasonapi,
        action_id: updatedStatusID,
        SubmitterUserID: userData.user_id,
        SubmitterEmail: userData?.email,
        orignatorEmail: emailOfOriginator,
        cancelledReasonapi: cancelledReasonapi,
      };
      console.log("apiPayload", apiPayload); // Log for debugging purposes.
      const res = await fetch(
        `${apiConfig.serverAddress}ppe-consumables/update-product-request-items/${item.id}`,
        {
          method: "POST",
          headers: {
            "content-type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify(apiPayload),
        }
      );
      if (res.status === 200) {
        return res.json();
      } else if (res.status === 401) {
        toast.error("Your session has expired. Please log in again.");
        setTimeout(async () => {
          await logout();
          const redirectUrl = `/login?redirect=${encodeURIComponent(
            window.location.pathname
          )}`;
          router.push(redirectUrl);
        }, 3000);
        throw new Error("Unauthorized");
      } else {
        const errorText = await res.text();
        console.error("Update failed:", errorText);
        toast.error("Failed to update item status.");
        throw new Error("Update failed");
      }
    });

    Promise.all(updatePromises)
      .then((results) => {
        const successCount = results.filter((result) => result?.msg).length;
        if (successCount > 0) {
          toast.success(`Successfully updated ${successCount} item(s)`, {
            position: "top-right",
          });
          setTimeout(() => {
            router.replace("/ppe-consumable");
          }, 2000);
        } else {
          toast.error("No items were successfully updated");
          router.push("/ppe-consumable");
        }
      })
      .catch((error) => {
        console.error("Error updating items:", error);
        toast.error("Some items failed to update");
      })
      .finally(() => {
        setLoading(false);
      });
  };
  //#endregion updated conditional

  // #region update
  const handleUpdate = (action_id) => {
    if (!validate()) {
      return;
    }
    // handleSaveClick();
    setLoading(true);
    let serverAddress = apiConfig.serverAddress;

    console.log("savedItems", savedItems);
    const items = savedItems.map((item) => ({
      // RequestItemID: item.id,
      RequestItemID:
        typeof item.id === "string" && item.id.startsWith("row-")
          ? null
          : item.id,

      ProductID: item.product_id || item.product?.value, // ProductID
      ProductName: item.product?.label || item.product?.value, // ProductID
      Size: item.size?.label || null, // Size
      // SizeID: item.size?.value || null, //TODO no size handel
      SizeID:
        item.size?.value && !isNaN(Number(item.size.value))
          ? Number(item.size.value)
          : null, //TODO no size handel
      Quantity: Number(item.quantity) || 0, // Quantity (make sure it's numeric)
      NameForPrinting: item.nameForPrinting || "", // NameForPrinting
      Comments: item.comments || "", // Comments
    }));

    const apiPayload = {
      requestId: ppeId,
      action_id: action_id,
      submitterUserId: userData.user_id,
      username: userData?.email || "system",
      targetSiteId: site.value,
      requiredDate: requiredDate,
      items: items,
      comment: cancelledReasonapi,
      orignatorEmail: emailOfOriginator,
    };
    console.log(
      "apiPayload--------------------------------------------------------",
      apiPayload
    );

    try {
      fetch(`${serverAddress}ppe-consumables/update-order-request`, {
        method: "Post",
        headers: {
          "content-type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(apiPayload),
      })
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async () => {
              await logout();
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              router.push(redirectUrl);
            }, 3000);
            return null;
          } else {
            toast.error("Failed to save product.");
            setLoading(false);
          }
        })
        .then((json) => {
          if (json && json.msg) {
            toast.success(json.msg, {
              position: "top-right",
            });
            setTimeout(() => {
              router.replace("/ppe-consumable");
            }, 2000);
          }
        });
    } catch (error) {
      console.log("errorin submitting the order", error);
    }
    setLoading(false);
    setTimeout(() => {
      setLoading(false);
      toast.success("Request submitted!");
      router.push("/ppe-consumable");
    }, 1000);
  };

  useEffect(() => {
    setTimeout(function () {
      Cookies.set("rawWarning", true, { expires: 365 });
      Cookies.remove("finishWarning");
    }, 2000);

    if (pageType == "update") {
      setIsEdit(true);
    }
    if (pageType == "add" && userData) {
      setNameOfOriginator(userData.name);
    }
  }, [0]);

  return (
    <>
      <form
        onSubmit={handleSubmit}
        className="flex flex-col justify-start max-w-full  mx-auto mr-14"
      >
        <div className="">
          {/* <h2 className="text-lg font-semibold tracking-wide mb-4 pl-2">
            {pageType === "add"
              ? "Add PPE/Consumable Order"
              : "Edit PPE/Consumable Order"}
          </h2> */}
          <div className="flex flex-row gap-8 bg-white p-6 rounded-lg shadow-[0_0_1px_rgba(0,0,0,0.1)]">
            <div className="w-full">
              <label className="labels mb-2">Full Name</label>
              <input
                type="text"
                value={nameOfOriginator || ""}
                disabled
                className="block w-full px-4 text-gray-500 border rounded-lg form-input"
              />
            </div>
            <div className="w-full">
              <label className="labels mb-2">Email</label>
              <input
                type="text"
                value={emailOfOriginator || userData?.email || ""}
                disabled
                className="block w-full px-4 text-gray-500 border rounded-lg form-input"
              />
            </div>
            <div className="w-full">
              <label className="labels mb-2">Created Date</label>
              <input
                type="date"
                value={new Date().toLocaleDateString("en-CA")}
                disabled
                className="block w-full px-4 text-gray-500 border rounded-lg form-input"
              />
            </div>
            <div className="w-full">
              <label className="labels mb-2">
                Site<span className="text-red-500 ml-[2px]">*</span>
              </label>
              <Select
                value={site}
                onChange={(e) => {
                  setSite();
                  handleSiteAdd(e);
                }}
                options={siteData}
                isDisabled={sites?.length === 1 || submitted}
                styles={customSelectStyles}
                isClearable={true}
                className=" w-full"
              />
              {errors.site && <p style={{ color: "#ef4444" }}>{errors.site}</p>}
            </div>
            <div className="w-full">
              <label className="labels mb-2">
                Required Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                min={new Date().toISOString().split("T")[0]}
                value={requiredDate}
                onChange={(e) => {
                  setRequiredDate(e.target.value);
                  validateRequiredDate(e.target.value);
                }}
                className="block w-full px-4 border rounded-lg form-input"
                disabled={submitted}
              />
              {dateWarning && (
                <span className="text-orange-500 text-sm">{dateWarning}</span>
              )}
              {errors.requiredDate && (
                <p style={{ color: "#ef4444" }}>{errors.requiredDate}</p>
              )}
            </div>
            {/* <div className="w-full">
            <label className="labels mb-2">
              Required Date <span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              min={new Date().toISOString().split("T")[0]}
              value={requiredDate}
              onChange={(e) => setRequiredDate(e.target.value)}
              className="block w-full px-4 border rounded-lg form-input"
            />
          </div> */}
          </div>
        </div>

        <div className="mt-3">
          <div className="flex flex-row justify-between items-end my-4">
            <h3 className="flex mb-1 font-semibold tracking-wider text-base pl-2">
              Items
            </h3>
          </div>

          <div className="flex flex-col mb-28px bg-white p-6 rounded-lg py-8 shadow-[0_0_2px_rgba(0,0,0,0.2)]">
            <div className="flex flex-row gap-4 mb-4">
              <div className="w-1/4">
                <label className="labels mb-2">
                  Product <span className="text-red-500">*</span>
                </label>
                <Select
                  value={currentItem.product}
                  onChange={(val) => handleCurrentItemChange("product", val)}
                  options={productsData} //{productsData.ProductName} //TODO
                  styles={customSelectStyles}
                  className="reactSelectCustom w-full"
                  isSearchable
                  isClearable
                  isDisabled={submitted}
                />
              </div>
              <div className="w-1/6">
                <label className="labels mb-2">
                  Size
                  <span
                    className={`text-red-500 ${
                      !currentItem.product?.size_required ||
                      !currentItem.product?.sizes?.length ||
                      submitted
                        ? "hidden"
                        : ""
                    }`}
                  >
                    *
                  </span>
                </label>
                <Select
                  value={currentItem.size}
                  onChange={(val) => handleCurrentItemChange("size", val)}
                  options={currentItem.product?.sizes || []}
                  styles={customSelectStyles}
                  className="reactSelectCustom w-full"
                  isSearchable
                  isClearable
                  isDisabled={
                    !currentItem.product?.size_required ||
                    !currentItem.product?.sizes?.length ||
                    submitted
                  }
                />
              </div>
              <div className="w-1/6">
                <label className="labels mb-2">
                  Quantity <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  // min={0}
                  max={999}
                  value={currentItem.quantity}
                  onChange={(e) =>
                    handleCurrentItemChange(
                      "quantity",
                      e.target.value > 999 ? 999 : parseInt(e.target.value)
                    )
                  }
                  className="block w-full px-4 border rounded-lg form-input"
                  disabled={submitted}
                />
              </div>
              <div className="w-1/4">
                <label className="labels mb-2">Name for Printing</label>
                <input
                  type="text"
                  maxLength="50" //TODO change in db also
                  value={currentItem.nameForPrinting}
                  onChange={(e) =>
                    handleCurrentItemChange("nameForPrinting", e.target.value)
                  }
                  disabled={
                    currentItem.quantity !== 1 ||
                    submitted ||
                    !currentItem.product?.name_printable
                  }
                  className="block w-full px-4 border rounded-lg form-input"
                  placeholder="Only if quantity = 1"
                />
              </div>
              <div className="w-1/4">
                <label className="labels mb-2">Comments</label>
                <input
                  value={currentItem.comments}
                  maxLength="500"
                  onChange={(e) =>
                    handleCurrentItemChange("comments", e.target.value)
                  }
                  className="disabled:text-gray-400 disabled:bg-[#F6F3F3] block w-full h-8 px-4 border rounded-lg form-input resize-none"
                  rows={1}
                  disabled={submitted}
                />
              </div>
              <div className="flex items-end">
                {/* <p>Cancel Edit</p> */}
                <span
                  onClick={saveCurrentItem}
                  // onClick={handleSaveClick}
                  className={`px-2 py-1 2xl:px-3.5 border border-skin-primary rounded-md text-skin-primary cursor-pointer ${
                    submitted ? "hidden" : ""
                  }`}
                  disabled={submitted}
                >
                  <FontAwesomeIcon icon={faFloppyDisk} />
                </span>
              </div>
            </div>
            {/* 
            //#region grid
            */}
            <div
              className="ag-theme-alpine my-[24px]"
              style={{ height: 250, width: "100%" }}
            >
              <AgGridReact
                ref={gridRef}
                columnDefs={columnDefs}
                rowData={savedItems}
                rowHeight={35}
                getRowClass={getRowClass}
                onGridReady={onGridReady}
              />
              {/* 
            //#endregion grid
            */}

              {/* {errors[`item_${index}_product`] && (
                <p style={{ color: "#ef4444" }}>{errors[`item_${index}_product`]}</p>
                )} */}
            </div>
            {popupType === "unsavedWarning" && (
              <div>
                <p>
                  One item is being edited. If you continue, changes will be
                  lost.
                </p>
                <div className="flex justify-end gap-2">
                  <button
                    onClick={closeModal}
                    className="border px-4 py-2 rounded"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      saveCurrentItem(); // discard editing row
                      closeModal();
                    }}
                    className="bg-blue-600 text-white px-4 py-2 rounded"
                  >
                    Save Anyway
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex flex-row justify-end gap-4 rounded-lg  p-4 mt-2">
          <button
            type="button"
            onClick={() => {
              router.push("/ppe-consumable");
            }} // handle cancel with loader and all
            className="border border-skin-primary text-skin-primary rounded-md px-6"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={() => {
              if (pageType === "edit") {
                console.log("hasUnsavedChanges", hasUnsavedChanges);
                if (hasUnsavedChanges) {
                  setPopupType("Save");
                  setIsOpen(true);
                } else {
                  handleUpdate(5);
                }
              } else {
                handleSubmit(5);
              }
            }}
            disabled={loading || requestStatus === 1}
            className={`border border-skin-primary text-skin-primary rounded-md px-6 ${
              requestStatus !== 5 ? "hidden" : ""
            }`}
          >
            Save
          </button>
          <button
            type="button"
            onClick={() => {
              if (hasUnsavedChanges) {
                setPopupType("submitWhileEditing");
                setIsOpen(true);
              } else {
                if (!validate()) return;
                setPopupType("submit");
                setIsOpen(true);
              }
            }}
            className={`border border-skin-primary bg-skin-primary text-white rounded-md px-6 font-medium ${
              requestStatus !== 5 ? "hidden" : ""
            }`}
            disabled={loading || requestStatus === 1}
          >
            Submit
          </button>
          <button
            type="button"
            onClick={() => {
              if (!validate()) return;
              setPopupType("reject");
              setIsOpen(true);
            }}
            className={`bg-red-600 text-white rounded-md px-6 font-medium ${
              !requestStatusList.includes(1) ? "hidden" : ""
            }  ${
              userData.role_id === 6 ||
              userData.role_id === 1 ||
              userData.role_id === 5
                ? ""
                : "hidden"
            }`}
          >
            Reject All Pending Requests
          </button>
          <button
            type="button"
            onClick={() => {
              if (!validate()) return;
              setPopupType("approve");
              setIsOpen(true);
            }}
            className={`bg-green-600 text-white rounded-md px-6 font-medium ${
              !requestStatusList.includes(1) ? "hidden" : ""
            }  ${
              userData.role_id === 6 ||
              userData.role_id === 1 ||
              userData.role_id === 5
                ? ""
                : "hidden"
            }`}
          >
            Approve All Pending Requests
          </button>
          <button
            type="button"
            // onClick={() => {
            //   updateSelectedRowStatus(4, 2);
            // }}
            onClick={() => {
              if (!validate()) return;
              setPopupType("markOrdered");
              setIsOpen(true);
            }}
            className={`bg-green-600 text-white rounded-md px-6 font-medium ${
              requestStatusList.includes(2) ? "" : "hidden"
            }  ${
              userData.role_id === 6 ||
              userData.role_id === 1 ||
              userData.role_id === 5
                ? ""
                : "hidden"
            }`}
            // disabled={loading || requestStatus === 1}
            // hide = {requestStatus !== 1}
          >
            Mark All Approved Requests to Ordered
          </button>
        </div>
      </form>
      {/* 
      //#region popup dialog
      */}
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-10"
          onClose={(value) => {
            if (popupType !== "reject" || isValidCancelReason) {
              closeModal();
            }
          }}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  {/* <!-- Modal content --> */}
                  <div className="relative bg-white rounded-lg shadow">
                    {/* <!-- Modal header --> */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "}
                        {`Confirm ${
                          popupType == "submit"
                            ? "Submission"
                            : popupType == "reject"
                            ? "Rejection"
                            : popupType == "approve"
                            ? "Approval"
                            : popupType == "markOrdered"
                            ? "markOrdered"
                            : popupType == "Save"
                            ? "Save"
                            : popupType == "submitWhileEditing"
                            ? "Submission"
                            : " "
                        }`}
                      </h3>
                      <button
                        onClick={closeModal}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    {/* <!-- Modal body --> */}
                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                        Are you sure you want to{" "}
                        {popupType == "submit"
                          ? "submit"
                          : popupType == "rejectSingleItem"
                          ? "reject"
                          : popupType == "approveSingleItem"
                          ? "approve"
                          : popupType == "markSingleItemOrdered"
                          ? "mark approved items as ordered"
                          : popupType == "reject"
                          ? "reject"
                          : popupType == "approve"
                          ? "approve"
                          : popupType == "markOrdered"
                          ? "mark approved items as ordered"
                          : popupType == "Save"
                          ? "save? there is a item being edited,\n it will be lost if you Save "
                          : popupType == "submitWhileEditing"
                          ? "Submit? there is a item being edited,\n it will be lost if you Submit "
                          : " "}{" "}
                        this request?{" "}
                      </p>{" "}
                      {popupType == "reject" || popupType == "rejectSingleItem" && (
                        <div>
                          {/* <h3>rejection reason</h3> */}
                          <textarea
                            className="flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2"
                            rows="8"
                            value={cancelledReasonapi}
                            onChange={(e) => {
                              setCancelledReasonapi(e.target.value);
                              if (e.target.value) {
                                setIsValidCancelReason(true);
                              }
                            }}
                            onBlur={(e) => {
                              const trimmedValue = trimInputText(
                                e.target.value
                              );
                              setCancelledReasonapi(trimmedValue);
                            }}
                            placeholder="Provide reason for rejection..."
                            maxLength="500"
                          ></textarea>
                          {(popupType == "reject" || popupType == "rejectSingleItem") && !isValidCancelReason && (
                            <span className="text-red-500">
                              Please Provide reason for cancellation
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    {/* <!-- Modal footer --> */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={() => {
                          closeModal(), setCancelledReasonapi("");
                        }}
                        data-modal-hide="default-modal"
                        type="button"
                        className="border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Cancel
                      </button>
                      <button
                        onClick={() => {
                          if (
                            popupType === "reject" &&
                            !cancelledReasonapi?.trim()
                          ) {
                            setIsValidCancelReason(false);
                            return;
                          }
                          if (pageType == "add") {
                            if (popupType == "submit") {
                              handleSubmit(1);
                            } else if (popupType === "submitWhileEditing") {
                              handleSubmit(1);
                            }
                          } else {
                            if (popupType == "submit") {
                              handleUpdate(1);
                            } else if (popupType === "reject") {
                              updateSelectedRowStatus(3, 1);
                            } else if (popupType === "approve") {
                              updateSelectedRowStatus(2, 1);
                            } else if (popupType === "rejectSingleItem") {
                              updateSelectedRowStatusForSingleItem(3);
                            } else if (popupType === "approveSingleItem") {
                              updateSelectedRowStatusForSingleItem(2);
                            } else if (popupType === "Save") {
                              handleUpdate(5);
                            } else if (popupType === "markOrdered") {
                              updateSelectedRowStatus(4, 2);
                            } else if (popupType === "markSingleItemOrdered") {
                              updateSelectedRowStatusForSingleItem(4, 2);
                            } else if (popupType === "submitWhileEditing") {
                              handleUpdate(1);
                            }
                          }
                          setCancelledReasonapi("");
                          closeModal();
                        }}
                        data-modal-hide="default-modal"
                        type="button"
                        className="text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center h-full border border-skin-primary"
                      >
                        Continue
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default PpeConsumable;

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable/[ppeId]/edit",{

/***/ "./components/PpeConsumable.js":
/*!*************************************!*\
  !*** ./components/PpeConsumable.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fortawesome/free-regular-svg-icons */ \"./node_modules/@fortawesome/free-regular-svg-icons/index.mjs\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lodash/forEach */ \"./node_modules/lodash/forEach.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(lodash_forEach__WEBPACK_IMPORTED_MODULE_12__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    valueContainer: (provided)=>({\n            ...provided,\n            height: \"28px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: ()=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst emptyItem = {\n    product: null,\n    size: null,\n    quantity: \"\",\n    nameForPrinting: \"\",\n    comments: \"\"\n};\nconst PpeConsumable = (param)=>{\n    let { userData, pageType, sites, OrderRequestData, ppeId = null } = param;\n    var _currentItem_product, _currentItem_product_sizes, _currentItem_product1, _currentItem_product2, _currentItem_product3, _currentItem_product_sizes1, _currentItem_product4, _currentItem_product5;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [nameOfOriginator, setNameOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [emailOfOriginator, setEmailOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [siteData, setSiteData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            ...emptyItem\n        }\n    ]);\n    const [requiredDate, setRequiredDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [createdDate, setCreatedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [site, setSite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((sites === null || sites === void 0 ? void 0 : sites[0]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [productsData, setProductsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [currentItem, setCurrentItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        ...emptyItem\n    });\n    const [savedItems, setSavedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // console.log(\"dsksldibiasdbilsdbv\", siteData);\n    // Add validation states for current item\n    // const [productValid, setProductValid] = useState(\"\");\n    // const [quantityValid, setQuantityValid] = useState(\"\");\n    const [selectedRowData, setSelectedRowData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dateWarning, setDateWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // const [isEditMode, setIsEditMode] = useState();\n    const [requestStatus, setRequestStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const [requestStatusList, setRequestStatusList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [submitted, setSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popupType, setPopupType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [editingRowId, setEditingRowId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gridApi, setGridApi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const closeModal = (e)=>{\n        if (e) {\n            e.preventDefault();\n        }\n        setIsValidCancelReason(true);\n        setCancelledReasonapi(\"\");\n        setIsOpen(false);\n    };\n    //#region Load data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n        if (OrderRequestData || (OrderRequestData === null || OrderRequestData === void 0 ? void 0 : OrderRequestData.length) > 0) {\n            var _OrderRequestData_, _OrderRequestData_1, _OrderRequestData_2, _OrderRequestData_3;\n            // console.log(\"OrderRequestData\", OrderRequestData);\n            if ((_OrderRequestData_ = OrderRequestData[0]) === null || _OrderRequestData_ === void 0 ? void 0 : _OrderRequestData_.action_id) {\n                var _OrderRequestData_4, _OrderRequestData_5;\n                setRequestStatus((_OrderRequestData_4 = OrderRequestData[0]) === null || _OrderRequestData_4 === void 0 ? void 0 : _OrderRequestData_4.action_id);\n                if (((_OrderRequestData_5 = OrderRequestData[0]) === null || _OrderRequestData_5 === void 0 ? void 0 : _OrderRequestData_5.action_id) !== 5) {\n                    setSubmitted(true);\n                }\n            }\n            if ((_OrderRequestData_1 = OrderRequestData[0]) === null || _OrderRequestData_1 === void 0 ? void 0 : _OrderRequestData_1.site_id) {\n                var _OrderRequestData_6, _OrderRequestData_7;\n                setSite({\n                    label: (_OrderRequestData_6 = OrderRequestData[0]) === null || _OrderRequestData_6 === void 0 ? void 0 : _OrderRequestData_6.site_name,\n                    value: (_OrderRequestData_7 = OrderRequestData[0]) === null || _OrderRequestData_7 === void 0 ? void 0 : _OrderRequestData_7.site_id\n                });\n            }\n            if ((_OrderRequestData_2 = OrderRequestData[0]) === null || _OrderRequestData_2 === void 0 ? void 0 : _OrderRequestData_2.required_date) {\n                var _OrderRequestData_8, _OrderRequestData_9;\n                setRequiredDate(((_OrderRequestData_8 = OrderRequestData[0]) === null || _OrderRequestData_8 === void 0 ? void 0 : _OrderRequestData_8.required_date) ? new Date((_OrderRequestData_9 = OrderRequestData[0]) === null || _OrderRequestData_9 === void 0 ? void 0 : _OrderRequestData_9.required_date).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if ((_OrderRequestData_3 = OrderRequestData[0]) === null || _OrderRequestData_3 === void 0 ? void 0 : _OrderRequestData_3.created_at) {\n                var _OrderRequestData_10, _OrderRequestData_11;\n                setCreatedDate(((_OrderRequestData_10 = OrderRequestData[0]) === null || _OrderRequestData_10 === void 0 ? void 0 : _OrderRequestData_10.created_at) ? new Date((_OrderRequestData_11 = OrderRequestData[0]) === null || _OrderRequestData_11 === void 0 ? void 0 : _OrderRequestData_11.created_at).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if (OrderRequestData[0].user_name) {\n                // setNameOfOriginator();\n                setNameOfOriginator(OrderRequestData[0].user_name);\n            }\n            if (OrderRequestData[0].actioned_by_email) {\n                // setNameOfOriginator();\n                setEmailOfOriginator(OrderRequestData[0].actioned_by_email);\n            }\n            // setrowdata\n            if (OrderRequestData && Array.isArray(OrderRequestData)) {\n                console.log(\"load data\", OrderRequestData, requestStatus);\n                const STATUS_MAP = {\n                    1: \"Pending Review\",\n                    2: \"Approved\",\n                    3: \"Rejected\",\n                    4: \"Ordered\",\n                    5: \"Draft\"\n                };\n                const mappedItems = OrderRequestData.map((item)=>({\n                        id: item.order_request_items,\n                        product: {\n                            value: item.product_id,\n                            label: item.product_name\n                        },\n                        size: {\n                            value: item.size,\n                            label: item.size || \"N/A\"\n                        },\n                        quantity: item.quantity,\n                        nameForPrinting: item.name_for_printing,\n                        comments: (item.action_id === 3 ? \"Rejected Reason: \".concat(item.latest_comment) : item.comments) || \"\",\n                        updateFlag: 0,\n                        status: STATUS_MAP[item.action_id]\n                    }));\n                const uniqueValues = [\n                    ...new Set(OrderRequestData.map((item)=>item.action_id))\n                ];\n                setRequestStatusList(uniqueValues);\n                setSavedItems(mappedItems);\n                console.log(\"uniqueValues\", uniqueValues);\n            }\n        }\n    }, [\n        OrderRequestData\n    ]);\n    //#endregion Load data\n    //#region ag-grid\n    const onRowSelected = (event)=>{\n        if (event.node.isSelected()) {\n            setEditingRowId(event.data.id);\n            setCurrentItem(event.data);\n        }\n    };\n    const onGridReady = (params1)=>{\n        setGridApi(params1.api);\n    };\n    // inside your component\n    const getRowClass = (params1)=>{\n        return params1.data.id === editingRowId ? \"bg-blue-100\" : \"\";\n    };\n    const IconsRenderer = (props)=>{\n        const handleDelete = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n        };\n        const handleEdit = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            setHasUnsavedChanges(true);\n            setEditingRowId(selectedRow.id);\n            // setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n            const handleRowUpdate = (actionId)=>()=>{\n                    setSelectedRowData(params.data);\n                    setStatusAction(actionId);\n                    setShowStatusDialog(true);\n                };\n            // event.preventDefault();\n            // const selectedRow = props.data;\n            // setEditingRowId(selectedRow.id || props.node.id);\n            // // const updatedData = savedItems.filter((item) => item !== selectedRow);\n            // // setSavedItems(updatedData);\n            //   setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            // Populate the form with the selected row data\n            setCurrentItem({\n                id: selectedRow.id,\n                product: selectedRow.product,\n                size: selectedRow.size,\n                quantity: selectedRow.quantity,\n                nameForPrinting: selectedRow.nameForPrinting,\n                nameForPrintingFlag: selectedRow.nameForPrinting,\n                comments: selectedRow.comments,\n                updateFlag: 1\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-row gap-4 justify-center text-skin-primary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleEdit,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faPenToSquare\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDelete,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\", \" text-red-500\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faTrash\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 277,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    onClick: ()=>{\n                        setSelectedRowData(props.data);\n                        setPopupType(\"approveSingleItem\");\n                        setIsOpen(true);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faCircleCheck,\n                        className: \"\".concat(requestStatus !== 1 || !requestStatus ? \"hidden\" : \"\", \" text-green-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 279,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    onClick: ()=>{\n                        setSelectedRowData(props.data);\n                        setPopupType(\"rejectSingleItem\");\n                        setIsOpen(true);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmarkCircle,\n                        className: \"\".concat(requestStatus !== 1 || !requestStatus ? \"hidden\" : \"\", \" text-red-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    onClick: ()=>{\n                        setSelectedRowData(props.data);\n                        setPopupType(\"markSingleItemOrdered\");\n                        setIsOpen(true);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faSquareCheck,\n                        className: \"\".concat(requestStatus !== 2 || !requestStatus ? \"hidden\" : \"\", \" text-green-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Column definitions for the grid\n    const columnDefs = [\n        {\n            headerName: \"Product\",\n            field: \"product.label\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Size\",\n            field: \"size.label\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Quantity\",\n            field: \"quantity\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Name for Printing\",\n            field: \"nameForPrinting\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Comments\",\n            field: \"comments\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            hide: requestStatus === 5 || !requestStatus,\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: 1.25,\n            filterParams: {\n                values: [\n                    \"Pending Review\",\n                    \"Approved\",\n                    \"Rejected\",\n                    \"Ordered\",\n                    \"Draft\"\n                ]\n            }\n        },\n        {\n            headerName: \"Actions\",\n            cellRenderer: IconsRenderer,\n            headerClass: \"header-with-border\",\n            flex: 1\n        }\n    ];\n    //#endregion ag-grid\n    //#region api's\n    function getAllProducts() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-products\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to fetch products: \".concat(error.message));\n            throw error;\n        });\n    }\n    function getAllSites() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/sites\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        });\n    }\n    //#endregion api's\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const siteData = await getAllSites();\n                const formattedSites = siteData === null || siteData === void 0 ? void 0 : siteData.map((site)=>({\n                        value: site.id,\n                        label: site.name\n                    }));\n                setSiteData(formattedSites);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error fetching Sites\", error);\n            }\n        };\n        const fetchProducts = async ()=>{\n            try {\n                const productsJson = await getAllProducts();\n                const filterOutHiddenProducts = productsJson.filter((product)=>!product.IsProductHidden);\n                const formattedProducts = filterOutHiddenProducts === null || filterOutHiddenProducts === void 0 ? void 0 : filterOutHiddenProducts.map((product)=>({\n                        value: product.ProductId,\n                        label: product.ProductName,\n                        size_required: product.IsSizeRequired || product.size_required,\n                        sizes: product.AvailableSizes ? product.AvailableSizes.split(\", \").map((size)=>({\n                                value: size.toLowerCase().replace(/\\s+/g, \"\"),\n                                label: size\n                            })) : [],\n                        productCode: product.ProductCode,\n                        packageQuantity: product.PackageQuantity,\n                        productType: product.ProductType,\n                        name_printable: product.name_printable\n                    }));\n                setProductsData(formattedProducts);\n            } catch (error) {\n                console.error(\"Error fetching products:\", error);\n            }\n        };\n        fetchProducts();\n        fetchSites();\n        setLoading(false);\n    }, []);\n    const updateSelectedRowStatusForSingleItem = (updatedStatusID)=>{\n        if (updatedStatusID === 3) {\n            if (cancelledReasonapi) {\n                setIsValidCancelReason(true);\n            } else {\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        console.log(\"sssssssssssssssssssssssssssssssssssss\", selectedRowData);\n        const apiPayload = {\n            request_no: selectedRowData.id,\n            item_number: selectedRowData.item_number,\n            ProductName: selectedRowData.product_name,\n            Size: selectedRowData.size,\n            Quantity: selectedRowData.quantity,\n            NameForPrinting: selectedRowData.NameForPrinting,\n            Comments: updatedStatusID === 3 ? cancelledReasonapi : selectedRowData.comment,\n            RequestItemID: selectedRowData.request_item_id,\n            commentOnUpdatingRequest: cancelledReasonapi,\n            action_id: updatedStatusID,\n            SubmitterUserID: userData.user_id,\n            SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n            orignatorEmail: selectedRowData === null || selectedRowData === void 0 ? void 0 : selectedRowData.orignatorEmail,\n            cancelledReasonapi: cancelledReasonapi\n        };\n        try {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-product-request-items/\").concat(selectedRowData.id), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n                getData().then((data)=>{\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            item_number: row.item_number,\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: (row.action_id === 3 ? \"Rejected Reason: \".concat(row.latest_comment) : row.comments) || \"\",\n                            size: row.size,\n                            quantity: row.quantity,\n                            NameForPrinting: row.name_for_printing,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    setRequestRowData(formattedData);\n                    setCancelledReasonapi(\"\");\n                // closeModal();\n                // setShowStatusDialog(false);\n                });\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    //#region Validate\n    // Update the validate function to use savedItems instead of items\n    const validate = ()=>{\n        const newErrors = {};\n        if (!site) newErrors.site = \"Site is required.\";\n        if (!requiredDate) newErrors.requiredDate = \"Required date is missing.\";\n        if (!savedItems || savedItems.length === 0) {\n            newErrors.savedItems = \"At least one item must be added.\";\n        }\n        // Update state for field-level messages\n        setErrors(newErrors);\n        const hasErrors = Object.keys(newErrors).length > 0;\n        if (hasErrors) {\n            const errorMessages = Object.values(newErrors);\n            // setPopupConfig({\n            //   title: \"Validation Error\",\n            //   message: errorMessages.join(\"\\n\"),\n            //   type: \"error\",\n            // });\n            // setShowPopup(true); // trigger popup\n            const errorsLength = Object.keys(newErrors).length;\n            if (errorsLength > 1) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill all required fields.\");\n            } else if (errorsLength == 1 && newErrors.savedItems) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please add at least one item.\");\n            }\n            return false;\n        }\n        return true;\n    };\n    const handleSiteAdd = (value)=>{\n        setSite(value);\n        // Clear the error for site if value is valid\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (value) {\n                delete updated.site;\n            }\n            return updated;\n        });\n    };\n    // Add date validation function\n    const validateRequiredDate = (selectedDate)=>{\n        // setRequiredDate(selectedDate);\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (selectedDate) {\n                delete updated.requiredDate;\n            }\n            return updated;\n        });\n        if (!selectedDate) {\n            setDateWarning(\"\");\n            return;\n        }\n        const today = new Date();\n        const selected = new Date(selectedDate);\n        const twoWeeksFromNow = new Date();\n        twoWeeksFromNow.setDate(today.getDate() + 14);\n        if (selected < twoWeeksFromNow) {\n            setDateWarning(\"Notice: Less than 2 weeks lead time may affect availability.\");\n        } else {\n            setDateWarning(\"\");\n        }\n    };\n    // #region handlers\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    const handleCancelReason = (data)=>{\n        if (data) {\n            setIsValidCancelReason(true);\n        } else {\n            setIsValidCancelReason(false);\n        }\n    };\n    const handleSaveClick = (e)=>{\n        // e.preventDefault();\n        if (hasUnsavedChanges) {\n            setPopupType(\"unsavedWarning\");\n            setIsOpen(true); // your existing modal state\n        } else {\n            saveCurrentItem(e);\n        }\n    };\n    const saveCurrentItem = (e)=>{\n        var _currentItem_product, _currentItem_product_sizes, _currentItem_product1;\n        console.log(\"save order request\");\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (e) {\n                delete updated.savedItems;\n            }\n            return updated;\n        });\n        if (!e) {\n            console.log(\"returning here\");\n            setDateWarning(\"\");\n            return;\n        }\n        e.preventDefault();\n        let count = 0;\n        if (!currentItem.product) {\n            console.log(\"++ product\");\n            count++;\n        // setProductValid(\"Please select a product\");\n        } else {\n        // setProductValid(\"\");\n        }\n        if (!currentItem.quantity || currentItem.quantity < 1) {\n            console.log(\"++ quantity\");\n            count++;\n        // setQuantityValid(\"Please enter a valid quantity\");\n        } else {\n            if (currentItem.quantity > 1 && currentItem.nameForPrinting) {\n                // currentItem.nameForPrinting=\"\"\n                setCurrentItem((prev)=>({\n                        ...prev,\n                        nameForPrinting: \"\"\n                    }));\n            }\n        // setQuantityValid(\"\");\n        }\n        if (((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) && ((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) && !currentItem.size) {\n            console.log(\"++ product size\");\n            count++;\n        // Add size validation if needed\n        }\n        if (count > 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill in all mandatory fields to add items with valid quantity\");\n            return;\n        }\n        //     if (editingRowId) {\n        //         // Update existing item\n        //   const updatedItems = savedItems.map(item =>\n        //     item.id === editingRowId ? { ...currentItem, id: editingRowId } : item\n        //   );\n        //   setSavedItems(updatedItems);\n        //   setEditingRowId(null);\n        // } else {\n        //   // Add new item\n        //   setSavedItems([...savedItems, { ...currentItem, id: Date.now() }]);\n        // }\n        if (editingRowId) {\n            // Add the edited item back to savedItems\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: editingRowId,\n                    product: currentItem.product ? {\n                        ...currentItem.product\n                    } : null,\n                    size: currentItem.size ? {\n                        ...currentItem.size\n                    } : null\n                }\n            ]);\n            setEditingRowId(null);\n        } else {\n            // Add new item\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: \"row-\".concat(Date.now())\n                }\n            ]);\n        }\n        // Reset current item\n        setCurrentItem({\n            ...emptyItem\n        });\n        setEditingRowId(null);\n        setHasUnsavedChanges(false);\n    };\n    // Handle current item changes\n    const handleCurrentItemChange = (field, value)=>{\n        if (field === \"product\") {\n            // console.log(\"field-------------\",value)\n            // Reset other fields when product changes\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    size: null,\n                    quantity: \"\",\n                    nameForPrinting: \"\",\n                    nameForPrintingFlag: \"\",\n                    comments: \"\"\n                }));\n        } else if (field === \"quantity\" && value > 1) {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    nameForPrinting: \"\"\n                }));\n        } else {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value\n                }));\n        }\n    };\n    //#region Save\n    const handleSubmit = (status_ID)=>{\n        if (status_ID != 1 && !validate()) {\n            return;\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssssssssssssssssssss\",savedItems);\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        // Transform savedItems to API format\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size;\n            return {\n                requestItemId: item.requestItemId,\n                // RequestItemID: typeof item.id === 'string' && item.id.startsWith('row-') ? null : item.id,\n                ProductID: (_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value,\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            Status_id: status_ID,\n            SubmitterUserID: userData === null || userData === void 0 ? void 0 : userData.user_id,\n            TargetSiteID: (site === null || site === void 0 ? void 0 : site.value) || 3,\n            RequiredDate: requiredDate,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            items: items,\n            orignatorEmail: emailOfOriginator\n        };\n        // console.log(\"items--------------------------------------------------------\",items)\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/create-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    //#region updated conditional\n    const updateSelectedRowStatus = (updatedStatusID, existingStatusId)=>{\n        const STATUS_MAP = {\n            1: \"Pending Review\",\n            2: \"Approved\",\n            3: \"Rejected\",\n            4: \"Ordered\",\n            5: \"Draft\"\n        };\n        const toBeUpdated = savedItems.filter((item)=>item.status == STATUS_MAP[existingStatusId]);\n        if (toBeUpdated.length === 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"No items found with the specified status\");\n            return;\n        }\n        setLoading(true);\n        if (updatedStatusID == 3) {\n            if (cancelledReasonapi) {\n                setIsValidCancelReason(true);\n                console.log(\"triggerrerererered\", cancelledReasonapi);\n            } else {\n                console.log(\"triggerrerererered\");\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssss\", toBeUpdated);\n        const updatePromises = toBeUpdated.map(async (item)=>{\n            const apiPayload = {\n                request_no: item.id,\n                item_number: item.item_number,\n                ProductName: item.product.label,\n                Size: item.size.label,\n                Quantity: item.quantity,\n                NameForPrinting: item.nameForPrinting,\n                Comments: updatedStatusID == 3 ? cancelledReasonapi : item.comments,\n                RequestItemID: item.id,\n                commentOnUpdatingRequest: cancelledReasonapi,\n                action_id: updatedStatusID,\n                SubmitterUserID: userData.user_id,\n                SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n                orignatorEmail: emailOfOriginator,\n                cancelledReasonapi: cancelledReasonapi\n            };\n            console.log(\"apiPayload\", apiPayload); // Log for debugging purposes.\n            const res = await fetch(\"\".concat(_services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress, \"ppe-consumables/update-product-request-items/\").concat(item.id), {\n                method: \"POST\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            });\n            if (res.status === 200) {\n                return res.json();\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    router.push(redirectUrl);\n                }, 3000);\n                throw new Error(\"Unauthorized\");\n            } else {\n                const errorText = await res.text();\n                console.error(\"Update failed:\", errorText);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to update item status.\");\n                throw new Error(\"Update failed\");\n            }\n        });\n        Promise.all(updatePromises).then((results)=>{\n            const successCount = results.filter((result)=>result === null || result === void 0 ? void 0 : result.msg).length;\n            if (successCount > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Successfully updated \".concat(successCount, \" item(s)\"), {\n                    position: \"top-right\"\n                });\n                setTimeout(()=>{\n                    router.replace(\"/ppe-consumable\");\n                }, 2000);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"No items were successfully updated\");\n                router.push(\"/ppe-consumable\");\n            }\n        }).catch((error)=>{\n            console.error(\"Error updating items:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Some items failed to update\");\n        }).finally(()=>{\n            setLoading(false);\n        });\n    };\n    //#endregion updated conditional\n    // #region update\n    const handleUpdate = (action_id)=>{\n        if (!validate()) {\n            return;\n        }\n        // handleSaveClick();\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        console.log(\"savedItems\", savedItems);\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size, _item_size1;\n            return {\n                // RequestItemID: item.id,\n                RequestItemID: typeof item.id === \"string\" && item.id.startsWith(\"row-\") ? null : item.id,\n                ProductID: item.product_id || ((_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value),\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                // SizeID: item.size?.value || null, //TODO no size handel\n                SizeID: ((_item_size1 = item.size) === null || _item_size1 === void 0 ? void 0 : _item_size1.value) && !isNaN(Number(item.size.value)) ? Number(item.size.value) : null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            requestId: ppeId,\n            action_id: action_id,\n            submitterUserId: userData.user_id,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            targetSiteId: site.value,\n            requiredDate: requiredDate,\n            items: items,\n            comment: cancelledReasonapi,\n            orignatorEmail: emailOfOriginator\n        };\n        console.log(\"apiPayload--------------------------------------------------------\", apiPayload);\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n        setLoading(false);\n        setTimeout(()=>{\n            setLoading(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Request submitted!\");\n            router.push(\"/ppe-consumable\");\n        }, 1000);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"update\") {\n            setIsEdit(true);\n        }\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n    }, [\n        0\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"flex flex-col justify-start max-w-full  mx-auto mr-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row gap-8 bg-white p-6 rounded-lg shadow-[0_0_1px_rgba(0,0,0,0.1)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1136,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: nameOfOriginator || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1137,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1135,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1145,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: emailOfOriginator || (userData === null || userData === void 0 ? void 0 : userData.email) || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1146,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1144,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Created Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1154,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: new Date().toLocaleDateString(\"en-CA\"),\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1155,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1153,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Site\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500 ml-[2px]\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1164,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1163,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            value: site,\n                                            onChange: (e)=>{\n                                                setSite();\n                                                handleSiteAdd(e);\n                                            },\n                                            options: siteData,\n                                            isDisabled: (sites === null || sites === void 0 ? void 0 : sites.length) === 1 || submitted,\n                                            styles: customSelectStyles,\n                                            isClearable: true,\n                                            className: \" w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1166,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        errors.site && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.site\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1178,\n                                            columnNumber: 31\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1162,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Required Date \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1182,\n                                                    columnNumber: 31\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1181,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            min: new Date().toISOString().split(\"T\")[0],\n                                            value: requiredDate,\n                                            onChange: (e)=>{\n                                                setRequiredDate(e.target.value);\n                                                validateRequiredDate(e.target.value);\n                                            },\n                                            className: \"block w-full px-4 border rounded-lg form-input\",\n                                            disabled: submitted\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1184,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        dateWarning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-orange-500 text-sm\",\n                                            children: dateWarning\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1196,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.requiredDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.requiredDate\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1199,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1180,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1134,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row justify-between items-end my-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"flex mb-1 font-semibold tracking-wider text-base pl-2\",\n                                    children: \"Items\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1219,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1218,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col mb-28px bg-white p-6 rounded-lg py-8 shadow-[0_0_2px_rgba(0,0,0,0.2)]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Product \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1228,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1227,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: currentItem.product,\n                                                        onChange: (val)=>handleCurrentItemChange(\"product\", val),\n                                                        options: productsData,\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1230,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1226,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Size\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 \".concat(!((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) || !((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) || submitted ? \"hidden\" : \"\"),\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1244,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1242,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: currentItem.size,\n                                                        onChange: (val)=>handleCurrentItemChange(\"size\", val),\n                                                        options: ((_currentItem_product2 = currentItem.product) === null || _currentItem_product2 === void 0 ? void 0 : _currentItem_product2.sizes) || [],\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: !((_currentItem_product3 = currentItem.product) === null || _currentItem_product3 === void 0 ? void 0 : _currentItem_product3.size_required) || !((_currentItem_product4 = currentItem.product) === null || _currentItem_product4 === void 0 ? void 0 : (_currentItem_product_sizes1 = _currentItem_product4.sizes) === null || _currentItem_product_sizes1 === void 0 ? void 0 : _currentItem_product_sizes1.length) || submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1256,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1241,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Quantity \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1273,\n                                                                columnNumber: 28\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1272,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        // min={0}\n                                                        max: 999,\n                                                        value: currentItem.quantity,\n                                                        onChange: (e)=>handleCurrentItemChange(\"quantity\", e.target.value > 999 ? 999 : parseInt(e.target.value)),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1275,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1271,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Name for Printing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1291,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        maxLength: \"50\" //TODO change in db also\n                                                        ,\n                                                        value: currentItem.nameForPrinting,\n                                                        onChange: (e)=>handleCurrentItemChange(\"nameForPrinting\", e.target.value),\n                                                        disabled: currentItem.quantity !== 1 || submitted || !((_currentItem_product5 = currentItem.product) === null || _currentItem_product5 === void 0 ? void 0 : _currentItem_product5.name_printable),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        placeholder: \"Only if quantity = 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1292,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1290,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Comments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1309,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        value: currentItem.comments,\n                                                        maxLength: \"500\",\n                                                        onChange: (e)=>handleCurrentItemChange(\"comments\", e.target.value),\n                                                        className: \"disabled:text-gray-400 disabled:bg-[#F6F3F3] block w-full h-8 px-4 border rounded-lg form-input resize-none\",\n                                                        rows: 1,\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1310,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1308,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    onClick: saveCurrentItem,\n                                                    // onClick={handleSaveClick}\n                                                    className: \"px-2 py-1 2xl:px-3.5 border border-skin-primary rounded-md text-skin-primary cursor-pointer \".concat(submitted ? \"hidden\" : \"\"),\n                                                    disabled: submitted,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faFloppyDisk\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1331,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1323,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1321,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1225,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ag-theme-alpine my-[24px]\",\n                                        style: {\n                                            height: 250,\n                                            width: \"100%\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__.AgGridReact, {\n                                            ref: gridRef,\n                                            columnDefs: columnDefs,\n                                            rowData: savedItems,\n                                            rowHeight: 35,\n                                            getRowClass: getRowClass,\n                                            onGridReady: onGridReady\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1342,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1338,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    popupType === \"unsavedWarning\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"One item is being edited. If you continue, changes will be lost.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1360,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: closeModal,\n                                                        className: \"border px-4 py-2 rounded\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1365,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            saveCurrentItem(); // discard editing row\n                                                            closeModal();\n                                                        },\n                                                        className: \"bg-blue-600 text-white px-4 py-2 rounded\",\n                                                        children: \"Save Anyway\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1371,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1364,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1359,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1224,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1217,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end gap-4 rounded-lg  p-4 mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    router.push(\"/ppe-consumable\");\n                                },\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6\",\n                                disabled: loading,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1387,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (pageType === \"edit\") {\n                                        console.log(\"hasUnsavedChanges\", hasUnsavedChanges);\n                                        if (hasUnsavedChanges) {\n                                            setPopupType(\"Save\");\n                                            setIsOpen(true);\n                                        } else {\n                                            handleUpdate(5);\n                                        }\n                                    } else {\n                                        handleSubmit(5);\n                                    }\n                                },\n                                disabled: loading || requestStatus === 1,\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6 \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1397,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (hasUnsavedChanges) {\n                                        setPopupType(\"submitWhileEditing\");\n                                        setIsOpen(true);\n                                    } else {\n                                        if (!validate()) return;\n                                        setPopupType(\"submit\");\n                                        setIsOpen(true);\n                                    }\n                                },\n                                className: \"border border-skin-primary bg-skin-primary text-white rounded-md px-6 font-medium \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                disabled: loading || requestStatus === 1,\n                                children: \"Submit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1419,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"reject\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-red-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Reject All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1438,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"approve\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Approve All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1457,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                // onClick={() => {\n                                //   updateSelectedRowStatus(4, 2);\n                                // }}\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"markOrdered\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(requestStatusList.includes(2) ? \"\" : \"hidden\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Mark All Approved Requests to Ordered\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1476,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1386,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1124,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition, {\n                appear: true,\n                show: isOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: (value)=>{\n                        if (popupType !== \"reject\" || isValidCancelReason) {\n                            closeModal();\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1524,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1515,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                            lineNumber: 1545,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1544,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Confirm \".concat(popupType == \"submit\" ? \"Submission\" : popupType == \"reject\" ? \"Rejection\" : popupType == \"approve\" ? \"Approval\" : popupType == \"markOrdered\" ? \"markOrdered\" : popupType == \"Save\" ? \"Save\" : popupType == \"submitWhileEditing\" ? \"Submission\" : \" \")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1543,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeModal,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1569,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1563,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1542,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                popupType == \"submit\" ? \"submit\" : popupType == \"rejectSingleItem\" ? \"reject\" : popupType == \"approveSingleItem\" ? \"approve\" : popupType == \"markSingleItemOrdered\" ? \"mark approved items as ordered\" : popupType == \"reject\" ? \"reject\" : popupType == \"approve\" ? \"approve\" : popupType == \"markOrdered\" ? \"mark approved items as ordered\" : popupType == \"Save\" ? \"save? there is a item being edited,\\n it will be lost if you Save \" : popupType == \"submitWhileEditing\" ? \"Submit? there is a item being edited,\\n it will be lost if you Submit \" : \" \",\n                                                                \" \",\n                                                                \"this request?\",\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1577,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        popupType == \"reject\" || popupType == \"rejectSingleItem\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                    className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                                    rows: \"8\",\n                                                                    value: cancelledReasonapi,\n                                                                    onChange: (e)=>{\n                                                                        setCancelledReasonapi(e.target.value);\n                                                                        if (e.target.value) {\n                                                                            setIsValidCancelReason(true);\n                                                                        }\n                                                                    },\n                                                                    onBlur: (e)=>{\n                                                                        const trimmedValue = trimInputText(e.target.value);\n                                                                        setCancelledReasonapi(trimmedValue);\n                                                                    },\n                                                                    placeholder: \"Provide reason for rejection...\",\n                                                                    maxLength: \"500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1603,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                (popupType == \"reject\" || popupType == \"rejectSingleItem\") && !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"Please Provide reason for cancellation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1623,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1601,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1576,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                closeModal(), setCancelledReasonapi(\"\");\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1632,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                if (popupType === \"reject\" && !(cancelledReasonapi === null || cancelledReasonapi === void 0 ? void 0 : cancelledReasonapi.trim())) {\n                                                                    setIsValidCancelReason(false);\n                                                                    return;\n                                                                }\n                                                                if (pageType == \"add\") {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleSubmit(1);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleSubmit(1);\n                                                                    }\n                                                                } else {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleUpdate(1);\n                                                                    } else if (popupType === \"reject\") {\n                                                                        updateSelectedRowStatus(3, 1);\n                                                                    } else if (popupType === \"approve\") {\n                                                                        updateSelectedRowStatus(2, 1);\n                                                                    } else if (popupType === \"rejectSingleItem\") {\n                                                                        updateSelectedRowStatusForSingleItem(3);\n                                                                    } else if (popupType === \"approveSingleItem\") {\n                                                                        updateSelectedRowStatusForSingleItem(2);\n                                                                    } else if (popupType === \"Save\") {\n                                                                        handleUpdate(5);\n                                                                    } else if (popupType === \"markOrdered\") {\n                                                                        updateSelectedRowStatus(4, 2);\n                                                                    } else if (popupType === \"markSingleItemOrdered\") {\n                                                                        updateSelectedRowStatusForSingleItem(4, 2);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleUpdate(1);\n                                                                    }\n                                                                }\n                                                                setCancelledReasonapi(\"\");\n                                                                closeModal();\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center h-full border border-skin-primary\",\n                                                            children: \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1642,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1631,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1540,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1538,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1529,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1528,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1527,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 1506,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1505,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(PpeConsumable, \"3uVmOuiN21oz/jDcvtM1KLDGaQI=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = PpeConsumable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PpeConsumable);\nvar _c;\n$RefreshReg$(_c, \"PpeConsumable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/PpeConsumable.js\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable/[ppeId]/edit",{

/***/ "./components/PpeConsumable.js":
/*!*************************************!*\
  !*** ./components/PpeConsumable.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fortawesome/free-regular-svg-icons */ \"./node_modules/@fortawesome/free-regular-svg-icons/index.mjs\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lodash/forEach */ \"./node_modules/lodash/forEach.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(lodash_forEach__WEBPACK_IMPORTED_MODULE_12__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    valueContainer: (provided)=>({\n            ...provided,\n            height: \"28px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: ()=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst emptyItem = {\n    product: null,\n    size: null,\n    quantity: \"\",\n    nameForPrinting: \"\",\n    comments: \"\"\n};\nconst PpeConsumable = (param)=>{\n    let { userData, pageType, sites, OrderRequestData, ppeId = null } = param;\n    var _currentItem_product, _currentItem_product_sizes, _currentItem_product1, _currentItem_product2, _currentItem_product3, _currentItem_product_sizes1, _currentItem_product4, _currentItem_product5;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [nameOfOriginator, setNameOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [emailOfOriginator, setEmailOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [siteData, setSiteData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            ...emptyItem\n        }\n    ]);\n    const [requiredDate, setRequiredDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [createdDate, setCreatedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [site, setSite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((sites === null || sites === void 0 ? void 0 : sites[0]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [productsData, setProductsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [currentItem, setCurrentItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        ...emptyItem\n    });\n    const [savedItems, setSavedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // console.log(\"dsksldibiasdbilsdbv\", siteData);\n    // Add validation states for current item\n    // const [productValid, setProductValid] = useState(\"\");\n    // const [quantityValid, setQuantityValid] = useState(\"\");\n    const [selectedRowData, setSelectedRowData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dateWarning, setDateWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // const [isEditMode, setIsEditMode] = useState();\n    const [requestStatus, setRequestStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const [requestStatusList, setRequestStatusList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [submitted, setSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popupType, setPopupType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [editingRowId, setEditingRowId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gridApi, setGridApi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const closeModal = (e)=>{\n        if (e) {\n            e.preventDefault();\n        }\n        setIsValidCancelReason(true);\n        setCancelledReasonapi(\"\");\n        setIsOpen(false);\n    };\n    //#region Load data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n        if (OrderRequestData || (OrderRequestData === null || OrderRequestData === void 0 ? void 0 : OrderRequestData.length) > 0) {\n            var _OrderRequestData_, _OrderRequestData_1, _OrderRequestData_2, _OrderRequestData_3;\n            // console.log(\"OrderRequestData\", OrderRequestData);\n            if ((_OrderRequestData_ = OrderRequestData[0]) === null || _OrderRequestData_ === void 0 ? void 0 : _OrderRequestData_.action_id) {\n                var _OrderRequestData_4, _OrderRequestData_5;\n                setRequestStatus((_OrderRequestData_4 = OrderRequestData[0]) === null || _OrderRequestData_4 === void 0 ? void 0 : _OrderRequestData_4.action_id);\n                if (((_OrderRequestData_5 = OrderRequestData[0]) === null || _OrderRequestData_5 === void 0 ? void 0 : _OrderRequestData_5.action_id) !== 5) {\n                    setSubmitted(true);\n                }\n            }\n            if ((_OrderRequestData_1 = OrderRequestData[0]) === null || _OrderRequestData_1 === void 0 ? void 0 : _OrderRequestData_1.site_id) {\n                var _OrderRequestData_6, _OrderRequestData_7;\n                setSite({\n                    label: (_OrderRequestData_6 = OrderRequestData[0]) === null || _OrderRequestData_6 === void 0 ? void 0 : _OrderRequestData_6.site_name,\n                    value: (_OrderRequestData_7 = OrderRequestData[0]) === null || _OrderRequestData_7 === void 0 ? void 0 : _OrderRequestData_7.site_id\n                });\n            }\n            if ((_OrderRequestData_2 = OrderRequestData[0]) === null || _OrderRequestData_2 === void 0 ? void 0 : _OrderRequestData_2.required_date) {\n                var _OrderRequestData_8, _OrderRequestData_9;\n                setRequiredDate(((_OrderRequestData_8 = OrderRequestData[0]) === null || _OrderRequestData_8 === void 0 ? void 0 : _OrderRequestData_8.required_date) ? new Date((_OrderRequestData_9 = OrderRequestData[0]) === null || _OrderRequestData_9 === void 0 ? void 0 : _OrderRequestData_9.required_date).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if ((_OrderRequestData_3 = OrderRequestData[0]) === null || _OrderRequestData_3 === void 0 ? void 0 : _OrderRequestData_3.created_at) {\n                var _OrderRequestData_10, _OrderRequestData_11;\n                setCreatedDate(((_OrderRequestData_10 = OrderRequestData[0]) === null || _OrderRequestData_10 === void 0 ? void 0 : _OrderRequestData_10.created_at) ? new Date((_OrderRequestData_11 = OrderRequestData[0]) === null || _OrderRequestData_11 === void 0 ? void 0 : _OrderRequestData_11.created_at).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if (OrderRequestData[0].user_name) {\n                // setNameOfOriginator();\n                setNameOfOriginator(OrderRequestData[0].user_name);\n            }\n            if (OrderRequestData[0].actioned_by_email) {\n                // setNameOfOriginator();\n                setEmailOfOriginator(OrderRequestData[0].actioned_by_email);\n            }\n            // setrowdata\n            if (OrderRequestData && Array.isArray(OrderRequestData)) {\n                console.log(\"load data\", OrderRequestData, requestStatus);\n                const STATUS_MAP = {\n                    1: \"Pending Review\",\n                    2: \"Approved\",\n                    3: \"Rejected\",\n                    4: \"Ordered\",\n                    5: \"Draft\"\n                };\n                const mappedItems = OrderRequestData.map((item)=>({\n                        id: item.order_request_items,\n                        product: {\n                            value: item.product_id,\n                            label: item.product_name\n                        },\n                        size: {\n                            value: item.size,\n                            label: item.size || \"N/A\"\n                        },\n                        quantity: item.quantity,\n                        nameForPrinting: item.name_for_printing,\n                        comments: (item.action_id === 3 ? \"Rejected Reason: \".concat(item.latest_comment) : item.comments) || \"\",\n                        updateFlag: 0,\n                        status: STATUS_MAP[item.action_id]\n                    }));\n                const uniqueValues = [\n                    ...new Set(OrderRequestData.map((item)=>item.action_id))\n                ];\n                setRequestStatusList(uniqueValues);\n                setSavedItems(mappedItems);\n                console.log(\"uniqueValues\", uniqueValues);\n            }\n        }\n    }, [\n        OrderRequestData\n    ]);\n    //#endregion Load data\n    //#region ag-grid\n    const onRowSelected = (event)=>{\n        if (event.node.isSelected()) {\n            setEditingRowId(event.data.id);\n            setCurrentItem(event.data);\n        }\n    };\n    const onGridReady = (params)=>{\n        setGridApi(params.api);\n    };\n    // inside your component\n    const getRowClass = (params)=>{\n        return params.data.id === editingRowId ? \"bg-blue-100\" : \"\";\n    };\n    const IconsRenderer = (props)=>{\n        const handleDelete = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n        };\n        const handleEdit = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            setHasUnsavedChanges(true);\n            setEditingRowId(selectedRow.id);\n            // setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n            const handleRowUpdate = (actionId)=>()=>{\n                    setSelectedRowData(selectedRow);\n                    setStatusAction(actionId);\n                    setShowStatusDialog(true);\n                };\n            // event.preventDefault();\n            // const selectedRow = props.data;\n            // setEditingRowId(selectedRow.id || props.node.id);\n            // // const updatedData = savedItems.filter((item) => item !== selectedRow);\n            // // setSavedItems(updatedData);\n            //   setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            // Populate the form with the selected row data\n            setCurrentItem({\n                id: selectedRow.id,\n                product: selectedRow.product,\n                size: selectedRow.size,\n                quantity: selectedRow.quantity,\n                nameForPrinting: selectedRow.nameForPrinting,\n                nameForPrintingFlag: selectedRow.nameForPrinting,\n                comments: selectedRow.comments,\n                updateFlag: 1\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-row gap-4 justify-center text-skin-primary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleEdit,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faPenToSquare\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDelete,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\", \" text-red-500\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faTrash\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 277,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    onClick: ()=>{\n                        setSelectedRowData(props.data);\n                        setPopupType(\"approveSingleItem\");\n                        setIsOpen(true);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faCircleCheck,\n                        className: \"\".concat(requestStatus !== 1 || !requestStatus ? \"hidden\" : \"\", \" text-green-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 279,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    onClick: ()=>{\n                        setSelectedRowData(props.data);\n                        setPopupType(\"rejectSingleItem\");\n                        setIsOpen(true);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmarkCircle,\n                        className: \"\".concat(requestStatus !== 1 || !requestStatus ? \"hidden\" : \"\", \" text-red-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    onClick: ()=>{\n                        setSelectedRowData(props.data);\n                        setPopupType(\"markSingleItemOrdered\");\n                        setIsOpen(true);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faSquareCheck,\n                        className: \"\".concat(requestStatus !== 2 || !requestStatus ? \"hidden\" : \"\", \" text-green-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Column definitions for the grid\n    const columnDefs = [\n        {\n            headerName: \"Product\",\n            field: \"product.label\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Size\",\n            field: \"size.label\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Quantity\",\n            field: \"quantity\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Name for Printing\",\n            field: \"nameForPrinting\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Comments\",\n            field: \"comments\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            hide: requestStatus === 5 || !requestStatus,\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: 1.25,\n            filterParams: {\n                values: [\n                    \"Pending Review\",\n                    \"Approved\",\n                    \"Rejected\",\n                    \"Ordered\",\n                    \"Draft\"\n                ]\n            }\n        },\n        {\n            headerName: \"Actions\",\n            cellRenderer: IconsRenderer,\n            headerClass: \"header-with-border\",\n            flex: 1\n        }\n    ];\n    //#endregion ag-grid\n    //#region api's\n    function getAllProducts() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-products\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to fetch products: \".concat(error.message));\n            throw error;\n        });\n    }\n    function getAllSites() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/sites\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        });\n    }\n    //#endregion api's\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const siteData = await getAllSites();\n                const formattedSites = siteData === null || siteData === void 0 ? void 0 : siteData.map((site)=>({\n                        value: site.id,\n                        label: site.name\n                    }));\n                setSiteData(formattedSites);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error fetching Sites\", error);\n            }\n        };\n        const fetchProducts = async ()=>{\n            try {\n                const productsJson = await getAllProducts();\n                const filterOutHiddenProducts = productsJson.filter((product)=>!product.IsProductHidden);\n                const formattedProducts = filterOutHiddenProducts === null || filterOutHiddenProducts === void 0 ? void 0 : filterOutHiddenProducts.map((product)=>({\n                        value: product.ProductId,\n                        label: product.ProductName,\n                        size_required: product.IsSizeRequired || product.size_required,\n                        sizes: product.AvailableSizes ? product.AvailableSizes.split(\", \").map((size)=>({\n                                value: size.toLowerCase().replace(/\\s+/g, \"\"),\n                                label: size\n                            })) : [],\n                        productCode: product.ProductCode,\n                        packageQuantity: product.PackageQuantity,\n                        productType: product.ProductType,\n                        name_printable: product.name_printable\n                    }));\n                setProductsData(formattedProducts);\n            } catch (error) {\n                console.error(\"Error fetching products:\", error);\n            }\n        };\n        fetchProducts();\n        fetchSites();\n        setLoading(false);\n    }, []);\n    const updateSelectedRowStatusForSingleItem = (updatedStatusID)=>{\n        if (updatedStatusID === 3) {\n            if (cancelledReasonapi) {\n                setIsValidCancelReason(true);\n            } else {\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        console.log(\"sssssssssssssssssssssssssssssssssssss\", selectedRowData);\n        const apiPayload = {\n            request_no: selectedRowData.id,\n            item_number: selectedRowData.item_number,\n            ProductName: selectedRowData.product_name,\n            Size: selectedRowData.size,\n            Quantity: selectedRowData.quantity,\n            NameForPrinting: selectedRowData.NameForPrinting,\n            Comments: updatedStatusID === 3 ? cancelledReasonapi : selectedRowData.comment,\n            RequestItemID: selectedRowData.request_item_id,\n            commentOnUpdatingRequest: cancelledReasonapi,\n            action_id: updatedStatusID,\n            SubmitterUserID: userData.user_id,\n            SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n            orignatorEmail: selectedRowData === null || selectedRowData === void 0 ? void 0 : selectedRowData.orignatorEmail,\n            cancelledReasonapi: cancelledReasonapi\n        };\n        try {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-product-request-items/\").concat(selectedRowData.id), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            // getData().then((data) => {\n            //   const grouped = data?.reduce((acc, row) => {\n            //     if (!acc[row.request_id]) {\n            //       acc[row.request_id] = [];\n            //     }\n            //     acc[row.request_id].push(row);\n            //     return acc;\n            //   }, {});\n            //   const STATUS_MAP = {\n            //     1: \"Pending Review\",\n            //     2: \"Approved\",\n            //     3: \"Rejected\",\n            //     4: \"Ordered\",\n            //     5: \"Draft\",\n            //   };\n            //   const counters = {};\n            //   const formattedData = data?.map((row) => {\n            //     const siblings = grouped[row.request_id];\n            //     const index = siblings.findIndex(r => r.request_item_id === row.request_item_id) + 1;\n            //     counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n            //     return {\n            //       id: row.id,\n            //       request_id: `${row.request_id} - ${row.item_number}`, // stable per request_id\n            //       item_number: row.item_number, // stable per request_id\n            //       required_date: new Date(row.required_date)\n            //         .toISOString()\n            //         .split(\"T\")[0],\n            //       site_name: row.site_name,\n            //       product_name: row.product_name || \"Unnamed Product\",\n            //       requestor: row.user_name,\n            //       orignatorEmail: row.actioned_by_email,\n            //       comment: (row.action_id === 3 ? `Rejected Reason: ${row.latest_comment}` : row.comments) || \"\",\n            //       size: row.size,\n            //       quantity: row.quantity,\n            //       NameForPrinting: row.name_for_printing,\n            //       status: STATUS_MAP[row.action_id],\n            //       request_item_id: row.request_item_id,\n            //     };\n            //   });\n            //   setRequestRowData(formattedData);\n            //   setCancelledReasonapi(\"\");\n            //   // closeModal();\n            //   // setShowStatusDialog(false);\n            // });\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    //#region Validate\n    // Update the validate function to use savedItems instead of items\n    const validate = ()=>{\n        const newErrors = {};\n        if (!site) newErrors.site = \"Site is required.\";\n        if (!requiredDate) newErrors.requiredDate = \"Required date is missing.\";\n        if (!savedItems || savedItems.length === 0) {\n            newErrors.savedItems = \"At least one item must be added.\";\n        }\n        // Update state for field-level messages\n        setErrors(newErrors);\n        const hasErrors = Object.keys(newErrors).length > 0;\n        if (hasErrors) {\n            const errorMessages = Object.values(newErrors);\n            // setPopupConfig({\n            //   title: \"Validation Error\",\n            //   message: errorMessages.join(\"\\n\"),\n            //   type: \"error\",\n            // });\n            // setShowPopup(true); // trigger popup\n            const errorsLength = Object.keys(newErrors).length;\n            if (errorsLength > 1) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill all required fields.\");\n            } else if (errorsLength == 1 && newErrors.savedItems) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please add at least one item.\");\n            }\n            return false;\n        }\n        return true;\n    };\n    const handleSiteAdd = (value)=>{\n        setSite(value);\n        // Clear the error for site if value is valid\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (value) {\n                delete updated.site;\n            }\n            return updated;\n        });\n    };\n    // Add date validation function\n    const validateRequiredDate = (selectedDate)=>{\n        // setRequiredDate(selectedDate);\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (selectedDate) {\n                delete updated.requiredDate;\n            }\n            return updated;\n        });\n        if (!selectedDate) {\n            setDateWarning(\"\");\n            return;\n        }\n        const today = new Date();\n        const selected = new Date(selectedDate);\n        const twoWeeksFromNow = new Date();\n        twoWeeksFromNow.setDate(today.getDate() + 14);\n        if (selected < twoWeeksFromNow) {\n            setDateWarning(\"Notice: Less than 2 weeks lead time may affect availability.\");\n        } else {\n            setDateWarning(\"\");\n        }\n    };\n    // #region handlers\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    const handleCancelReason = (data)=>{\n        if (data) {\n            setIsValidCancelReason(true);\n        } else {\n            setIsValidCancelReason(false);\n        }\n    };\n    const handleSaveClick = (e)=>{\n        // e.preventDefault();\n        if (hasUnsavedChanges) {\n            setPopupType(\"unsavedWarning\");\n            setIsOpen(true); // your existing modal state\n        } else {\n            saveCurrentItem(e);\n        }\n    };\n    const saveCurrentItem = (e)=>{\n        var _currentItem_product, _currentItem_product_sizes, _currentItem_product1;\n        console.log(\"save order request\");\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (e) {\n                delete updated.savedItems;\n            }\n            return updated;\n        });\n        if (!e) {\n            console.log(\"returning here\");\n            setDateWarning(\"\");\n            return;\n        }\n        e.preventDefault();\n        let count = 0;\n        if (!currentItem.product) {\n            console.log(\"++ product\");\n            count++;\n        // setProductValid(\"Please select a product\");\n        } else {\n        // setProductValid(\"\");\n        }\n        if (!currentItem.quantity || currentItem.quantity < 1) {\n            console.log(\"++ quantity\");\n            count++;\n        // setQuantityValid(\"Please enter a valid quantity\");\n        } else {\n            if (currentItem.quantity > 1 && currentItem.nameForPrinting) {\n                // currentItem.nameForPrinting=\"\"\n                setCurrentItem((prev)=>({\n                        ...prev,\n                        nameForPrinting: \"\"\n                    }));\n            }\n        // setQuantityValid(\"\");\n        }\n        if (((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) && ((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) && !currentItem.size) {\n            console.log(\"++ product size\");\n            count++;\n        // Add size validation if needed\n        }\n        if (count > 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill in all mandatory fields to add items with valid quantity\");\n            return;\n        }\n        //     if (editingRowId) {\n        //         // Update existing item\n        //   const updatedItems = savedItems.map(item =>\n        //     item.id === editingRowId ? { ...currentItem, id: editingRowId } : item\n        //   );\n        //   setSavedItems(updatedItems);\n        //   setEditingRowId(null);\n        // } else {\n        //   // Add new item\n        //   setSavedItems([...savedItems, { ...currentItem, id: Date.now() }]);\n        // }\n        if (editingRowId) {\n            // Add the edited item back to savedItems\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: editingRowId,\n                    product: currentItem.product ? {\n                        ...currentItem.product\n                    } : null,\n                    size: currentItem.size ? {\n                        ...currentItem.size\n                    } : null\n                }\n            ]);\n            setEditingRowId(null);\n        } else {\n            // Add new item\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: \"row-\".concat(Date.now())\n                }\n            ]);\n        }\n        // Reset current item\n        setCurrentItem({\n            ...emptyItem\n        });\n        setEditingRowId(null);\n        setHasUnsavedChanges(false);\n    };\n    // Handle current item changes\n    const handleCurrentItemChange = (field, value)=>{\n        if (field === \"product\") {\n            // console.log(\"field-------------\",value)\n            // Reset other fields when product changes\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    size: null,\n                    quantity: \"\",\n                    nameForPrinting: \"\",\n                    nameForPrintingFlag: \"\",\n                    comments: \"\"\n                }));\n        } else if (field === \"quantity\" && value > 1) {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    nameForPrinting: \"\"\n                }));\n        } else {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value\n                }));\n        }\n    };\n    //#region Save\n    const handleSubmit = (status_ID)=>{\n        if (status_ID != 1 && !validate()) {\n            return;\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssssssssssssssssssss\",savedItems);\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        // Transform savedItems to API format\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size;\n            return {\n                requestItemId: item.requestItemId,\n                // RequestItemID: typeof item.id === 'string' && item.id.startsWith('row-') ? null : item.id,\n                ProductID: (_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value,\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            Status_id: status_ID,\n            SubmitterUserID: userData === null || userData === void 0 ? void 0 : userData.user_id,\n            TargetSiteID: (site === null || site === void 0 ? void 0 : site.value) || 3,\n            RequiredDate: requiredDate,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            items: items,\n            orignatorEmail: emailOfOriginator\n        };\n        // console.log(\"items--------------------------------------------------------\",items)\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/create-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    //#region updated conditional\n    const updateSelectedRowStatus = (updatedStatusID, existingStatusId)=>{\n        const STATUS_MAP = {\n            1: \"Pending Review\",\n            2: \"Approved\",\n            3: \"Rejected\",\n            4: \"Ordered\",\n            5: \"Draft\"\n        };\n        const toBeUpdated = savedItems.filter((item)=>item.status == STATUS_MAP[existingStatusId]);\n        if (toBeUpdated.length === 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"No items found with the specified status\");\n            return;\n        }\n        setLoading(true);\n        if (updatedStatusID == 3) {\n            if (cancelledReasonapi) {\n                setIsValidCancelReason(true);\n                console.log(\"triggerrerererered\", cancelledReasonapi);\n            } else {\n                console.log(\"triggerrerererered\");\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssss\", toBeUpdated);\n        const updatePromises = toBeUpdated.map(async (item)=>{\n            const apiPayload = {\n                request_no: item.id,\n                item_number: item.item_number,\n                ProductName: item.product.label,\n                Size: item.size.label,\n                Quantity: item.quantity,\n                NameForPrinting: item.nameForPrinting,\n                Comments: updatedStatusID == 3 ? cancelledReasonapi : item.comments,\n                RequestItemID: item.id,\n                commentOnUpdatingRequest: cancelledReasonapi,\n                action_id: updatedStatusID,\n                SubmitterUserID: userData.user_id,\n                SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n                orignatorEmail: emailOfOriginator,\n                cancelledReasonapi: cancelledReasonapi\n            };\n            console.log(\"apiPayload\", apiPayload); // Log for debugging purposes.\n            const res = await fetch(\"\".concat(_services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress, \"ppe-consumables/update-product-request-items/\").concat(item.id), {\n                method: \"POST\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            });\n            if (res.status === 200) {\n                return res.json();\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    router.push(redirectUrl);\n                }, 3000);\n                throw new Error(\"Unauthorized\");\n            } else {\n                const errorText = await res.text();\n                console.error(\"Update failed:\", errorText);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to update item status.\");\n                throw new Error(\"Update failed\");\n            }\n        });\n        Promise.all(updatePromises).then((results)=>{\n            const successCount = results.filter((result)=>result === null || result === void 0 ? void 0 : result.msg).length;\n            if (successCount > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Successfully updated \".concat(successCount, \" item(s)\"), {\n                    position: \"top-right\"\n                });\n                setTimeout(()=>{\n                    router.replace(\"/ppe-consumable\");\n                }, 2000);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"No items were successfully updated\");\n                router.push(\"/ppe-consumable\");\n            }\n        }).catch((error)=>{\n            console.error(\"Error updating items:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Some items failed to update\");\n        }).finally(()=>{\n            setLoading(false);\n        });\n    };\n    //#endregion updated conditional\n    // #region update\n    const handleUpdate = (action_id)=>{\n        if (!validate()) {\n            return;\n        }\n        // handleSaveClick();\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        console.log(\"savedItems\", savedItems);\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size, _item_size1;\n            return {\n                // RequestItemID: item.id,\n                RequestItemID: typeof item.id === \"string\" && item.id.startsWith(\"row-\") ? null : item.id,\n                ProductID: item.product_id || ((_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value),\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                // SizeID: item.size?.value || null, //TODO no size handel\n                SizeID: ((_item_size1 = item.size) === null || _item_size1 === void 0 ? void 0 : _item_size1.value) && !isNaN(Number(item.size.value)) ? Number(item.size.value) : null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            requestId: ppeId,\n            action_id: action_id,\n            submitterUserId: userData.user_id,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            targetSiteId: site.value,\n            requiredDate: requiredDate,\n            items: items,\n            comment: cancelledReasonapi,\n            orignatorEmail: emailOfOriginator\n        };\n        console.log(\"apiPayload--------------------------------------------------------\", apiPayload);\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n        setLoading(false);\n        setTimeout(()=>{\n            setLoading(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Request submitted!\");\n            router.push(\"/ppe-consumable\");\n        }, 1000);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"update\") {\n            setIsEdit(true);\n        }\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n    }, [\n        0\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"flex flex-col justify-start max-w-full  mx-auto mr-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row gap-8 bg-white p-6 rounded-lg shadow-[0_0_1px_rgba(0,0,0,0.1)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1136,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: nameOfOriginator || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1137,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1135,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1145,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: emailOfOriginator || (userData === null || userData === void 0 ? void 0 : userData.email) || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1146,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1144,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Created Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1154,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: new Date().toLocaleDateString(\"en-CA\"),\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1155,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1153,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Site\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500 ml-[2px]\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1164,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1163,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            value: site,\n                                            onChange: (e)=>{\n                                                setSite();\n                                                handleSiteAdd(e);\n                                            },\n                                            options: siteData,\n                                            isDisabled: (sites === null || sites === void 0 ? void 0 : sites.length) === 1 || submitted,\n                                            styles: customSelectStyles,\n                                            isClearable: true,\n                                            className: \" w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1166,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        errors.site && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.site\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1178,\n                                            columnNumber: 31\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1162,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Required Date \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1182,\n                                                    columnNumber: 31\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1181,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            min: new Date().toISOString().split(\"T\")[0],\n                                            value: requiredDate,\n                                            onChange: (e)=>{\n                                                setRequiredDate(e.target.value);\n                                                validateRequiredDate(e.target.value);\n                                            },\n                                            className: \"block w-full px-4 border rounded-lg form-input\",\n                                            disabled: submitted\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1184,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        dateWarning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-orange-500 text-sm\",\n                                            children: dateWarning\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1196,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.requiredDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.requiredDate\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1199,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1180,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1134,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row justify-between items-end my-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"flex mb-1 font-semibold tracking-wider text-base pl-2\",\n                                    children: \"Items\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1219,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1218,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col mb-28px bg-white p-6 rounded-lg py-8 shadow-[0_0_2px_rgba(0,0,0,0.2)]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Product \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1228,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1227,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: currentItem.product,\n                                                        onChange: (val)=>handleCurrentItemChange(\"product\", val),\n                                                        options: productsData,\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1230,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1226,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Size\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 \".concat(!((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) || !((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) || submitted ? \"hidden\" : \"\"),\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1244,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1242,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: currentItem.size,\n                                                        onChange: (val)=>handleCurrentItemChange(\"size\", val),\n                                                        options: ((_currentItem_product2 = currentItem.product) === null || _currentItem_product2 === void 0 ? void 0 : _currentItem_product2.sizes) || [],\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: !((_currentItem_product3 = currentItem.product) === null || _currentItem_product3 === void 0 ? void 0 : _currentItem_product3.size_required) || !((_currentItem_product4 = currentItem.product) === null || _currentItem_product4 === void 0 ? void 0 : (_currentItem_product_sizes1 = _currentItem_product4.sizes) === null || _currentItem_product_sizes1 === void 0 ? void 0 : _currentItem_product_sizes1.length) || submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1256,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1241,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Quantity \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1273,\n                                                                columnNumber: 28\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1272,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        // min={0}\n                                                        max: 999,\n                                                        value: currentItem.quantity,\n                                                        onChange: (e)=>handleCurrentItemChange(\"quantity\", e.target.value > 999 ? 999 : parseInt(e.target.value)),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1275,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1271,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Name for Printing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1291,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        maxLength: \"50\" //TODO change in db also\n                                                        ,\n                                                        value: currentItem.nameForPrinting,\n                                                        onChange: (e)=>handleCurrentItemChange(\"nameForPrinting\", e.target.value),\n                                                        disabled: currentItem.quantity !== 1 || submitted || !((_currentItem_product5 = currentItem.product) === null || _currentItem_product5 === void 0 ? void 0 : _currentItem_product5.name_printable),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        placeholder: \"Only if quantity = 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1292,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1290,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Comments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1309,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        value: currentItem.comments,\n                                                        maxLength: \"500\",\n                                                        onChange: (e)=>handleCurrentItemChange(\"comments\", e.target.value),\n                                                        className: \"disabled:text-gray-400 disabled:bg-[#F6F3F3] block w-full h-8 px-4 border rounded-lg form-input resize-none\",\n                                                        rows: 1,\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1310,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1308,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    onClick: saveCurrentItem,\n                                                    // onClick={handleSaveClick}\n                                                    className: \"px-2 py-1 2xl:px-3.5 border border-skin-primary rounded-md text-skin-primary cursor-pointer \".concat(submitted ? \"hidden\" : \"\"),\n                                                    disabled: submitted,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faFloppyDisk\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1331,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1323,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1321,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1225,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ag-theme-alpine my-[24px]\",\n                                        style: {\n                                            height: 250,\n                                            width: \"100%\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__.AgGridReact, {\n                                            ref: gridRef,\n                                            columnDefs: columnDefs,\n                                            rowData: savedItems,\n                                            rowHeight: 35,\n                                            getRowClass: getRowClass,\n                                            onGridReady: onGridReady\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1342,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1338,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    popupType === \"unsavedWarning\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"One item is being edited. If you continue, changes will be lost.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1360,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: closeModal,\n                                                        className: \"border px-4 py-2 rounded\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1365,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            saveCurrentItem(); // discard editing row\n                                                            closeModal();\n                                                        },\n                                                        className: \"bg-blue-600 text-white px-4 py-2 rounded\",\n                                                        children: \"Save Anyway\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1371,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1364,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1359,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1224,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1217,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end gap-4 rounded-lg  p-4 mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    router.push(\"/ppe-consumable\");\n                                },\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6\",\n                                disabled: loading,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1387,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (pageType === \"edit\") {\n                                        console.log(\"hasUnsavedChanges\", hasUnsavedChanges);\n                                        if (hasUnsavedChanges) {\n                                            setPopupType(\"Save\");\n                                            setIsOpen(true);\n                                        } else {\n                                            handleUpdate(5);\n                                        }\n                                    } else {\n                                        handleSubmit(5);\n                                    }\n                                },\n                                disabled: loading || requestStatus === 1,\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6 \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1397,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (hasUnsavedChanges) {\n                                        setPopupType(\"submitWhileEditing\");\n                                        setIsOpen(true);\n                                    } else {\n                                        if (!validate()) return;\n                                        setPopupType(\"submit\");\n                                        setIsOpen(true);\n                                    }\n                                },\n                                className: \"border border-skin-primary bg-skin-primary text-white rounded-md px-6 font-medium \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                disabled: loading || requestStatus === 1,\n                                children: \"Submit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1419,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"reject\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-red-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Reject All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1438,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"approve\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Approve All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1457,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                // onClick={() => {\n                                //   updateSelectedRowStatus(4, 2);\n                                // }}\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"markOrdered\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(requestStatusList.includes(2) ? \"\" : \"hidden\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Mark All Approved Requests to Ordered\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1476,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1386,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1124,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition, {\n                appear: true,\n                show: isOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: (value)=>{\n                        if (popupType !== \"reject\" || isValidCancelReason) {\n                            closeModal();\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1524,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1515,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                            lineNumber: 1545,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1544,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Confirm \".concat(popupType == \"submit\" ? \"Submission\" : popupType == \"reject\" ? \"Rejection\" : popupType == \"approve\" ? \"Approval\" : popupType == \"markOrdered\" ? \"markOrdered\" : popupType == \"Save\" ? \"Save\" : popupType == \"submitWhileEditing\" ? \"Submission\" : \" \")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1543,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeModal,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1569,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1563,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1542,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                popupType == \"submit\" ? \"submit\" : popupType == \"rejectSingleItem\" ? \"reject\" : popupType == \"approveSingleItem\" ? \"approve\" : popupType == \"markSingleItemOrdered\" ? \"mark approved items as ordered\" : popupType == \"reject\" ? \"reject\" : popupType == \"approve\" ? \"approve\" : popupType == \"markOrdered\" ? \"mark approved items as ordered\" : popupType == \"Save\" ? \"save? there is a item being edited,\\n it will be lost if you Save \" : popupType == \"submitWhileEditing\" ? \"Submit? there is a item being edited,\\n it will be lost if you Submit \" : \" \",\n                                                                \" \",\n                                                                \"this request?\",\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1577,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        popupType == \"reject\" || popupType == \"rejectSingleItem\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                    className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                                    rows: \"8\",\n                                                                    value: cancelledReasonapi,\n                                                                    onChange: (e)=>{\n                                                                        setCancelledReasonapi(e.target.value);\n                                                                        if (e.target.value) {\n                                                                            setIsValidCancelReason(true);\n                                                                        }\n                                                                    },\n                                                                    onBlur: (e)=>{\n                                                                        const trimmedValue = trimInputText(e.target.value);\n                                                                        setCancelledReasonapi(trimmedValue);\n                                                                    },\n                                                                    placeholder: \"Provide reason for rejection...\",\n                                                                    maxLength: \"500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1603,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                (popupType == \"reject\" || popupType == \"rejectSingleItem\") && !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"Please Provide reason for cancellation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1623,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1601,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1576,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                closeModal(), setCancelledReasonapi(\"\");\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1632,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                if (popupType === \"reject\" && !(cancelledReasonapi === null || cancelledReasonapi === void 0 ? void 0 : cancelledReasonapi.trim())) {\n                                                                    setIsValidCancelReason(false);\n                                                                    return;\n                                                                }\n                                                                if (pageType == \"add\") {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleSubmit(1);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleSubmit(1);\n                                                                    }\n                                                                } else {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleUpdate(1);\n                                                                    } else if (popupType === \"reject\") {\n                                                                        updateSelectedRowStatus(3, 1);\n                                                                    } else if (popupType === \"approve\") {\n                                                                        updateSelectedRowStatus(2, 1);\n                                                                    } else if (popupType === \"rejectSingleItem\") {\n                                                                        updateSelectedRowStatusForSingleItem(3);\n                                                                    } else if (popupType === \"approveSingleItem\") {\n                                                                        updateSelectedRowStatusForSingleItem(2);\n                                                                    } else if (popupType === \"Save\") {\n                                                                        handleUpdate(5);\n                                                                    } else if (popupType === \"markOrdered\") {\n                                                                        updateSelectedRowStatus(4, 2);\n                                                                    } else if (popupType === \"markSingleItemOrdered\") {\n                                                                        updateSelectedRowStatusForSingleItem(4, 2);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleUpdate(1);\n                                                                    }\n                                                                }\n                                                                setCancelledReasonapi(\"\");\n                                                                closeModal();\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center h-full border border-skin-primary\",\n                                                            children: \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1642,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1631,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1540,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1538,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1529,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1528,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1527,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 1506,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1505,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(PpeConsumable, \"3uVmOuiN21oz/jDcvtM1KLDGaQI=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = PpeConsumable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PpeConsumable);\nvar _c;\n$RefreshReg$(_c, \"PpeConsumable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/PpeConsumable.js\n"));

/***/ })

});
import Layout from "@/components/Layout";
import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles//ag-grid.css";
import "ag-grid-community/styles//ag-theme-alpine.css";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch } from "@fortawesome/free-solid-svg-icons";
import { useLoading } from "@/utils/loaders/loadingContext";
import { useRouter } from "next/router";
import { apiConfig } from "@/services/apiConfig";
import AddProductDialog from "@/components/AddProductDialog";
import productStatusRenderer from "@/utils/renderer/productStatusRenderer";
import ppeActionRenderer from "@/utils/renderer/ppeActionRenderer";
import {
  Button,
  Dialog,
  DialogBody,
  DialogContent,
  DialogSurface,
  DialogTitle,
  DialogTrigger,
  FluentProvider,
  webLightTheme,
} from "@fluentui/react-components";
import productActiveRenderer from "@/utils/renderer/ppeActiveRenderer";
import { logout } from "@/utils/secureStorage";

const PPEConsumables = ({ userData }) => {
  // console.log("PARETN component rendered", userData);
  const router = useRouter();
  // const [rowData, setRowData] = useState([]);
  const [requestRowData, setRequestRowData] = useState([]);
  const [pageSize, setPageSize] = useState(15);
  const gridRef = useRef();
  const { setIsLoading } = useLoading();
  const [searchInput, setSearchInput] = useState("");
  const [blockScreen, setBlockScreen] = useState(false);
  const [selectedView, setSelectedView] = useState(null);
  const [orderView, setOrderView] = useState(true);
  const [productTypes, setProductTypes] = useState([]);

  //#region pop up states
  const [showAddProduct, setShowAddProduct] = useState(false);
  const [isEditingProduct, setIsEditingProduct] = useState(false);
  const [editProductData, setEditProductData] = useState();
  const [isValidCancelReason, setIsValidCancelReason] = useState(true);

  // Controlled filter states
  const [siteFilter, setSiteFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [dateFilter, setDateFilter] = useState("");

  const [showStatusDialog, setShowStatusDialog] = useState(false);
  const [selectedRowData, setSelectedRowData] = useState(null);
  const [statusAction, setStatusAction] = useState(null);
  const [cancelledReasonapi, setCancelledReasonapi] = useState(null);

  // Load saved view from localStorage on component mount
  useEffect(() => {
    // Only run on client side
    if (typeof window !== "undefined") {
      const savedView = localStorage.getItem("ppeSelectedView");
      if (savedView && (savedView === "Orders" || savedView === "Products")) {
        setSelectedView(savedView);
        setOrderView(savedView === "Orders");
      } else {
        setSelectedView("Orders");
        setOrderView(true);
      }
    }
  }, []); // Empty dependency array - runs only on mount

  useEffect(() => {
    if (!selectedView) return;
    const fetchProducts = async () => {
      if (selectedView === "Orders") {
        getData().then((data) => {
          // console.log("data", data);
          const grouped = data?.reduce((acc, row) => {
              if (!acc[row.request_id]) {
                acc[row.request_id] = [];
              }
              acc[row.request_id].push(row);
              return acc;
            }, {});
          const STATUS_MAP = {
            1: "Pending Review",
            2: "Approved",
            3: "Rejected",
            4: "Ordered",
            5: "Draft",
          };
          const counters = {};
          const formattedData = data?.map((row) => {
            counters[row.request_id] = (counters[row.request_id] || 0) + 1;
            const siblings = grouped[row.request_id];
              const index = siblings.findIndex(r => r.request_item_id === row.request_item_id) + 1;

            return {
              id: row.id,
              request_id: `${row.request_id} - ${row.item_number}`, // stable per request_id
              item_number: row.item_number, // stable per request_id

              required_date: new Date(row.required_date)
                .toISOString()
                .split("T")[0],
              site_name: row.site_name,
              product_name: row.product_name || "Unnamed Product",
              requestor: row.user_name,
              orignatorEmail: row.actioned_by_email,
              comment: row.comments,
              NameForPrinting:row.name_for_printing,
              size: row.size,
              quantity: row.quantity,
              status: STATUS_MAP[row.action_id],
              request_item_id: row.request_item_id,
            };
          });
          // console.log("Formatted Data:", formattedData);
          // setRowData(formattedData);
          setRequestRowData(formattedData);
        });
      } else {
        const [productsJson, productTypesJson] = await Promise.all([
          getAllProducts(),
          getProductTypes(),
        ]);

        // console.log("Fetched Products JSON:", productsJson);
        // console.log("Fetched Product Types JSON:", productTypesJson);

        // Save product types for later use (e.g., filters or dialogs)
        if (Array.isArray(productTypesJson)) {
          setProductTypes(productTypesJson);
        } else {
          setProductTypes([]);
        }
        // console.log("Fetched Products JSON:", productsJson);
        if (productsJson && productsJson.length > 0) {
          // console.log(
          //   "Fetched Products JSON inside code execution",
          //   productsJson
          // );
          const formattedProductData = productsJson.map((product) => ({
            product_id: product.ProductId,
            product_name: product.ProductName || "Unnamed Product",
            category: product.ProductType || "Uncategorized",
            type_id: product.TypeId,
            stock: 0,
            sizes: product.AvailableSizes || "One Size",
            unit: deriveUnitFromPackageQuantity(product.PackageQuantity),
            name_printable: product.name_printable,
            IsProductHidden: product.IsProductHidden,
          }));
          // setRowData(formattedProductData);
          setRequestRowData(formattedProductData);
        } else {
          const fallbackProductData = [
            {
              product_name: "Nitrile Gloves",
              category: "Gloves",
              stock: 1200,
              unit: "Box",
              status: "Available",
            },
          ];
          // setRowData(fallbackProductData);
        }
      }
    };
    fetchProducts();
  }, [selectedView]);

  // #region get data
  const getData = async () => {
    // setRowData([]);
    setRequestRowData([]);
    let serverAddress = apiConfig.serverAddress;
    console.log("");
    return fetch(`${serverAddress}ppe-consumables/all-ppe-requests`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include", // Use session authentication
    })
      .then(async (res) => {
        if (res.status == 502) {
          setBlockScreen(true);
          return;
        }
        setBlockScreen(false);
        if (res.status === 200) {
          return res.json();
        }
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async () => {
            await logout();
            router.push("/login");
          }, 3000);
        }
        throw new Error("Failed to fetch data");
      })
      .catch((error) => {
        console.error(error);
      });
  };
  async function getAllProducts() {
    // setRowData([]);
    let serverAddress = apiConfig.serverAddress;
    return fetch(`${serverAddress}ppe-consumables/all-products`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include", // Use session authentication
    })
      .then(async (res) => {
        if (res.status == 502) {
          setBlockScreen(true);
          return;
        }
        setBlockScreen(false);
        if (res.status === 200) {
          return res.json();
        }
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async () => {
            await logout();
            router.push("/login");
          }, 3000);
        }
        throw new Error("Failed to fetch products");
      })
      .catch((error) => {
        console.error(error);
        toast.error(`Failed to fetch products: ${error.message}`);
        throw error;
      });
  }

  async function getProductTypes() {
    let serverAddress = apiConfig.serverAddress;
    return fetch(`${serverAddress}ppe-consumables/get-product_types`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
    })
      .then(async (res) => {
        if (res.status == 502) {
          setBlockScreen(true);
          return;
        }
        setBlockScreen(false);
        if (res.status === 200) {
          return res.json();
        }
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async () => {
            await logout();
            router.push("/login");
          }, 3000);
        }
        throw new Error("Failed to fetch product types");
      })
      .catch((error) => {
        console.error(error);
        toast.error(`Failed to fetch product types: ${error.message}`);
        throw error;
      });
  }

  //#region column def
  // Example column definitions for Products
  // Replace with proper filter handling
  const handleSiteFilterChange = (value) => {
    setSiteFilter(value);
    if (gridRef.current && gridRef.current.api) {
      const filterInstance = gridRef.current.api.getFilterInstance("site_name");
      if (filterInstance) {
        if (value) {
          filterInstance.setModel({
            type: "equals",
            filter: value,
          });
        } else {
          filterInstance.setModel(null);
        }
        gridRef.current.api.onFilterChanged();
      }
    }
  };

  const handleStatusFilterChange = (value) => {
    setStatusFilter(value);
    if (gridRef.current && gridRef.current.api) {
      const filterInstance = gridRef.current.api.getFilterInstance("status");
      if (filterInstance) {
        if (value) {
          filterInstance.setModel({
            type: "equals",
            filter: value,
          });
        } else {
          filterInstance.setModel(null);
        }
        gridRef.current.api.onFilterChanged();
      }
    }
  };

  const handleSelectedView = (selectedType) => {
    setSelectedView(selectedType);
    setOrderView(selectedType === "Orders");
    // Only save to localStorage on client side
    if (typeof window !== "undefined") {
      localStorage.setItem("ppeSelectedView", selectedType);
    }
  };

  const handleDateFilterChange = (value) => {
    setDateFilter(value);
    if (gridRef.current && gridRef.current.api) {
      const filterInstance =
        gridRef.current.api.getFilterInstance("required_date");
      if (filterInstance) {
        if (value) {
          const selectedDate = new Date(value);
          const yyyy = selectedDate.getFullYear();
          const mm = String(selectedDate.getMonth() + 1).padStart(2, "0");
          const dd = String(selectedDate.getDate()).padStart(2, "0");

          const formattedDate = `${yyyy}-${mm}-${dd}`;

          filterInstance.setModel({
            type: "equals",
            dateFrom: formattedDate,
          });
        } else {
          filterInstance.setModel(null);
        }
        gridRef.current.api.onFilterChanged();
      }
    }
  };

  // Example column definitions
  const requestColumnDefs = useMemo(
    () => [
      {
        headerName: "Request ID",
        field: "request_id",
        flex: 1,
        hide: !orderView,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Required Date",
        field: "required_date",
        flex: 1,
        hide: !orderView,
        filter: "agDateColumnFilter",
        filterParams: {
          comparator: (filterLocalDateAtMidnight, cellValue) => {
            const dateAsString = cellValue;
            if (!dateAsString) return 0;

            const cellDate = new Date(dateAsString);
            const cellDateAtMidnight = new Date(cellDate);
            cellDateAtMidnight.setHours(0, 0, 0, 0);

            if (cellDateAtMidnight < filterLocalDateAtMidnight) {
              return -1;
            } else if (cellDateAtMidnight > filterLocalDateAtMidnight) {
              return 1;
            } else {
              return 0;
            }
          },
        },
        valueFormatter: (params) => {
          return params.value
            ? new Date(params.value).toLocaleDateString()
            : "";
        },
      },
      {
        headerName: "Site",
        field: "site_name",
        flex: 1,
        hide: !orderView,
        filter: "agTextColumnFilter",
        filterParams: {
          values: ["ISS1-Teynham", "ISS2-Linton", "ISS3-Sittingbourne"],
        },
      },
      {
        headerName: "Product",
        field: "product_name",
        flex: 1,
        hide: !orderView,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Size",
        field: "size",
        flex: 0.75,
        hide: !orderView,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Qty",
        field: "quantity",
        flex: 0.75,
        hide: !orderView,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Requestor",
        field: "requestor",
        flex: 1,
        hide: !orderView,
        filter: "agTextColumnFilter",
      },
      { headerName: "Comment", field: "comment", flex: 1, hide: !orderView },
      {
        headerName: "Status",
        field: "status",
        hide: !orderView,
        cellRenderer: productStatusRenderer,
        cellStyle: () => ({ justifyContent: "center" }),
        flex: 1.25,
        filterParams: {
          values: [
            "Pending Review",
            "Approved",
            "Rejected",
            "Ordered",
            "Draft",
          ],
        },
      },

      //product view
      {
        headerName: "Product Name",
        field: "product_name",
        flex: 1,
        hide: orderView,
      },
      { headerName: "Category", field: "category", flex: 0.5, hide: orderView },
      { headerName: "Sizes", field: "sizes", flex: 0.5, hide: orderView },
      {
        headerName: "Name Printing",
        field: "name_printable",
        flex: 0.5,
        hide: orderView,
        cellRenderer: "agCheckboxCellRenderer",
        cellRendererParams: {
          disabled: true, // Makes it read-only
        },
        cellStyle: {
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        },
        headerClass: "ag-header-cell-centered",
      },
      {
        headerName: "Status",
        // field: "site_id",
        flex: 0.5,
        cellRenderer: (params) =>
          productActiveRenderer(
            params,
            setShowAddProduct,
            orderView,
            setIsEditingProduct,
            setEditProductData,
            //banana
            setShowStatusDialog,
            setSelectedRowData,
            setStatusAction
          ),
        sortable: false,
        hide: orderView,
        // },
      },
      {
        headerName: "Actions",
        field: "site_id",
        flex: 1,
        cellRenderer: (params) =>
          ppeActionRenderer(
            params,
            setShowAddProduct,
            orderView,
            setIsEditingProduct,
            setEditProductData,
            //banana
            setShowStatusDialog,
            setSelectedRowData,
            setStatusAction,
            userData
          ),
        sortable: false,
      },
    ],
    [orderView]
  );

  // console.log("order vbiew", orderView);
  const defaultColDef = useMemo(
    () => ({
      sortable: true,
      filter: true,
      filterParams: {
        debounceMs: 300, // Add debounce for better performance
      },
      resizable: true,
      flex: 1,
      suppressMenu: false,
    }),
    []
  );

  useEffect(() => {
    setIsLoading(false);
    // console.log(
    //   "Selected View:",
    //   selectedView,
    //   "Order View:",
    //   orderView,
    //   selectedView === "Orders"
    // );
  }, [selectedView]);

  const handleGridReady = (params) => {
    params.api.setColumnDefs(requestColumnDefs);
  };

  useEffect(() => {
    if (gridRef.current && gridRef.current.api) {
      gridRef.current.api.setColumnDefs(requestColumnDefs);
      // Refresh the grid to apply new column definitions
      gridRef.current.api.refreshHeader();
      gridRef.current.api.refreshCells();
    }
  }, [requestColumnDefs, orderView]); // Add orderView dependency

  const onFilterTextBoxChanged = useCallback(() => {
    gridRef.current.api.setQuickFilter(
      document.getElementById("filter-text-box").value
    );
    setSearchInput(document.getElementById("filter-text-box").value);
  }, []);

  const handlePageSizeChange = (event) => {
    const newPageSize = parseInt(event.target.value, 15);
    setPageSize(newPageSize);
    gridRef.current.api.paginationSetPageSize(newPageSize);
  };

  const handleClearFilters = () => {
    // Clear AG Grid filters
    if (gridRef.current && gridRef.current.api) {
      gridRef.current.api.setFilterModel(null);
      gridRef.current.api.onFilterChanged();
    }

    // Reset UI control states
    setSiteFilter("");
    setStatusFilter("");
    setDateFilter("");
  };
  const trimInputText = (input) => {
    return input.trim();
  };

  //#region api
  const updateSelectedRowStatus = (updatedStatusID, comment, params) => {
    if (updatedStatusID == 3){
      if(comment) {
        setIsValidCancelReason(true);
      } else {
        setIsValidCancelReason(false);
        return;
      }
    } 

    console.log("sssssssssssssssssssssssssssssssssssss", params);
    const apiPayload = {
      request_no: params.id,
      item_number: params.item_number,
      ProductName: params.product_name,
      Size: params.size,
      Quantity: params.quantity,
      NameForPrinting: params.NameForPrinting,
      Comments: params.comment,
      RequestItemID: params.request_item_id,
      commentOnUpdatingRequest: comment,
      action_id: updatedStatusID,
      SubmitterUserID: userData.user_id,
      SubmitterEmail: userData?.email,
      orignatorEmail: params?.orignatorEmail,
    };


    try {
      let serverAddress = apiConfig.serverAddress;
      fetch(
        `${serverAddress}ppe-consumables/update-product-request-items/${params.id}`,
        {
          method: "Post",
          headers: {
            "content-type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify(apiPayload),
        }
      )
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async () => {
              await logout();
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              router.push(redirectUrl);
            }, 3000);
            return null;
          } else {
            toast.error("Failed to save product.");
            setLoading(false);
          }
        })
        .then((json) => {
          if (json && json.msg) {
            toast.success(json.msg, {
              position: "top-right",
            });
            setTimeout(() => {
              router.replace("/ppe-consumable");
            }, 2000);
          }
          getData().then((data) => {
            const grouped = data?.reduce((acc, row) => {
              if (!acc[row.request_id]) {
                acc[row.request_id] = [];
              }
              acc[row.request_id].push(row);
              return acc;
            }, {});
            const STATUS_MAP = {
              1: "Pending Review",
              2: "Approved",
              3: "Rejected",
              4: "Ordered",
              5: "Draft",
            };
            const counters = {};
            const formattedData = data?.map((row) => {
              const siblings = grouped[row.request_id];
              const index = siblings.findIndex(r => r.request_item_id === row.request_item_id) + 1;

              counters[row.request_id] = (counters[row.request_id] || 0) + 1;
              return {
                id: row.id,
                request_id: `${row.request_id} - ${row.item_number}`, // stable per request_id
                item_number: row.item_number, // stable per request_id
                required_date: new Date(row.required_date)
                  .toISOString()
                  .split("T")[0],
                site_name: row.site_name,
                product_name: row.product_name || "Unnamed Product",
                requestor: row.user_name,
                orignatorEmail: row.actioned_by_email,
                comment: row.comments,
                size: row.size,
                quantity: row.quantity,
                NameForPrinting: row.name_for_printing,
                status: STATUS_MAP[row.action_id],
                request_item_id: row.request_item_id,
                
              };
            });
            setRequestRowData(formattedData);
            setCancelledReasonapi("");
            setShowStatusDialog(false);
          });
        });
    } catch (error) {
      console.log("errorin submitting the order", error);
    }
  };

  return (
    <>
      <ToastContainer limit={1} />
      <Layout userData={userData}>
        <div className="mr-20 md:mr-12 lg:mr-14">
          <div className="flex flex-row md:flex-col lg:flex-row justify-between">
            {(userData.role_id === 6 ||
              userData.role_id === 1 ||
              userData.role_id === 5) && (
              <div className="flex items-center gap-6">
                <div
                  className={`inline-flex rounded-full bg-gray-200 p-1 
                 ${
                   userData.role_id === 6 ||
                   userData.role_id === 1 ||
                   userData.role_id === 5
                     ? ""
                     : "hidden"
                 }
                `}
                >
                  <button
                    className={`px-4 py-1 rounded-full text-sm font-medium transition ${
                      selectedView === "Orders"
                        ? "bg-white shadow text-black"
                        : "text-gray-500 hover:text-black"
                    }`}
                    onClick={() => handleSelectedView("Orders")}
                  >
                    Requests
                  </button>
                  <button
                    className={`px-4 py-1 rounded-full text-sm font-medium transition ${
                      selectedView === "Products"
                        ? "bg-white shadow text-black"
                        : "text-gray-500 hover:text-black"
                    }`}
                    onClick={() => {
                      handleSelectedView("Products"), handleClearFilters();
                    }}
                  >
                    Products
                  </button>
                </div>
              </div>
            )}
            {orderView && (
              <div
                // style={{ marginBottom: "10px" }}
                className="flex gap-4 items-center"
              >
                <div>
                  <label className="mr-2">Site:</label>
                  <select
                    value={siteFilter}
                    onChange={(e) => handleSiteFilterChange(e.target.value)}
                    className="border p-1 rounded"
                  >
                    <option value="">All Sites</option>{" "}
                    {/*TODO get data from db*/}
                    <option value="ISS1-Teynham">ISS1-Teynham</option>
                    <option value="ISS2-Linton">ISS2-Linton</option>
                    <option value="ISS3-Sittingbourne">
                      ISS3-Sittingbourne
                    </option>
                  </select>
                </div>

                {/* Status Filter */}
                <div>
                  <label className="mr-2">Status:</label>
                  <select
                    value={statusFilter}
                    onChange={(e) => handleStatusFilterChange(e.target.value)}
                    className="border p-1 rounded"
                  >
                    <option value="">All Statuses</option>{" "}
                    {/*TODO get data from db*/}
                    <option value="Pending Review">Pending</option>
                    <option value="Approved">Approved</option>
                    <option value="Rejected">Rejected</option>
                    <option value="Ordered">Ordered</option>
                    <option value="Draft">Draft</option>
                  </select>
                </div>

                {/* Date Filter */}
                <div>
                  <label className="mr-2">Date:</label>
                  <input
                    type="date"
                    value={dateFilter}
                    onChange={(e) => handleDateFilterChange(e.target.value)}
                    className="border p-1 rounded"
                  />
                </div>
                {/* Clear Filters Button */}
                <button
                  onClick={handleClearFilters}
                  className="ml-4 px-3 py-1 border rounded bg-gray-200 hover:bg-gray-300"
                >
                  Clear Filters
                </button>
              </div>
            )}
            <div className="flex flex-row">
              <label className="relative block w-[47vh] text-gray-400  mt-0 py-2">
                <span className="absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black">
                  <FontAwesomeIcon icon={faSearch} className="fw-bold" />
                </span>
                <input
                  type="text"
                  id="filter-text-box"
                  placeholder="Search..."
                  onInput={onFilterTextBoxChanged}
                  value={searchInput}
                  className="block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none shadow-[0_0_5px_rgba(0,0,0,0.1)]"
                />
              </label>
              <button
                className="ml-2 my-2 px-3 py-1 p-[6px] border rounded-md bg-skin-primary text-white text-sm cursor-pointer"
                onClick={() => {
                  if (selectedView === "Orders") {
                    router.push("/ppe-consumable/add");
                  } else {
                    setShowAddProduct(true);
                    setIsEditingProduct(false);
                  }
                }}
              >
                {orderView ? "New Request" : "Add Product"}
              </button>
            </div>
          </div>
          <AddProductDialog
            open={showAddProduct}
            onOpenChange={(_, data) => {
              setShowAddProduct(data.open);
              setIsEditingProduct(false);
            }}
            onSubmit={({ productName, printing, sizes }) => {
              // handle your submit logic here
              console.log({ productName, printing, sizes });
              setShowAddProduct(false);
              router.reload();
            }}
            isEditingProduct={isEditingProduct}
            editProductData={editProductData}
            productTypes={productTypes}
          />
          {/* // Add this before the closing Layout tag */}
          <div className="my-5">
            <div
              className="relative ag-theme-alpine !rounded-md"
              style={{ height: "calc(100vh - 180px)" }}
            >
              <AgGridReact
                rowData={requestRowData}
                ref={gridRef}
                requestColumnDefs={requestColumnDefs}
                defaultColDef={defaultColDef}
                suppressRowClickSelection
                pagination={true}
                paginationPageSize={pageSize}
                onPageSizeChanged={handlePageSizeChange}
                tooltipShowDelay={0}
                tooltipHideDelay={1000}
                onGridReady={handleGridReady}
              />
              <div className="flex justify-start mt-2 pagination-style">
                <label htmlFor="page-size-select pagination" className="inputs">
                  Show{" "}
                  <select
                    id="page-size-select"
                    onChange={handlePageSizeChange}
                    value={pageSize}
                    className="focus:outline-none"
                  >
                    <option value={10}>10</option>
                    <option value={15}>15</option>
                    <option value={25}>25</option>
                    <option value={50}>50</option>
                    <option value={100}>100</option>
                  </select>{" "}
                  Entries
                </label>
              </div>
            </div>
          </div>
        </div>
        <FluentProvider
          theme={webLightTheme}
          className="!bg-transparent"
          style={{ fontFamily: "poppinsregular" }}
        >
          <Dialog
            open={showStatusDialog}
            onOpenChange={(_, data) => {
              if (!data.open) {
                setIsValidCancelReason(true);
                setCancelledReasonapi(""); // Also clear the text
              }
              setShowStatusDialog(data.open);
            }}
          >
            <DialogTrigger disableButtonEnhancement>
              <div style={{ display: "none" }} />
            </DialogTrigger>
            <DialogSurface>
              <DialogBody>
                <DialogTitle>Update Status</DialogTitle>
                <DialogContent>
                  <p className="mb-6">
                    Are you sure you want to{" "}
                    {statusAction === 2
                      ? "approve"
                      : statusAction === 3
                      ? "reject"
                      : statusAction === 4
                      ? "mark as ordered"
                      : "update"}{" "}
                    this request ?
                  </p>
                  {statusAction === 3 && (
                    <textarea
                      className="flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2"
                      rows="8"
                      value={cancelledReasonapi}
                      onChange={(e) => {
                        setCancelledReasonapi(e.target.value);
                        if (e.target.value) {
                          setIsValidCancelReason(true);
                        }
                      }}
                      onBlur={(e) => {
                        const trimmedValue = trimInputText(e.target.value);
                        setCancelledReasonapi(trimmedValue);
                      }}
                      placeholder="Provide reason for rejection..."
                      maxLength="500"
                    ></textarea>
                  )}
                  {statusAction === 3 && !isValidCancelReason && (
                    <span className="text-red-500">
                      Please Provide reason for cancellation
                    </span>
                  )}
                  <div
                    style={{ display: "flex", gap: "8px", marginTop: "16px" }}
                    className="flex justify-end"
                  >
                    <button
                      onClick={() => {setShowStatusDialog(false); setCancelledReasonapi("")}}
                      data-modal-hide="default-modal"
                      type="button"
                      className="border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                    >
                      Cancel
                    </button>
                    <button
                      onClick={() => {
                        // Handle status update logic here
                        console.log(
                          "Updating status:",
                          statusAction,
                          selectedRowData
                        );
                        updateSelectedRowStatus(
                          statusAction,
                          cancelledReasonapi,
                          selectedRowData
                        );
                      }}
                      data-modal-hide="default-modal"
                      type="button"
                      className="text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                    >
                      Continue
                    </button>
                  </div>
                </DialogContent>
              </DialogBody>
            </DialogSurface>
          </Dialog>{" "}
        </FluentProvider>
      </Layout>
    </>
  );
};

export default PPEConsumables;

export const getServerSideProps = async (context) => {
  try {
    const { req, resolvedUrl } = context;
    const sessionId = req.cookies.thl_session;

    if (!sessionId) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    const apiBase =
      process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081";

    const response = await fetch(`${apiBase}/api/auth/me`, {
      method: "GET",
      headers: {
        Cookie: `thl_session=${sessionId}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    const { user } = await response.json();

      //    const allowedRoles = [1, 5, 6];
      // if (!allowedRoles.includes(user.role_id)) {
      //   return {
      //     redirect: {
      //       destination: '/unauthorized',
      //       permanent: false,
      //     },
      //   };
      // }

    return {
      props: {
        userData: user,
      },
    };
  } catch (error) {
    console.error("Error in getServerSideProps:", error);
    return {
      redirect: {
        destination: "/login",
        permanent: false,
      },
    };
  }
};

// Helper function to derive unit from package quantity
function deriveUnitFromPackageQuantity(packageQuantity) {
  if (!packageQuantity) return "Unit";
  const lower = packageQuantity.toLowerCase();
  if (lower.includes("box")) return "Box";
  if (lower.includes("pack")) return "Pack";
  if (lower.includes("pair")) return "Pair";
  return "Unit";
}

SELECT r.[id]
      ,r.[request_id]
      ,r.[user_id]
      ,u.[full_name] AS user_name
      ,r.[site_id]
      ,r.[created_at]
      ,r.[required_date]
      ,r.[is_active]
      ,ori.[id] as request_item_id
      ,ori.[item_number]
      ,ori.[size]
      ,ori.[quantity]
      ,ori.[comments]
      ,ori.[name_for_printing]
      ,pro.[name] as product_name

	  ,ste.[name] as site_name
	  ,sus.[action_id]
	  ,sus.[actioned_by]
	  ,sus.[actioned_by]
	  ,sus.[actioned_by_email]
	  ,sus.[actioned_at] as created_date
	  ,COUNT(ori.id) AS total_items
  FROM [Iss_ppe_consumables_portal].[dbo].[order_requests] r
  left join [Iss_ppe_consumables_portal].[dbo].[order_request_items] ori on r.request_id = ori.request_id and ori.is_active = 1
    left join [Iss_ppe_consumables_portal].[dbo].sites ste on r.site_id = ste.id and ste.is_active = 1
	left join [Iss_ppe_consumables_portal].[dbo].status sus on ori.id = sus.request_item_id and sus.is_active = 1 AND sus.is_latest = 1
  left join [Iss_ppe_consumables_portal].[dbo].[products] pro on ori.product_id = pro.id
  LEFT JOIN [users] u ON r.user_id = u.user_id
   where r.is_active = 1
  group by     
  r.[id],
  r.[request_id],
    r.[user_id],
    r.[site_id],
    r.[created_at],
	   r.[required_date], 
    r.[is_active],      
	ste.[name]
	,sus.[action_id]
	  ,sus.[actioned_by]
	  ,sus.[actioned_by_email]
	  ,sus.[actioned_at]
    ,ori.[size]
    ,ori.[item_number]
      ,ori.[quantity]
      ,ori.[comments]
      ,pro.[name]
      ,ori.[id]
      ,ori.[name_for_printing]
      ,u.[full_name] 
	order by r.request_id desc, ori.item_number asc;
	

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ppe-consumable/add",{

/***/ "./components/PpeConsumable.js":
/*!*************************************!*\
  !*** ./components/PpeConsumable.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fortawesome/free-regular-svg-icons */ \"./node_modules/@fortawesome/free-regular-svg-icons/index.mjs\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lodash/forEach */ \"./node_modules/lodash/forEach.js\");\n/* harmony import */ var lodash_forEach__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(lodash_forEach__WEBPACK_IMPORTED_MODULE_12__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    valueContainer: (provided)=>({\n            ...provided,\n            height: \"28px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: ()=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst emptyItem = {\n    product: null,\n    size: null,\n    quantity: \"\",\n    nameForPrinting: \"\",\n    comments: \"\"\n};\nconst PpeConsumable = (param)=>{\n    let { userData, pageType, sites, OrderRequestData, ppeId = null } = param;\n    var _currentItem_product, _currentItem_product_sizes, _currentItem_product1, _currentItem_product2, _currentItem_product3, _currentItem_product_sizes1, _currentItem_product4, _currentItem_product5;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [nameOfOriginator, setNameOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [emailOfOriginator, setEmailOfOriginator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [siteData, setSiteData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            ...emptyItem\n        }\n    ]);\n    const [requiredDate, setRequiredDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [createdDate, setCreatedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [site, setSite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((sites === null || sites === void 0 ? void 0 : sites[0]) || null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [productsData, setProductsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [currentItem, setCurrentItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        ...emptyItem\n    });\n    const [savedItems, setSavedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // console.log(\"dsksldibiasdbilsdbv\", siteData);\n    // Add validation states for current item\n    // const [productValid, setProductValid] = useState(\"\");\n    // const [quantityValid, setQuantityValid] = useState(\"\");\n    const [dateWarning, setDateWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // const [isEditMode, setIsEditMode] = useState();\n    const [requestStatus, setRequestStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const [requestStatusList, setRequestStatusList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [submitted, setSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popupType, setPopupType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [editingRowId, setEditingRowId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gridApi, setGridApi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const closeModal = (e)=>{\n        if (e) {\n            e.preventDefault();\n        }\n        setIsValidCancelReason(true);\n        setCancelledReasonapi(\"\");\n        setIsOpen(false);\n    };\n    //#region Load data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n        if (OrderRequestData || (OrderRequestData === null || OrderRequestData === void 0 ? void 0 : OrderRequestData.length) > 0) {\n            var _OrderRequestData_, _OrderRequestData_1, _OrderRequestData_2, _OrderRequestData_3;\n            // console.log(\"OrderRequestData\", OrderRequestData);\n            if ((_OrderRequestData_ = OrderRequestData[0]) === null || _OrderRequestData_ === void 0 ? void 0 : _OrderRequestData_.action_id) {\n                var _OrderRequestData_4, _OrderRequestData_5;\n                setRequestStatus((_OrderRequestData_4 = OrderRequestData[0]) === null || _OrderRequestData_4 === void 0 ? void 0 : _OrderRequestData_4.action_id);\n                if (((_OrderRequestData_5 = OrderRequestData[0]) === null || _OrderRequestData_5 === void 0 ? void 0 : _OrderRequestData_5.action_id) !== 5) {\n                    setSubmitted(true);\n                }\n            }\n            if ((_OrderRequestData_1 = OrderRequestData[0]) === null || _OrderRequestData_1 === void 0 ? void 0 : _OrderRequestData_1.site_id) {\n                var _OrderRequestData_6, _OrderRequestData_7;\n                setSite({\n                    label: (_OrderRequestData_6 = OrderRequestData[0]) === null || _OrderRequestData_6 === void 0 ? void 0 : _OrderRequestData_6.site_name,\n                    value: (_OrderRequestData_7 = OrderRequestData[0]) === null || _OrderRequestData_7 === void 0 ? void 0 : _OrderRequestData_7.site_id\n                });\n            }\n            if ((_OrderRequestData_2 = OrderRequestData[0]) === null || _OrderRequestData_2 === void 0 ? void 0 : _OrderRequestData_2.required_date) {\n                var _OrderRequestData_8, _OrderRequestData_9;\n                setRequiredDate(((_OrderRequestData_8 = OrderRequestData[0]) === null || _OrderRequestData_8 === void 0 ? void 0 : _OrderRequestData_8.required_date) ? new Date((_OrderRequestData_9 = OrderRequestData[0]) === null || _OrderRequestData_9 === void 0 ? void 0 : _OrderRequestData_9.required_date).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if ((_OrderRequestData_3 = OrderRequestData[0]) === null || _OrderRequestData_3 === void 0 ? void 0 : _OrderRequestData_3.created_at) {\n                var _OrderRequestData_10, _OrderRequestData_11;\n                setCreatedDate(((_OrderRequestData_10 = OrderRequestData[0]) === null || _OrderRequestData_10 === void 0 ? void 0 : _OrderRequestData_10.created_at) ? new Date((_OrderRequestData_11 = OrderRequestData[0]) === null || _OrderRequestData_11 === void 0 ? void 0 : _OrderRequestData_11.created_at).toLocaleDateString(\"en-CA\", {\n                    year: \"numeric\",\n                    month: \"2-digit\",\n                    day: \"2-digit\"\n                }) : \"\");\n            }\n            if (OrderRequestData[0].user_name) {\n                // setNameOfOriginator();\n                setNameOfOriginator(OrderRequestData[0].user_name);\n            }\n            if (OrderRequestData[0].actioned_by_email) {\n                // setNameOfOriginator();\n                setEmailOfOriginator(OrderRequestData[0].actioned_by_email);\n            }\n            // setrowdata\n            if (OrderRequestData && Array.isArray(OrderRequestData)) {\n                console.log(\"load data\", OrderRequestData, requestStatus);\n                const STATUS_MAP = {\n                    1: \"Pending Review\",\n                    2: \"Approved\",\n                    3: \"Rejected\",\n                    4: \"Ordered\",\n                    5: \"Draft\"\n                };\n                const mappedItems = OrderRequestData.map((item)=>({\n                        id: item.order_request_items,\n                        product: {\n                            value: item.product_id,\n                            label: item.product_name\n                        },\n                        size: {\n                            value: item.size,\n                            label: item.size || \"N/A\"\n                        },\n                        quantity: item.quantity,\n                        nameForPrinting: item.name_for_printing,\n                        comments: (item.action_id === 3 ? \"Rejected Reason: \".concat(item.latest_comment) : item.comments) || \"\",\n                        updateFlag: 0,\n                        status: STATUS_MAP[item.action_id]\n                    }));\n                const uniqueValues = [\n                    ...new Set(OrderRequestData.map((item)=>item.action_id))\n                ];\n                setRequestStatusList(uniqueValues);\n                setSavedItems(mappedItems);\n                console.log(\"uniqueValues\", uniqueValues);\n            }\n        }\n    }, [\n        OrderRequestData\n    ]);\n    //#endregion Load data\n    //#region ag-grid\n    const onRowSelected = (event)=>{\n        if (event.node.isSelected()) {\n            setEditingRowId(event.data.id);\n            setCurrentItem(event.data);\n        }\n    };\n    const onGridReady = (params1)=>{\n        setGridApi(params1.api);\n    };\n    // inside your component\n    const getRowClass = (params1)=>{\n        return params1.data.id === editingRowId ? \"bg-blue-100\" : \"\";\n    };\n    const IconsRenderer = (props)=>{\n        const handleDelete = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n        };\n        const handleEdit = (event)=>{\n            event.preventDefault();\n            const selectedRow = props.data;\n            setHasUnsavedChanges(true);\n            setEditingRowId(selectedRow.id);\n            // setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            const updatedData = savedItems.filter((item)=>item !== selectedRow);\n            setSavedItems(updatedData);\n            const handleRowUpdate = (actionId)=>()=>{\n                    setSelectedRowData(params.data);\n                    setStatusAction(actionId);\n                    setShowStatusDialog(true);\n                };\n            // event.preventDefault();\n            // const selectedRow = props.data;\n            // setEditingRowId(selectedRow.id || props.node.id);\n            // // const updatedData = savedItems.filter((item) => item !== selectedRow);\n            // // setSavedItems(updatedData);\n            //   setCurrentItem(JSON.parse(JSON.stringify(selectedRow)));\n            // Populate the form with the selected row data\n            setCurrentItem({\n                id: selectedRow.id,\n                product: selectedRow.product,\n                size: selectedRow.size,\n                quantity: selectedRow.quantity,\n                nameForPrinting: selectedRow.nameForPrinting,\n                nameForPrintingFlag: selectedRow.nameForPrinting,\n                comments: selectedRow.comments,\n                updateFlag: 1\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-row gap-4 justify-center text-skin-primary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleEdit,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faPenToSquare\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDelete,\n                    className: \"\".concat(requestStatus !== 5 || !requestStatus ? \"hidden\" : \"\", \" text-red-500\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faTrash\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        onClick: ()=>{\n                            if (!validate()) return;\n                            setPopupType(\"approve\");\n                            setIsOpen(true);\n                        },\n                        // onClick={handleRowUpdate(4)}\n                        icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faCircleCheck,\n                        className: \"\".concat(requestStatus !== 1 || !requestStatus ? \"hidden\" : \"\", \" text-green-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        // onClick={handleRowUpdate(4)}\n                        onClick: ()=>{\n                            if (!validate()) return;\n                            setPopupType(\"approve\");\n                            setIsOpen(true);\n                        },\n                        icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmarkCircle,\n                        className: \"\".concat(requestStatus !== 1 || !requestStatus ? \"hidden\" : \"\", \" text-red-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                        // onClick={handleRowUpdate(4)}\n                        onClick: ()=>{\n                            if (!validate()) return;\n                            setPopupType(\"approve\");\n                            setIsOpen(true);\n                        },\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faSquareCheck,\n                        className: \"\".concat(requestStatus !== 2 || !requestStatus ? \"hidden\" : \"\", \" text-green-500\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Column definitions for the grid\n    const columnDefs = [\n        {\n            headerName: \"Product\",\n            field: \"product.label\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Size\",\n            field: \"size.label\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Quantity\",\n            field: \"quantity\",\n            flex: 1,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Name for Printing\",\n            field: \"nameForPrinting\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Comments\",\n            field: \"comments\",\n            flex: 2,\n            headerClass: \"header-with-border\"\n        },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            hide: requestStatus === 5 || !requestStatus,\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: 1.25,\n            filterParams: {\n                values: [\n                    \"Pending Review\",\n                    \"Approved\",\n                    \"Rejected\",\n                    \"Ordered\",\n                    \"Draft\"\n                ]\n            }\n        },\n        {\n            headerName: \"Actions\",\n            cellRenderer: IconsRenderer,\n            headerClass: \"header-with-border\",\n            flex: 1\n        }\n    ];\n    //#endregion ag-grid\n    //#region api's\n    function getAllProducts() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/all-products\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        }).catch((error)=>{\n            console.error(error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to fetch products: \".concat(error.message));\n            throw error;\n        });\n    }\n    function getAllSites() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"ppe-consumables/sites\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                return;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch products\");\n        });\n    }\n    //#endregion api's\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const siteData = await getAllSites();\n                const formattedSites = siteData === null || siteData === void 0 ? void 0 : siteData.map((site)=>({\n                        value: site.id,\n                        label: site.name\n                    }));\n                setSiteData(formattedSites);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error fetching Sites\", error);\n            }\n        };\n        const fetchProducts = async ()=>{\n            try {\n                const productsJson = await getAllProducts();\n                const filterOutHiddenProducts = productsJson.filter((product)=>!product.IsProductHidden);\n                const formattedProducts = filterOutHiddenProducts === null || filterOutHiddenProducts === void 0 ? void 0 : filterOutHiddenProducts.map((product)=>({\n                        value: product.ProductId,\n                        label: product.ProductName,\n                        size_required: product.IsSizeRequired || product.size_required,\n                        sizes: product.AvailableSizes ? product.AvailableSizes.split(\", \").map((size)=>({\n                                value: size.toLowerCase().replace(/\\s+/g, \"\"),\n                                label: size\n                            })) : [],\n                        productCode: product.ProductCode,\n                        packageQuantity: product.PackageQuantity,\n                        productType: product.ProductType,\n                        name_printable: product.name_printable\n                    }));\n                setProductsData(formattedProducts);\n            } catch (error) {\n                console.error(\"Error fetching products:\", error);\n            }\n        };\n        fetchProducts();\n        fetchSites();\n        setLoading(false);\n    }, []);\n    const updateSelectedRowStatusForSingleItem = (updatedStatusID, params1)=>{\n        if (updatedStatusID == 3) {\n            if (cancelledReasonapi) {\n                setIsValidCancelReason(true);\n            } else {\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        console.log(\"sssssssssssssssssssssssssssssssssssss\", params1);\n        const apiPayload = {\n            request_no: params1.id,\n            item_number: params1.item_number,\n            ProductName: params1.product_name,\n            Size: params1.size,\n            Quantity: params1.quantity,\n            NameForPrinting: params1.NameForPrinting,\n            Comments: updatedStatusID == 3 ? cancelledReasonapi : params1.comment,\n            RequestItemID: params1.request_item_id,\n            commentOnUpdatingRequest: cancelledReasonapi,\n            action_id: updatedStatusID,\n            SubmitterUserID: userData.user_id,\n            SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n            orignatorEmail: params1 === null || params1 === void 0 ? void 0 : params1.orignatorEmail,\n            cancelledReasonapi: cancelledReasonapi\n        };\n        try {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-product-request-items/\").concat(params1.id), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n                getData().then((data)=>{\n                    const grouped = data === null || data === void 0 ? void 0 : data.reduce((acc, row)=>{\n                        if (!acc[row.request_id]) {\n                            acc[row.request_id] = [];\n                        }\n                        acc[row.request_id].push(row);\n                        return acc;\n                    }, {});\n                    const STATUS_MAP = {\n                        1: \"Pending Review\",\n                        2: \"Approved\",\n                        3: \"Rejected\",\n                        4: \"Ordered\",\n                        5: \"Draft\"\n                    };\n                    const counters = {};\n                    const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                        const siblings = grouped[row.request_id];\n                        const index = siblings.findIndex((r)=>r.request_item_id === row.request_item_id) + 1;\n                        counters[row.request_id] = (counters[row.request_id] || 0) + 1;\n                        return {\n                            id: row.id,\n                            request_id: \"\".concat(row.request_id, \" - \").concat(row.item_number),\n                            item_number: row.item_number,\n                            required_date: new Date(row.required_date).toISOString().split(\"T\")[0],\n                            site_name: row.site_name,\n                            product_name: row.product_name || \"Unnamed Product\",\n                            requestor: row.user_name,\n                            orignatorEmail: row.actioned_by_email,\n                            comment: (row.action_id === 3 ? \"Rejected Reason: \".concat(row.latest_comment) : row.comments) || \"\",\n                            size: row.size,\n                            quantity: row.quantity,\n                            NameForPrinting: row.name_for_printing,\n                            status: STATUS_MAP[row.action_id],\n                            request_item_id: row.request_item_id\n                        };\n                    });\n                    setRequestRowData(formattedData);\n                    setCancelledReasonapi(\"\");\n                // closeModal();\n                // setShowStatusDialog(false);\n                });\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    //#region Validate\n    // Update the validate function to use savedItems instead of items\n    const validate = ()=>{\n        const newErrors = {};\n        if (!site) newErrors.site = \"Site is required.\";\n        if (!requiredDate) newErrors.requiredDate = \"Required date is missing.\";\n        if (!savedItems || savedItems.length === 0) {\n            newErrors.savedItems = \"At least one item must be added.\";\n        }\n        // Update state for field-level messages\n        setErrors(newErrors);\n        const hasErrors = Object.keys(newErrors).length > 0;\n        if (hasErrors) {\n            const errorMessages = Object.values(newErrors);\n            // setPopupConfig({\n            //   title: \"Validation Error\",\n            //   message: errorMessages.join(\"\\n\"),\n            //   type: \"error\",\n            // });\n            // setShowPopup(true); // trigger popup\n            const errorsLength = Object.keys(newErrors).length;\n            if (errorsLength > 1) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill all required fields.\");\n            } else if (errorsLength == 1 && newErrors.savedItems) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please add at least one item.\");\n            }\n            return false;\n        }\n        return true;\n    };\n    const handleSiteAdd = (value)=>{\n        setSite(value);\n        // Clear the error for site if value is valid\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (value) {\n                delete updated.site;\n            }\n            return updated;\n        });\n    };\n    // Add date validation function\n    const validateRequiredDate = (selectedDate)=>{\n        // setRequiredDate(selectedDate);\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (selectedDate) {\n                delete updated.requiredDate;\n            }\n            return updated;\n        });\n        if (!selectedDate) {\n            setDateWarning(\"\");\n            return;\n        }\n        const today = new Date();\n        const selected = new Date(selectedDate);\n        const twoWeeksFromNow = new Date();\n        twoWeeksFromNow.setDate(today.getDate() + 14);\n        if (selected < twoWeeksFromNow) {\n            setDateWarning(\"Notice: Less than 2 weeks lead time may affect availability.\");\n        } else {\n            setDateWarning(\"\");\n        }\n    };\n    // #region handlers\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    const handleCancelReason = (data)=>{\n        if (data) {\n            setIsValidCancelReason(true);\n        } else {\n            setIsValidCancelReason(false);\n        }\n    };\n    const handleSaveClick = (e)=>{\n        // e.preventDefault();\n        if (hasUnsavedChanges) {\n            setPopupType(\"unsavedWarning\");\n            setIsOpen(true); // your existing modal state\n        } else {\n            saveCurrentItem(e);\n        }\n    };\n    const saveCurrentItem = (e)=>{\n        var _currentItem_product, _currentItem_product_sizes, _currentItem_product1;\n        console.log(\"save order request\");\n        setErrors((prev)=>{\n            const updated = {\n                ...prev\n            };\n            if (e) {\n                delete updated.savedItems;\n            }\n            return updated;\n        });\n        if (!e) {\n            console.log(\"returning here\");\n            setDateWarning(\"\");\n            return;\n        }\n        e.preventDefault();\n        let count = 0;\n        if (!currentItem.product) {\n            console.log(\"++ product\");\n            count++;\n        // setProductValid(\"Please select a product\");\n        } else {\n        // setProductValid(\"\");\n        }\n        if (!currentItem.quantity || currentItem.quantity < 1) {\n            console.log(\"++ quantity\");\n            count++;\n        // setQuantityValid(\"Please enter a valid quantity\");\n        } else {\n            if (currentItem.quantity > 1 && currentItem.nameForPrinting) {\n                // currentItem.nameForPrinting=\"\"\n                setCurrentItem((prev)=>({\n                        ...prev,\n                        nameForPrinting: \"\"\n                    }));\n            }\n        // setQuantityValid(\"\");\n        }\n        if (((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) && ((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) && !currentItem.size) {\n            console.log(\"++ product size\");\n            count++;\n        // Add size validation if needed\n        }\n        if (count > 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please fill in all mandatory fields to add items with valid quantity\");\n            return;\n        }\n        //     if (editingRowId) {\n        //         // Update existing item\n        //   const updatedItems = savedItems.map(item =>\n        //     item.id === editingRowId ? { ...currentItem, id: editingRowId } : item\n        //   );\n        //   setSavedItems(updatedItems);\n        //   setEditingRowId(null);\n        // } else {\n        //   // Add new item\n        //   setSavedItems([...savedItems, { ...currentItem, id: Date.now() }]);\n        // }\n        if (editingRowId) {\n            // Add the edited item back to savedItems\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: editingRowId,\n                    product: currentItem.product ? {\n                        ...currentItem.product\n                    } : null,\n                    size: currentItem.size ? {\n                        ...currentItem.size\n                    } : null\n                }\n            ]);\n            setEditingRowId(null);\n        } else {\n            // Add new item\n            setSavedItems([\n                ...savedItems,\n                {\n                    ...currentItem,\n                    id: \"row-\".concat(Date.now())\n                }\n            ]);\n        }\n        // Reset current item\n        setCurrentItem({\n            ...emptyItem\n        });\n        setEditingRowId(null);\n        setHasUnsavedChanges(false);\n    };\n    // Handle current item changes\n    const handleCurrentItemChange = (field, value)=>{\n        if (field === \"product\") {\n            // console.log(\"field-------------\",value)\n            // Reset other fields when product changes\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    size: null,\n                    quantity: \"\",\n                    nameForPrinting: \"\",\n                    nameForPrintingFlag: \"\",\n                    comments: \"\"\n                }));\n        } else if (field === \"quantity\" && value > 1) {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value,\n                    nameForPrinting: \"\"\n                }));\n        } else {\n            setCurrentItem((prev)=>({\n                    ...prev,\n                    [field]: value\n                }));\n        }\n    };\n    //#region Save\n    const handleSubmit = (status_ID)=>{\n        if (status_ID != 1 && !validate()) {\n            return;\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssssssssssssssssssss\",savedItems);\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        // Transform savedItems to API format\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size;\n            return {\n                requestItemId: item.requestItemId,\n                // RequestItemID: typeof item.id === 'string' && item.id.startsWith('row-') ? null : item.id,\n                ProductID: (_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value,\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            Status_id: status_ID,\n            SubmitterUserID: userData === null || userData === void 0 ? void 0 : userData.user_id,\n            TargetSiteID: (site === null || site === void 0 ? void 0 : site.value) || 3,\n            RequiredDate: requiredDate,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            items: items,\n            orignatorEmail: emailOfOriginator\n        };\n        // console.log(\"items--------------------------------------------------------\",items)\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/create-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n    };\n    //#region updated conditional\n    const updateSelectedRowStatus = (updatedStatusID, existingStatusId)=>{\n        const STATUS_MAP = {\n            1: \"Pending Review\",\n            2: \"Approved\",\n            3: \"Rejected\",\n            4: \"Ordered\",\n            5: \"Draft\"\n        };\n        const toBeUpdated = savedItems.filter((item)=>item.status == STATUS_MAP[existingStatusId]);\n        if (toBeUpdated.length === 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"No items found with the specified status\");\n            return;\n        }\n        setLoading(true);\n        if (updatedStatusID == 3) {\n            if (cancelledReasonapi) {\n                setIsValidCancelReason(true);\n                console.log(\"triggerrerererered\", cancelledReasonapi);\n            } else {\n                console.log(\"triggerrerererered\");\n                setIsValidCancelReason(false);\n                return;\n            }\n        }\n        // console.log(\"sssssssssssssssssssssssssssssssssssss\", toBeUpdated);\n        const updatePromises = toBeUpdated.map(async (item)=>{\n            const apiPayload = {\n                request_no: item.id,\n                item_number: item.item_number,\n                ProductName: item.product.label,\n                Size: item.size.label,\n                Quantity: item.quantity,\n                NameForPrinting: item.nameForPrinting,\n                Comments: updatedStatusID == 3 ? cancelledReasonapi : item.comments,\n                RequestItemID: item.id,\n                commentOnUpdatingRequest: cancelledReasonapi,\n                action_id: updatedStatusID,\n                SubmitterUserID: userData.user_id,\n                SubmitterEmail: userData === null || userData === void 0 ? void 0 : userData.email,\n                orignatorEmail: emailOfOriginator,\n                cancelledReasonapi: cancelledReasonapi\n            };\n            console.log(\"apiPayload\", apiPayload); // Log for debugging purposes.\n            const res = await fetch(\"\".concat(_services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress, \"ppe-consumables/update-product-request-items/\").concat(item.id), {\n                method: \"POST\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            });\n            if (res.status === 200) {\n                return res.json();\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    router.push(redirectUrl);\n                }, 3000);\n                throw new Error(\"Unauthorized\");\n            } else {\n                const errorText = await res.text();\n                console.error(\"Update failed:\", errorText);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to update item status.\");\n                throw new Error(\"Update failed\");\n            }\n        });\n        Promise.all(updatePromises).then((results)=>{\n            const successCount = results.filter((result)=>result === null || result === void 0 ? void 0 : result.msg).length;\n            if (successCount > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Successfully updated \".concat(successCount, \" item(s)\"), {\n                    position: \"top-right\"\n                });\n                setTimeout(()=>{\n                    router.replace(\"/ppe-consumable\");\n                }, 2000);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"No items were successfully updated\");\n                router.push(\"/ppe-consumable\");\n            }\n        }).catch((error)=>{\n            console.error(\"Error updating items:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Some items failed to update\");\n        }).finally(()=>{\n            setLoading(false);\n        });\n    };\n    //#endregion updated conditional\n    // #region update\n    const handleUpdate = (action_id)=>{\n        if (!validate()) {\n            return;\n        }\n        // handleSaveClick();\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_5__.apiConfig.serverAddress;\n        console.log(\"savedItems\", savedItems);\n        const items = savedItems.map((item)=>{\n            var _item_product, _item_product1, _item_product2, _item_size, _item_size1;\n            return {\n                // RequestItemID: item.id,\n                RequestItemID: typeof item.id === \"string\" && item.id.startsWith(\"row-\") ? null : item.id,\n                ProductID: item.product_id || ((_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.value),\n                ProductName: ((_item_product1 = item.product) === null || _item_product1 === void 0 ? void 0 : _item_product1.label) || ((_item_product2 = item.product) === null || _item_product2 === void 0 ? void 0 : _item_product2.value),\n                Size: ((_item_size = item.size) === null || _item_size === void 0 ? void 0 : _item_size.label) || null,\n                // SizeID: item.size?.value || null, //TODO no size handel\n                SizeID: ((_item_size1 = item.size) === null || _item_size1 === void 0 ? void 0 : _item_size1.value) && !isNaN(Number(item.size.value)) ? Number(item.size.value) : null,\n                Quantity: Number(item.quantity) || 0,\n                NameForPrinting: item.nameForPrinting || \"\",\n                Comments: item.comments || \"\"\n            };\n        });\n        const apiPayload = {\n            requestId: ppeId,\n            action_id: action_id,\n            submitterUserId: userData.user_id,\n            username: (userData === null || userData === void 0 ? void 0 : userData.email) || \"system\",\n            targetSiteId: site.value,\n            requiredDate: requiredDate,\n            items: items,\n            comment: cancelledReasonapi,\n            orignatorEmail: emailOfOriginator\n        };\n        console.log(\"apiPayload--------------------------------------------------------\", apiPayload);\n        try {\n            fetch(\"\".concat(serverAddress, \"ppe-consumables/update-order-request\"), {\n                method: \"Post\",\n                headers: {\n                    \"content-type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(apiPayload)\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to save product.\");\n                    setLoading(false);\n                }\n            }).then((json)=>{\n                if (json && json.msg) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(json.msg, {\n                        position: \"top-right\"\n                    });\n                    setTimeout(()=>{\n                        router.replace(\"/ppe-consumable\");\n                    }, 2000);\n                }\n            });\n        } catch (error) {\n            console.log(\"errorin submitting the order\", error);\n        }\n        setLoading(false);\n        setTimeout(()=>{\n            setLoading(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Request submitted!\");\n            router.push(\"/ppe-consumable\");\n        }, 1000);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTimeout(function() {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"rawWarning\", true, {\n                expires: 365\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"finishWarning\");\n        }, 2000);\n        if (pageType == \"update\") {\n            setIsEdit(true);\n        }\n        if (pageType == \"add\" && userData) {\n            setNameOfOriginator(userData.name);\n        }\n    }, [\n        0\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"flex flex-col justify-start max-w-full  mx-auto mr-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row gap-8 bg-white p-6 rounded-lg shadow-[0_0_1px_rgba(0,0,0,0.1)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1143,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: nameOfOriginator || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1144,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1142,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1152,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: emailOfOriginator || (userData === null || userData === void 0 ? void 0 : userData.email) || \"\",\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1153,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1151,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: \"Created Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1161,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: new Date().toLocaleDateString(\"en-CA\"),\n                                            disabled: true,\n                                            className: \"block w-full px-4 text-gray-500 border rounded-lg form-input\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1162,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1160,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Site\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500 ml-[2px]\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1171,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1170,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            value: site,\n                                            onChange: (e)=>{\n                                                setSite();\n                                                handleSiteAdd(e);\n                                            },\n                                            options: siteData,\n                                            isDisabled: (sites === null || sites === void 0 ? void 0 : sites.length) === 1 || submitted,\n                                            styles: customSelectStyles,\n                                            isClearable: true,\n                                            className: \" w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1173,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        errors.site && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.site\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1185,\n                                            columnNumber: 31\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1169,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"labels mb-2\",\n                                            children: [\n                                                \"Required Date \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1189,\n                                                    columnNumber: 31\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1188,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            min: new Date().toISOString().split(\"T\")[0],\n                                            value: requiredDate,\n                                            onChange: (e)=>{\n                                                setRequiredDate(e.target.value);\n                                                validateRequiredDate(e.target.value);\n                                            },\n                                            className: \"block w-full px-4 border rounded-lg form-input\",\n                                            disabled: submitted\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1191,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        dateWarning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-orange-500 text-sm\",\n                                            children: dateWarning\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1203,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.requiredDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#ef4444\"\n                                            },\n                                            children: errors.requiredDate\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1206,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1187,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1141,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1135,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row justify-between items-end my-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"flex mb-1 font-semibold tracking-wider text-base pl-2\",\n                                    children: \"Items\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1226,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1225,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col mb-28px bg-white p-6 rounded-lg py-8 shadow-[0_0_2px_rgba(0,0,0,0.2)]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Product \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1235,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1234,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: currentItem.product,\n                                                        onChange: (val)=>handleCurrentItemChange(\"product\", val),\n                                                        options: productsData,\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1237,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1233,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Size\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 \".concat(!((_currentItem_product = currentItem.product) === null || _currentItem_product === void 0 ? void 0 : _currentItem_product.size_required) || !((_currentItem_product1 = currentItem.product) === null || _currentItem_product1 === void 0 ? void 0 : (_currentItem_product_sizes = _currentItem_product1.sizes) === null || _currentItem_product_sizes === void 0 ? void 0 : _currentItem_product_sizes.length) || submitted ? \"hidden\" : \"\"),\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1251,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1249,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: currentItem.size,\n                                                        onChange: (val)=>handleCurrentItemChange(\"size\", val),\n                                                        options: ((_currentItem_product2 = currentItem.product) === null || _currentItem_product2 === void 0 ? void 0 : _currentItem_product2.sizes) || [],\n                                                        styles: customSelectStyles,\n                                                        className: \"reactSelectCustom w-full\",\n                                                        isSearchable: true,\n                                                        isClearable: true,\n                                                        isDisabled: !((_currentItem_product3 = currentItem.product) === null || _currentItem_product3 === void 0 ? void 0 : _currentItem_product3.size_required) || !((_currentItem_product4 = currentItem.product) === null || _currentItem_product4 === void 0 ? void 0 : (_currentItem_product_sizes1 = _currentItem_product4.sizes) === null || _currentItem_product_sizes1 === void 0 ? void 0 : _currentItem_product_sizes1.length) || submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1263,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1248,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: [\n                                                            \"Quantity \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                lineNumber: 1280,\n                                                                columnNumber: 28\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1279,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        // min={0}\n                                                        max: 999,\n                                                        value: currentItem.quantity,\n                                                        onChange: (e)=>handleCurrentItemChange(\"quantity\", e.target.value > 999 ? 999 : parseInt(e.target.value)),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1282,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1278,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Name for Printing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1298,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        maxLength: \"50\" //TODO change in db also\n                                                        ,\n                                                        value: currentItem.nameForPrinting,\n                                                        onChange: (e)=>handleCurrentItemChange(\"nameForPrinting\", e.target.value),\n                                                        disabled: currentItem.quantity !== 1 || submitted || !((_currentItem_product5 = currentItem.product) === null || _currentItem_product5 === void 0 ? void 0 : _currentItem_product5.name_printable),\n                                                        className: \"block w-full px-4 border rounded-lg form-input\",\n                                                        placeholder: \"Only if quantity = 1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1299,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1297,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"labels mb-2\",\n                                                        children: \"Comments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1316,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        value: currentItem.comments,\n                                                        maxLength: \"500\",\n                                                        onChange: (e)=>handleCurrentItemChange(\"comments\", e.target.value),\n                                                        className: \"disabled:text-gray-400 disabled:bg-[#F6F3F3] block w-full h-8 px-4 border rounded-lg form-input resize-none\",\n                                                        rows: 1,\n                                                        disabled: submitted\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1317,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1315,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    onClick: saveCurrentItem,\n                                                    // onClick={handleSaveClick}\n                                                    className: \"px-2 py-1 2xl:px-3.5 border border-skin-primary rounded-md text-skin-primary cursor-pointer \".concat(submitted ? \"hidden\" : \"\"),\n                                                    disabled: submitted,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faFloppyDisk\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1338,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1330,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1328,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1232,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ag-theme-alpine my-[24px]\",\n                                        style: {\n                                            height: 250,\n                                            width: \"100%\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_7__.AgGridReact, {\n                                            ref: gridRef,\n                                            columnDefs: columnDefs,\n                                            rowData: savedItems,\n                                            rowHeight: 35,\n                                            getRowClass: getRowClass,\n                                            onGridReady: onGridReady\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1349,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1345,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    popupType === \"unsavedWarning\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"One item is being edited. If you continue, changes will be lost.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1367,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: closeModal,\n                                                        className: \"border px-4 py-2 rounded\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1372,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            saveCurrentItem(); // discard editing row\n                                                            closeModal();\n                                                        },\n                                                        className: \"bg-blue-600 text-white px-4 py-2 rounded\",\n                                                        children: \"Save Anyway\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                        lineNumber: 1378,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                lineNumber: 1371,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1366,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1231,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1224,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end gap-4 rounded-lg  p-4 mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    router.push(\"/ppe-consumable\");\n                                },\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6\",\n                                disabled: loading,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1394,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (pageType === \"edit\") {\n                                        console.log(\"hasUnsavedChanges\", hasUnsavedChanges);\n                                        if (hasUnsavedChanges) {\n                                            setPopupType(\"Save\");\n                                            setIsOpen(true);\n                                        } else {\n                                            handleUpdate(5);\n                                        }\n                                    } else {\n                                        handleSubmit(5);\n                                    }\n                                },\n                                disabled: loading || requestStatus === 1,\n                                className: \"border border-skin-primary text-skin-primary rounded-md px-6 \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1404,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (hasUnsavedChanges) {\n                                        setPopupType(\"submitWhileEditing\");\n                                        setIsOpen(true);\n                                    } else {\n                                        if (!validate()) return;\n                                        setPopupType(\"submit\");\n                                        setIsOpen(true);\n                                    }\n                                },\n                                className: \"border border-skin-primary bg-skin-primary text-white rounded-md px-6 font-medium \".concat(requestStatus !== 5 ? \"hidden\" : \"\"),\n                                disabled: loading || requestStatus === 1,\n                                children: \"Submit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1426,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"reject\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-red-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Reject All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1445,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"approve\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(!requestStatusList.includes(1) ? \"hidden\" : \"\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Approve All Pending Requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1464,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                // onClick={() => {\n                                //   updateSelectedRowStatus(4, 2);\n                                // }}\n                                onClick: ()=>{\n                                    if (!validate()) return;\n                                    setPopupType(\"markOrdered\");\n                                    setIsOpen(true);\n                                },\n                                className: \"bg-green-600 text-white rounded-md px-6 font-medium \".concat(requestStatusList.includes(2) ? \"\" : \"hidden\", \"  \").concat(userData.role_id === 6 || userData.role_id === 1 || userData.role_id === 5 ? \"\" : \"hidden\"),\n                                children: \"Mark All Approved Requests to Ordered\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1483,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                        lineNumber: 1393,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1131,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition, {\n                appear: true,\n                show: isOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: (value)=>{\n                        if (popupType !== \"reject\" || isValidCancelReason) {\n                            closeModal();\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1531,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1522,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                            lineNumber: 1552,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1551,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Confirm \".concat(popupType == \"submit\" ? \"Submission\" : popupType == \"reject\" ? \"Rejection\" : popupType == \"approve\" ? \"Approval\" : popupType == \"markOrdered\" ? \"markOrdered\" : popupType == \"Save\" ? \"Save\" : popupType == \"submitWhileEditing\" ? \"Submission\" : \" \")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1550,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeModal,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_6__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1576,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1570,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1549,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                popupType == \"submit\" ? \"submit\" : popupType == \"rejectSingleItem\" ? \"reject\" : popupType == \"approveSingleItem\" ? \"approve\" : popupType == \"markSingleItemOrdered\" ? \"mark approved items as ordered\" : popupType == \"reject\" ? \"reject\" : popupType == \"approve\" ? \"approve\" : popupType == \"markOrdered\" ? \"mark approved items as ordered\" : popupType == \"Save\" ? \"save? there is a item being edited,\\n it will be lost if you Save \" : popupType == \"submitWhileEditing\" ? \"Submit? there is a item being edited,\\n it will be lost if you Submit \" : \" \",\n                                                                \" \",\n                                                                \"this request?\",\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1584,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        popupType == \"reject\" || popupType == \"rejectSingleItem\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                    className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                                    rows: \"8\",\n                                                                    value: cancelledReasonapi,\n                                                                    onChange: (e)=>{\n                                                                        setCancelledReasonapi(e.target.value);\n                                                                        if (e.target.value) {\n                                                                            setIsValidCancelReason(true);\n                                                                        }\n                                                                    },\n                                                                    onBlur: (e)=>{\n                                                                        const trimmedValue = trimInputText(e.target.value);\n                                                                        setCancelledReasonapi(trimmedValue);\n                                                                    },\n                                                                    placeholder: \"Provide reason for rejection...\",\n                                                                    maxLength: \"500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1610,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                (popupType == \"reject\" || popupType == \"rejectSingleItem\") && !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"Please Provide reason for cancellation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                                    lineNumber: 1630,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1608,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1583,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                closeModal(), setCancelledReasonapi(\"\");\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1639,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                if (popupType === \"reject\" && !(cancelledReasonapi === null || cancelledReasonapi === void 0 ? void 0 : cancelledReasonapi.trim())) {\n                                                                    setIsValidCancelReason(false);\n                                                                    return;\n                                                                }\n                                                                if (pageType == \"add\") {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleSubmit(1);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleSubmit(1);\n                                                                    }\n                                                                } else {\n                                                                    if (popupType == \"submit\") {\n                                                                        handleUpdate(1);\n                                                                    } else if (popupType === \"reject\") {\n                                                                        updateSelectedRowStatus(3, 1);\n                                                                    } else if (popupType === \"approve\") {\n                                                                        updateSelectedRowStatus(2, 1);\n                                                                    } else if (popupType === \"rejectSingleItem\") {\n                                                                        updateSelectedRowStatusForSingleItem(3);\n                                                                    } else if (popupType === \"approveSingleItem\") {\n                                                                        updateSelectedRowStatusForSingleItem(2);\n                                                                    } else if (popupType === \"Save\") {\n                                                                        handleUpdate(5);\n                                                                    } else if (popupType === \"markOrdered\") {\n                                                                        updateSelectedRowStatus(4, 2);\n                                                                    } else if (popupType === \"markSingleItemOrdered\") {\n                                                                        updateSelectedRowStatusForSingleItem(4, 2);\n                                                                    } else if (popupType === \"submitWhileEditing\") {\n                                                                        handleUpdate(1);\n                                                                    }\n                                                                }\n                                                                setCancelledReasonapi(\"\");\n                                                                closeModal();\n                                                            },\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center h-full border border-skin-primary\",\n                                                            children: \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                            lineNumber: 1649,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                                    lineNumber: 1638,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                            lineNumber: 1547,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                        lineNumber: 1545,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                    lineNumber: 1536,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                                lineNumber: 1535,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                            lineNumber: 1534,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                    lineNumber: 1513,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\thl-portal\\\\thlwebapp\\\\components\\\\PpeConsumable.js\",\n                lineNumber: 1512,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(PpeConsumable, \"O68D4V6/DUUfJI1W3US3XF3lslU=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = PpeConsumable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PpeConsumable);\nvar _c;\n$RefreshReg$(_c, \"PpeConsumable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/PpeConsumable.js\n"));

/***/ })

});